@props([
    'paginate' => null,
    'class' => '',
])

@php
$classes = 'w-full border-collapse bg-white dark:bg-zinc-900 ' . $class;
@endphp

<div class="overflow-x-auto">
    <table {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }}
    </table>
    
    @if($paginate && method_exists($paginate, 'links'))
        <div class="px-3 py-4 border-t border-zinc-200 dark:border-white/20">
            {{ $paginate->links() }}
        </div>
    @endif
</div>
