<?php

declare(strict_types=1);

namespace App\Livewire;

use Flux\Flux;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\WithPagination;
use Illuminate\Contracts\View\View;
use Spatie\Permission\Models\Permission;
use Illuminate\Contracts\Database\Eloquent\Builder;

class PermissionTable extends Component
{
    use WithPagination;

    public string $search = '';

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    public function delete(int $permission_id): void
    {
        $permission = Permission::find($permission_id);

        Flux::toast(
            variant: 'success',
            text: "Successfully deleted the {$permission?->name} permission",
        );

        Flux::modals()->close();

        $permission?->delete();
    }

    #[On('permission-saved')]
    public function render(): View
    {
        return view('livewire.permission-table', [
            'permissions' => Permission::query()
                ->withCount('roles', 'users')
                ->when(strlen($this->search) >= 1, function (Builder $query): void {
                    $query->where('name', 'like', "%{$this->search}%");
                })
                ->orderBy('name')
                ->paginate(15),
        ]);
    }
}
