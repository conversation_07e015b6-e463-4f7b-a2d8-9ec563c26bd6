{"__meta": {"id": "01JW3BM9FPY226X7XC5G14R5SD", "datetime": "2025-05-25 09:15:52", "utime": **********.182981, "method": "GET", "uri": "/planned-spending", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.262718, "end": **********.182995, "duration": 0.9202771186828613, "duration_str": "920ms", "measures": [{"label": "Booting", "start": **********.262718, "relative_start": 0, "end": **********.560919, "relative_end": **********.560919, "duration": 0.****************, "duration_str": "298ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.560933, "relative_start": 0.****************, "end": **********.182997, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "622ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.586147, "relative_start": 0.****************, "end": **********.591191, "relative_end": **********.591191, "duration": 0.005043983459472656, "duration_str": "5.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.planned-spending", "start": **********.699858, "relative_start": 0.*****************, "end": **********.699858, "relative_end": **********.699858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.701831, "relative_start": 0.*****************, "end": **********.701831, "relative_end": **********.701831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.70439, "relative_start": 0.44167208671569824, "end": **********.70439, "relative_end": **********.70439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.plus", "start": **********.705282, "relative_start": 0.4425640106201172, "end": **********.705282, "relative_end": **********.705282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.706353, "relative_start": 0.4436349868774414, "end": **********.706353, "relative_end": **********.706353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.707566, "relative_start": 0.44484806060791016, "end": **********.707566, "relative_end": **********.707566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.trigger", "start": **********.708228, "relative_start": 0.4455101490020752, "end": **********.708228, "relative_end": **********.708228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.planned-spending-form", "start": **********.726493, "relative_start": 0.4637749195098877, "end": **********.726493, "relative_end": **********.726493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.728216, "relative_start": 0.4654979705810547, "end": **********.728216, "relative_end": **********.728216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.729235, "relative_start": 0.46651697158813477, "end": **********.729235, "relative_end": **********.729235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.729876, "relative_start": 0.46715807914733887, "end": **********.729876, "relative_end": **********.729876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.731989, "relative_start": 0.4692709445953369, "end": **********.731989, "relative_end": **********.731989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.73312, "relative_start": 0.4704020023345947, "end": **********.73312, "relative_end": **********.73312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.73373, "relative_start": 0.4710121154785156, "end": **********.73373, "relative_end": **********.73373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.categories", "start": **********.734306, "relative_start": 0.471588134765625, "end": **********.734306, "relative_end": **********.734306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.735523, "relative_start": 0.4728050231933594, "end": **********.735523, "relative_end": **********.735523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.search", "start": **********.736247, "relative_start": 0.4735291004180908, "end": **********.736247, "relative_end": **********.736247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.magnifying-glass", "start": **********.737551, "relative_start": 0.47483301162719727, "end": **********.737551, "relative_end": **********.737551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.741466, "relative_start": 0.478748083114624, "end": **********.741466, "relative_end": **********.741466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.742089, "relative_start": 0.4793710708618164, "end": **********.742089, "relative_end": **********.742089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.743273, "relative_start": 0.48055505752563477, "end": **********.743273, "relative_end": **********.743273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.743813, "relative_start": 0.4810950756072998, "end": **********.743813, "relative_end": **********.743813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.plus", "start": **********.744349, "relative_start": 0.4816310405731201, "end": **********.744349, "relative_end": **********.744349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.7448, "relative_start": 0.4820821285247803, "end": **********.7448, "relative_end": **********.7448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.745974, "relative_start": 0.48325610160827637, "end": **********.745974, "relative_end": **********.745974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.746505, "relative_start": 0.48378705978393555, "end": **********.746505, "relative_end": **********.746505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.trigger", "start": **********.746983, "relative_start": 0.4842650890350342, "end": **********.746983, "relative_end": **********.746983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.747472, "relative_start": 0.4847540855407715, "end": **********.747472, "relative_end": **********.747472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.748282, "relative_start": 0.4855639934539795, "end": **********.748282, "relative_end": **********.748282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.749057, "relative_start": 0.48633909225463867, "end": **********.749057, "relative_end": **********.749057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.749689, "relative_start": 0.4869711399078369, "end": **********.749689, "relative_end": **********.749689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.750176, "relative_start": 0.4874579906463623, "end": **********.750176, "relative_end": **********.750176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.750911, "relative_start": 0.4881930351257324, "end": **********.750911, "relative_end": **********.750911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.751775, "relative_start": 0.4890570640563965, "end": **********.751775, "relative_end": **********.751775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.752734, "relative_start": 0.49001598358154297, "end": **********.752734, "relative_end": **********.752734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.753387, "relative_start": 0.49066901206970215, "end": **********.753387, "relative_end": **********.753387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.754043, "relative_start": 0.49132513999938965, "end": **********.754043, "relative_end": **********.754043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.754535, "relative_start": 0.49181699752807617, "end": **********.754535, "relative_end": **********.754535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.754903, "relative_start": 0.492185115814209, "end": **********.754903, "relative_end": **********.754903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.75556, "relative_start": 0.4928419589996338, "end": **********.75556, "relative_end": **********.75556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.756472, "relative_start": 0.49375414848327637, "end": **********.756472, "relative_end": **********.756472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.757808, "relative_start": 0.4950900077819824, "end": **********.757808, "relative_end": **********.757808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.758481, "relative_start": 0.49576306343078613, "end": **********.758481, "relative_end": **********.758481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.759109, "relative_start": 0.49639105796813965, "end": **********.759109, "relative_end": **********.759109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.759573, "relative_start": 0.4968550205230713, "end": **********.759573, "relative_end": **********.759573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.759905, "relative_start": 0.49718713760375977, "end": **********.759905, "relative_end": **********.759905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.760521, "relative_start": 0.4978029727935791, "end": **********.760521, "relative_end": **********.760521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.760955, "relative_start": 0.49823713302612305, "end": **********.760955, "relative_end": **********.760955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.761674, "relative_start": 0.49895596504211426, "end": **********.761674, "relative_end": **********.761674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.762196, "relative_start": 0.4994781017303467, "end": **********.762196, "relative_end": **********.762196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.76279, "relative_start": 0.5000720024108887, "end": **********.76279, "relative_end": **********.76279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.763242, "relative_start": 0.5005240440368652, "end": **********.763242, "relative_end": **********.763242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.763562, "relative_start": 0.5008440017700195, "end": **********.763562, "relative_end": **********.763562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.763983, "relative_start": 0.5012650489807129, "end": **********.763983, "relative_end": **********.763983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.764408, "relative_start": 0.501690149307251, "end": **********.764408, "relative_end": **********.764408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.765167, "relative_start": 0.5024490356445312, "end": **********.765167, "relative_end": **********.765167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.765742, "relative_start": 0.5030241012573242, "end": **********.765742, "relative_end": **********.765742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.766535, "relative_start": 0.503817081451416, "end": **********.766535, "relative_end": **********.766535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.767468, "relative_start": 0.5047500133514404, "end": **********.767468, "relative_end": **********.767468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.767948, "relative_start": 0.5052299499511719, "end": **********.767948, "relative_end": **********.767948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.768548, "relative_start": 0.5058300495147705, "end": **********.768548, "relative_end": **********.768548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.769224, "relative_start": 0.5065059661865234, "end": **********.769224, "relative_end": **********.769224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.770082, "relative_start": 0.50736403465271, "end": **********.770082, "relative_end": **********.770082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.770695, "relative_start": 0.5079770088195801, "end": **********.770695, "relative_end": **********.770695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.771336, "relative_start": 0.5086181163787842, "end": **********.771336, "relative_end": **********.771336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.771802, "relative_start": 0.5090839862823486, "end": **********.771802, "relative_end": **********.771802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.772438, "relative_start": 0.5097200870513916, "end": **********.772438, "relative_end": **********.772438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.773436, "relative_start": 0.5107181072235107, "end": **********.773436, "relative_end": **********.773436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.774194, "relative_start": 0.5114760398864746, "end": **********.774194, "relative_end": **********.774194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.775051, "relative_start": 0.5123331546783447, "end": **********.775051, "relative_end": **********.775051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.775696, "relative_start": 0.5129780769348145, "end": **********.775696, "relative_end": **********.775696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.776342, "relative_start": 0.5136239528656006, "end": **********.776342, "relative_end": **********.776342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.77684, "relative_start": 0.5141220092773438, "end": **********.77684, "relative_end": **********.77684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.777235, "relative_start": 0.514517068862915, "end": **********.777235, "relative_end": **********.777235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.777736, "relative_start": 0.5150179862976074, "end": **********.777736, "relative_end": **********.777736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.778229, "relative_start": 0.5155110359191895, "end": **********.778229, "relative_end": **********.778229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.779062, "relative_start": 0.5163440704345703, "end": **********.779062, "relative_end": **********.779062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.779716, "relative_start": 0.5169980525970459, "end": **********.779716, "relative_end": **********.779716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.780395, "relative_start": 0.5176770687103271, "end": **********.780395, "relative_end": **********.780395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.780889, "relative_start": 0.5181710720062256, "end": **********.780889, "relative_end": **********.780889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.781388, "relative_start": 0.5186700820922852, "end": **********.781388, "relative_end": **********.781388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.782132, "relative_start": 0.519413948059082, "end": **********.782132, "relative_end": **********.782132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.782672, "relative_start": 0.5199539661407471, "end": **********.782672, "relative_end": **********.782672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.783508, "relative_start": 0.5207901000976562, "end": **********.783508, "relative_end": **********.783508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.784138, "relative_start": 0.5214200019836426, "end": **********.784138, "relative_end": **********.784138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.78478, "relative_start": 0.5220620632171631, "end": **********.78478, "relative_end": **********.78478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.785342, "relative_start": 0.5226240158081055, "end": **********.785342, "relative_end": **********.785342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.785735, "relative_start": 0.5230169296264648, "end": **********.785735, "relative_end": **********.785735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.786241, "relative_start": 0.5235230922698975, "end": **********.786241, "relative_end": **********.786241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.786736, "relative_start": 0.5240180492401123, "end": **********.786736, "relative_end": **********.786736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.78758, "relative_start": 0.5248620510101318, "end": **********.78758, "relative_end": **********.78758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.788218, "relative_start": 0.5255000591278076, "end": **********.788218, "relative_end": **********.788218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.789178, "relative_start": 0.5264599323272705, "end": **********.789178, "relative_end": **********.789178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.790297, "relative_start": 0.5275790691375732, "end": **********.790297, "relative_end": **********.790297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.790892, "relative_start": 0.5281739234924316, "end": **********.790892, "relative_end": **********.790892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.791457, "relative_start": 0.5287389755249023, "end": **********.791457, "relative_end": **********.791457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.791984, "relative_start": 0.5292661190032959, "end": **********.791984, "relative_end": **********.791984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.792851, "relative_start": 0.5301330089569092, "end": **********.792851, "relative_end": **********.792851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.793517, "relative_start": 0.530799150466919, "end": **********.793517, "relative_end": **********.793517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.794224, "relative_start": 0.5315060615539551, "end": **********.794224, "relative_end": **********.794224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.794771, "relative_start": 0.5320529937744141, "end": **********.794771, "relative_end": **********.794771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.79516, "relative_start": 0.5324420928955078, "end": **********.79516, "relative_end": **********.79516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.795667, "relative_start": 0.5329489707946777, "end": **********.795667, "relative_end": **********.795667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.796204, "relative_start": 0.5334861278533936, "end": **********.796204, "relative_end": **********.796204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.797409, "relative_start": 0.5346910953521729, "end": **********.797409, "relative_end": **********.797409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.798162, "relative_start": 0.5354440212249756, "end": **********.798162, "relative_end": **********.798162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.798881, "relative_start": 0.5361630916595459, "end": **********.798881, "relative_end": **********.798881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.799405, "relative_start": 0.5366871356964111, "end": **********.799405, "relative_end": **********.799405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.799809, "relative_start": 0.5370910167694092, "end": **********.799809, "relative_end": **********.799809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.800302, "relative_start": 0.5375840663909912, "end": **********.800302, "relative_end": **********.800302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.800821, "relative_start": 0.5381031036376953, "end": **********.800821, "relative_end": **********.800821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.801646, "relative_start": 0.5389280319213867, "end": **********.801646, "relative_end": **********.801646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.802274, "relative_start": 0.5395560264587402, "end": **********.802274, "relative_end": **********.802274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.802961, "relative_start": 0.5402431488037109, "end": **********.802961, "relative_end": **********.802961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.803478, "relative_start": 0.5407600402832031, "end": **********.803478, "relative_end": **********.803478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.80388, "relative_start": 0.5411620140075684, "end": **********.80388, "relative_end": **********.80388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.804365, "relative_start": 0.5416469573974609, "end": **********.804365, "relative_end": **********.804365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.804908, "relative_start": 0.5421900749206543, "end": **********.804908, "relative_end": **********.804908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.806301, "relative_start": 0.5435831546783447, "end": **********.806301, "relative_end": **********.806301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.807483, "relative_start": 0.5447649955749512, "end": **********.807483, "relative_end": **********.807483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.808298, "relative_start": 0.5455801486968994, "end": **********.808298, "relative_end": **********.808298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.808875, "relative_start": 0.5461571216583252, "end": **********.808875, "relative_end": **********.808875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.809276, "relative_start": 0.546558141708374, "end": **********.809276, "relative_end": **********.809276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.809788, "relative_start": 0.5470700263977051, "end": **********.809788, "relative_end": **********.809788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.810313, "relative_start": 0.5475950241088867, "end": **********.810313, "relative_end": **********.810313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.811192, "relative_start": 0.5484740734100342, "end": **********.811192, "relative_end": **********.811192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.811862, "relative_start": 0.5491440296173096, "end": **********.811862, "relative_end": **********.811862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.812539, "relative_start": 0.549821138381958, "end": **********.812539, "relative_end": **********.812539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.81341, "relative_start": 0.550692081451416, "end": **********.81341, "relative_end": **********.81341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.81388, "relative_start": 0.5511620044708252, "end": **********.81388, "relative_end": **********.81388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.814408, "relative_start": 0.5516901016235352, "end": **********.814408, "relative_end": **********.814408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.814956, "relative_start": 0.5522379875183105, "end": **********.814956, "relative_end": **********.814956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.815851, "relative_start": 0.5531330108642578, "end": **********.815851, "relative_end": **********.815851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.816495, "relative_start": 0.5537769794464111, "end": **********.816495, "relative_end": **********.816495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.817195, "relative_start": 0.5544769763946533, "end": **********.817195, "relative_end": **********.817195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.817732, "relative_start": 0.5550141334533691, "end": **********.817732, "relative_end": **********.817732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.818134, "relative_start": 0.5554161071777344, "end": **********.818134, "relative_end": **********.818134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.818629, "relative_start": 0.5559110641479492, "end": **********.818629, "relative_end": **********.818629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.819136, "relative_start": 0.5564179420471191, "end": **********.819136, "relative_end": **********.819136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.820014, "relative_start": 0.5572960376739502, "end": **********.820014, "relative_end": **********.820014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.820666, "relative_start": 0.557948112487793, "end": **********.820666, "relative_end": **********.820666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.821389, "relative_start": 0.5586709976196289, "end": **********.821389, "relative_end": **********.821389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.821934, "relative_start": 0.5592160224914551, "end": **********.821934, "relative_end": **********.821934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.822787, "relative_start": 0.5600690841674805, "end": **********.822787, "relative_end": **********.822787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.82384, "relative_start": 0.561121940612793, "end": **********.82384, "relative_end": **********.82384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.824499, "relative_start": 0.5617809295654297, "end": **********.824499, "relative_end": **********.824499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.825457, "relative_start": 0.5627391338348389, "end": **********.825457, "relative_end": **********.825457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.826137, "relative_start": 0.5634191036224365, "end": **********.826137, "relative_end": **********.826137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.826842, "relative_start": 0.5641241073608398, "end": **********.826842, "relative_end": **********.826842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.827378, "relative_start": 0.5646600723266602, "end": **********.827378, "relative_end": **********.827378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.827789, "relative_start": 0.5650711059570312, "end": **********.827789, "relative_end": **********.827789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.828284, "relative_start": 0.5655660629272461, "end": **********.828284, "relative_end": **********.828284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.828904, "relative_start": 0.5661859512329102, "end": **********.828904, "relative_end": **********.828904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.830099, "relative_start": 0.5673811435699463, "end": **********.830099, "relative_end": **********.830099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.830817, "relative_start": 0.5680990219116211, "end": **********.830817, "relative_end": **********.830817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.831523, "relative_start": 0.5688049793243408, "end": **********.831523, "relative_end": **********.831523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.832061, "relative_start": 0.569343090057373, "end": **********.832061, "relative_end": **********.832061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.832453, "relative_start": 0.569735050201416, "end": **********.832453, "relative_end": **********.832453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.832958, "relative_start": 0.5702400207519531, "end": **********.832958, "relative_end": **********.832958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.833592, "relative_start": 0.5708739757537842, "end": **********.833592, "relative_end": **********.833592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.83487, "relative_start": 0.5721521377563477, "end": **********.83487, "relative_end": **********.83487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.835685, "relative_start": 0.5729670524597168, "end": **********.835685, "relative_end": **********.835685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.836422, "relative_start": 0.5737040042877197, "end": **********.836422, "relative_end": **********.836422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.836932, "relative_start": 0.574213981628418, "end": **********.836932, "relative_end": **********.836932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.837266, "relative_start": 0.5745480060577393, "end": **********.837266, "relative_end": **********.837266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.837682, "relative_start": 0.5749640464782715, "end": **********.837682, "relative_end": **********.837682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.838152, "relative_start": 0.5754339694976807, "end": **********.838152, "relative_end": **********.838152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.839315, "relative_start": 0.5765969753265381, "end": **********.839315, "relative_end": **********.839315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.840465, "relative_start": 0.577747106552124, "end": **********.840465, "relative_end": **********.840465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.841282, "relative_start": 0.578563928604126, "end": **********.841282, "relative_end": **********.841282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.841805, "relative_start": 0.5790870189666748, "end": **********.841805, "relative_end": **********.841805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.842199, "relative_start": 0.5794811248779297, "end": **********.842199, "relative_end": **********.842199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.842684, "relative_start": 0.5799660682678223, "end": **********.842684, "relative_end": **********.842684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.843155, "relative_start": 0.5804369449615479, "end": **********.843155, "relative_end": **********.843155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.844239, "relative_start": 0.5815210342407227, "end": **********.844239, "relative_end": **********.844239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.845002, "relative_start": 0.5822839736938477, "end": **********.845002, "relative_end": **********.845002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.845688, "relative_start": 0.582970142364502, "end": **********.845688, "relative_end": **********.845688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.846222, "relative_start": 0.5835039615631104, "end": **********.846222, "relative_end": **********.846222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.846613, "relative_start": 0.5838949680328369, "end": **********.846613, "relative_end": **********.846613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.847122, "relative_start": 0.5844039916992188, "end": **********.847122, "relative_end": **********.847122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.847626, "relative_start": 0.5849080085754395, "end": **********.847626, "relative_end": **********.847626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.848478, "relative_start": 0.5857601165771484, "end": **********.848478, "relative_end": **********.848478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.849137, "relative_start": 0.5864191055297852, "end": **********.849137, "relative_end": **********.849137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.849825, "relative_start": 0.5871069431304932, "end": **********.849825, "relative_end": **********.849825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.850353, "relative_start": 0.5876350402832031, "end": **********.850353, "relative_end": **********.850353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.850729, "relative_start": 0.5880110263824463, "end": **********.850729, "relative_end": **********.850729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.851222, "relative_start": 0.5885040760040283, "end": **********.851222, "relative_end": **********.851222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.851692, "relative_start": 0.5889739990234375, "end": **********.851692, "relative_end": **********.851692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.852555, "relative_start": 0.5898370742797852, "end": **********.852555, "relative_end": **********.852555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.853215, "relative_start": 0.5904970169067383, "end": **********.853215, "relative_end": **********.853215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.853891, "relative_start": 0.5911729335784912, "end": **********.853891, "relative_end": **********.853891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.854406, "relative_start": 0.5916881561279297, "end": **********.854406, "relative_end": **********.854406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.854804, "relative_start": 0.5920860767364502, "end": **********.854804, "relative_end": **********.854804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.855317, "relative_start": 0.5925991535186768, "end": **********.855317, "relative_end": **********.855317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.856431, "relative_start": 0.5937130451202393, "end": **********.856431, "relative_end": **********.856431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.857702, "relative_start": 0.5949840545654297, "end": **********.857702, "relative_end": **********.857702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.858458, "relative_start": 0.5957400798797607, "end": **********.858458, "relative_end": **********.858458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.859448, "relative_start": 0.5967299938201904, "end": **********.859448, "relative_end": **********.859448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.860076, "relative_start": 0.597357988357544, "end": **********.860076, "relative_end": **********.860076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.860467, "relative_start": 0.5977489948272705, "end": **********.860467, "relative_end": **********.860467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.860962, "relative_start": 0.5982439517974854, "end": **********.860962, "relative_end": **********.860962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.861471, "relative_start": 0.5987529754638672, "end": **********.861471, "relative_end": **********.861471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.862315, "relative_start": 0.5995969772338867, "end": **********.862315, "relative_end": **********.862315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.862973, "relative_start": 0.600255012512207, "end": **********.862973, "relative_end": **********.862973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.863634, "relative_start": 0.6009161472320557, "end": **********.863634, "relative_end": **********.863634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.864152, "relative_start": 0.6014339923858643, "end": **********.864152, "relative_end": **********.864152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.864529, "relative_start": 0.6018109321594238, "end": **********.864529, "relative_end": **********.864529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.86505, "relative_start": 0.6023321151733398, "end": **********.86505, "relative_end": **********.86505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.865538, "relative_start": 0.6028199195861816, "end": **********.865538, "relative_end": **********.865538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.866377, "relative_start": 0.6036591529846191, "end": **********.866377, "relative_end": **********.866377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.867048, "relative_start": 0.6043300628662109, "end": **********.867048, "relative_end": **********.867048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.867708, "relative_start": 0.6049900054931641, "end": **********.867708, "relative_end": **********.867708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.868233, "relative_start": 0.6055150032043457, "end": **********.868233, "relative_end": **********.868233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.868613, "relative_start": 0.6058950424194336, "end": **********.868613, "relative_end": **********.868613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.869111, "relative_start": 0.6063930988311768, "end": **********.869111, "relative_end": **********.869111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.869587, "relative_start": 0.6068689823150635, "end": **********.869587, "relative_end": **********.869587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.870468, "relative_start": 0.6077499389648438, "end": **********.870468, "relative_end": **********.870468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.871133, "relative_start": 0.6084151268005371, "end": **********.871133, "relative_end": **********.871133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.871825, "relative_start": 0.6091070175170898, "end": **********.871825, "relative_end": **********.871825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.872818, "relative_start": 0.6101000308990479, "end": **********.872818, "relative_end": **********.872818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.873576, "relative_start": 0.6108579635620117, "end": **********.873576, "relative_end": **********.873576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.874244, "relative_start": 0.6115260124206543, "end": **********.874244, "relative_end": **********.874244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.874772, "relative_start": 0.6120541095733643, "end": **********.874772, "relative_end": **********.874772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.876062, "relative_start": 0.6133439540863037, "end": **********.876062, "relative_end": **********.876062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.876738, "relative_start": 0.6140201091766357, "end": **********.876738, "relative_end": **********.876738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.877459, "relative_start": 0.6147410869598389, "end": **********.877459, "relative_end": **********.877459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.877998, "relative_start": 0.6152801513671875, "end": **********.877998, "relative_end": **********.877998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.878384, "relative_start": 0.6156661510467529, "end": **********.878384, "relative_end": **********.878384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.87887, "relative_start": 0.6161520481109619, "end": **********.87887, "relative_end": **********.87887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.87936, "relative_start": 0.6166419982910156, "end": **********.87936, "relative_end": **********.87936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.880223, "relative_start": 0.6175050735473633, "end": **********.880223, "relative_end": **********.880223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.880875, "relative_start": 0.618157148361206, "end": **********.880875, "relative_end": **********.880875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.881568, "relative_start": 0.6188499927520752, "end": **********.881568, "relative_end": **********.881568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.882097, "relative_start": 0.6193790435791016, "end": **********.882097, "relative_end": **********.882097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.882482, "relative_start": 0.6197640895843506, "end": **********.882482, "relative_end": **********.882482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.882995, "relative_start": 0.620276927947998, "end": **********.882995, "relative_end": **********.882995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.883485, "relative_start": 0.6207671165466309, "end": **********.883485, "relative_end": **********.883485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.index", "start": **********.884324, "relative_start": 0.6216061115264893, "end": **********.884324, "relative_end": **********.884324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.variants.listbox", "start": **********.885083, "relative_start": 0.6223649978637695, "end": **********.885083, "relative_end": **********.885083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.button", "start": **********.886006, "relative_start": 0.6232881546020508, "end": **********.886006, "relative_end": **********.886006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.selected", "start": **********.887009, "relative_start": 0.624290943145752, "end": **********.887009, "relative_end": **********.887009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.887803, "relative_start": 0.6250851154327393, "end": **********.887803, "relative_end": **********.887803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.888275, "relative_start": 0.6255569458007812, "end": **********.888275, "relative_end": **********.888275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.890724, "relative_start": 0.6280059814453125, "end": **********.890724, "relative_end": **********.890724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.891649, "relative_start": 0.6289310455322266, "end": **********.891649, "relative_end": **********.891649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-down", "start": **********.892609, "relative_start": 0.6298909187316895, "end": **********.892609, "relative_end": **********.892609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.options", "start": **********.89335, "relative_start": 0.6306319236755371, "end": **********.89335, "relative_end": **********.89335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.empty", "start": **********.894277, "relative_start": 0.6315591335296631, "end": **********.894277, "relative_end": **********.894277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.894822, "relative_start": 0.6321039199829102, "end": **********.894822, "relative_end": **********.894822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.895682, "relative_start": 0.6329641342163086, "end": **********.895682, "relative_end": **********.895682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.category-form", "start": **********.898136, "relative_start": 0.6354179382324219, "end": **********.898136, "relative_end": **********.898136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.899576, "relative_start": 0.6368579864501953, "end": **********.899576, "relative_end": **********.899576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.900152, "relative_start": 0.6374340057373047, "end": **********.900152, "relative_end": **********.900152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.900648, "relative_start": 0.637930154800415, "end": **********.900648, "relative_end": **********.900648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.9024, "relative_start": 0.6396820545196533, "end": **********.9024, "relative_end": **********.9024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.903234, "relative_start": 0.6405160427093506, "end": **********.903234, "relative_end": **********.903234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.903809, "relative_start": 0.6410911083221436, "end": **********.903809, "relative_end": **********.903809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.904373, "relative_start": 0.6416549682617188, "end": **********.904373, "relative_end": **********.904373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.909419, "relative_start": 0.6467010974884033, "end": **********.909419, "relative_end": **********.909419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.910144, "relative_start": 0.6474261283874512, "end": **********.910144, "relative_end": **********.910144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.910867, "relative_start": 0.6481490135192871, "end": **********.910867, "relative_end": **********.910867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.911393, "relative_start": 0.6486749649047852, "end": **********.911393, "relative_end": **********.911393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.911773, "relative_start": 0.649055004119873, "end": **********.911773, "relative_end": **********.911773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.912304, "relative_start": 0.6495859622955322, "end": **********.912304, "relative_end": **********.912304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.912838, "relative_start": 0.6501200199127197, "end": **********.912838, "relative_end": **********.912838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.913715, "relative_start": 0.6509969234466553, "end": **********.913715, "relative_end": **********.913715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.914357, "relative_start": 0.6516389846801758, "end": **********.914357, "relative_end": **********.914357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.915055, "relative_start": 0.6523370742797852, "end": **********.915055, "relative_end": **********.915055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.915595, "relative_start": 0.6528770923614502, "end": **********.915595, "relative_end": **********.915595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.916002, "relative_start": 0.6532840728759766, "end": **********.916002, "relative_end": **********.916002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.916492, "relative_start": 0.6537740230560303, "end": **********.916492, "relative_end": **********.916492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.91699, "relative_start": 0.6542720794677734, "end": **********.91699, "relative_end": **********.91699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.917852, "relative_start": 0.6551339626312256, "end": **********.917852, "relative_end": **********.917852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.918518, "relative_start": 0.6558001041412354, "end": **********.918518, "relative_end": **********.918518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.919203, "relative_start": 0.6564850807189941, "end": **********.919203, "relative_end": **********.919203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.91976, "relative_start": 0.6570420265197754, "end": **********.91976, "relative_end": **********.91976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.920144, "relative_start": 0.657426118850708, "end": **********.920144, "relative_end": **********.920144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.920637, "relative_start": 0.6579189300537109, "end": **********.920637, "relative_end": **********.920637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.921119, "relative_start": 0.6584010124206543, "end": **********.921119, "relative_end": **********.921119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.922213, "relative_start": 0.6594951152801514, "end": **********.922213, "relative_end": **********.922213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.923485, "relative_start": 0.6607670783996582, "end": **********.923485, "relative_end": **********.923485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.924702, "relative_start": 0.6619839668273926, "end": **********.924702, "relative_end": **********.924702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.925316, "relative_start": 0.6625981330871582, "end": **********.925316, "relative_end": **********.925316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.925686, "relative_start": 0.6629679203033447, "end": **********.925686, "relative_end": **********.925686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.926144, "relative_start": 0.6634259223937988, "end": **********.926144, "relative_end": **********.926144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.9266, "relative_start": 0.6638820171356201, "end": **********.9266, "relative_end": **********.9266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.927441, "relative_start": 0.6647229194641113, "end": **********.927441, "relative_end": **********.927441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.928043, "relative_start": 0.6653249263763428, "end": **********.928043, "relative_end": **********.928043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.928622, "relative_start": 0.6659040451049805, "end": **********.928622, "relative_end": **********.928622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.929063, "relative_start": 0.6663451194763184, "end": **********.929063, "relative_end": **********.929063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.929373, "relative_start": 0.6666550636291504, "end": **********.929373, "relative_end": **********.929373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.929775, "relative_start": 0.6670570373535156, "end": **********.929775, "relative_end": **********.929775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.930194, "relative_start": 0.6674759387969971, "end": **********.930194, "relative_end": **********.930194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.930924, "relative_start": 0.668205976486206, "end": **********.930924, "relative_end": **********.930924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.931462, "relative_start": 0.6687440872192383, "end": **********.931462, "relative_end": **********.931462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.932021, "relative_start": 0.6693029403686523, "end": **********.932021, "relative_end": **********.932021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.932441, "relative_start": 0.6697230339050293, "end": **********.932441, "relative_end": **********.932441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.932752, "relative_start": 0.6700339317321777, "end": **********.932752, "relative_end": **********.932752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.933166, "relative_start": 0.6704480648040771, "end": **********.933166, "relative_end": **********.933166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.933584, "relative_start": 0.6708660125732422, "end": **********.933584, "relative_end": **********.933584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.934322, "relative_start": 0.6716041564941406, "end": **********.934322, "relative_end": **********.934322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.934847, "relative_start": 0.6721291542053223, "end": **********.934847, "relative_end": **********.934847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.935457, "relative_start": 0.6727390289306641, "end": **********.935457, "relative_end": **********.935457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.9359, "relative_start": 0.6731820106506348, "end": **********.9359, "relative_end": **********.9359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.936245, "relative_start": 0.6735270023345947, "end": **********.936245, "relative_end": **********.936245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.936667, "relative_start": 0.6739490032196045, "end": **********.936667, "relative_end": **********.936667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.937113, "relative_start": 0.6743950843811035, "end": **********.937113, "relative_end": **********.937113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.937862, "relative_start": 0.6751439571380615, "end": **********.937862, "relative_end": **********.937862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.938552, "relative_start": 0.6758339405059814, "end": **********.938552, "relative_end": **********.938552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.939944, "relative_start": 0.6772260665893555, "end": **********.939944, "relative_end": **********.939944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.940836, "relative_start": 0.6781179904937744, "end": **********.940836, "relative_end": **********.940836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.941222, "relative_start": 0.6785039901733398, "end": **********.941222, "relative_end": **********.941222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.941686, "relative_start": 0.6789679527282715, "end": **********.941686, "relative_end": **********.941686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.94219, "relative_start": 0.6794719696044922, "end": **********.94219, "relative_end": **********.94219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.942965, "relative_start": 0.6802470684051514, "end": **********.942965, "relative_end": **********.942965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.943725, "relative_start": 0.6810071468353271, "end": **********.943725, "relative_end": **********.943725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.944337, "relative_start": 0.6816189289093018, "end": **********.944337, "relative_end": **********.944337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.944785, "relative_start": 0.6820671558380127, "end": **********.944785, "relative_end": **********.944785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.945127, "relative_start": 0.6824090480804443, "end": **********.945127, "relative_end": **********.945127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.945543, "relative_start": 0.6828250885009766, "end": **********.945543, "relative_end": **********.945543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.945979, "relative_start": 0.6832611560821533, "end": **********.945979, "relative_end": **********.945979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.946754, "relative_start": 0.6840360164642334, "end": **********.946754, "relative_end": **********.946754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.947348, "relative_start": 0.6846301555633545, "end": **********.947348, "relative_end": **********.947348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.947969, "relative_start": 0.685250997543335, "end": **********.947969, "relative_end": **********.947969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.948417, "relative_start": 0.6856989860534668, "end": **********.948417, "relative_end": **********.948417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.948749, "relative_start": 0.6860311031341553, "end": **********.948749, "relative_end": **********.948749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.94918, "relative_start": 0.6864619255065918, "end": **********.94918, "relative_end": **********.94918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.949634, "relative_start": 0.6869161128997803, "end": **********.949634, "relative_end": **********.949634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.index", "start": **********.950375, "relative_start": 0.6876571178436279, "end": **********.950375, "relative_end": **********.950375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.variants.listbox", "start": **********.950946, "relative_start": 0.6882281303405762, "end": **********.950946, "relative_end": **********.950946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.button", "start": **********.951589, "relative_start": 0.6888711452484131, "end": **********.951589, "relative_end": **********.951589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.selected", "start": **********.952329, "relative_start": 0.6896109580993652, "end": **********.952329, "relative_end": **********.952329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.952786, "relative_start": 0.6900680065155029, "end": **********.952786, "relative_end": **********.952786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.953207, "relative_start": 0.6904890537261963, "end": **********.953207, "relative_end": **********.953207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.954715, "relative_start": 0.6919970512390137, "end": **********.954715, "relative_end": **********.954715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.955298, "relative_start": 0.692579984664917, "end": **********.955298, "relative_end": **********.955298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-down", "start": **********.956304, "relative_start": 0.6935861110687256, "end": **********.956304, "relative_end": **********.956304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.options", "start": **********.957192, "relative_start": 0.6944739818572998, "end": **********.957192, "relative_end": **********.957192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.957906, "relative_start": 0.695188045501709, "end": **********.957906, "relative_end": **********.957906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.958701, "relative_start": 0.6959829330444336, "end": **********.958701, "relative_end": **********.958701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.959191, "relative_start": 0.6964731216430664, "end": **********.959191, "relative_end": **********.959191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.959897, "relative_start": 0.6971790790557861, "end": **********.959897, "relative_end": **********.959897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.960356, "relative_start": 0.6976380348205566, "end": **********.960356, "relative_end": **********.960356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.961465, "relative_start": 0.698746919631958, "end": **********.961465, "relative_end": **********.961465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.96197, "relative_start": 0.6992521286010742, "end": **********.96197, "relative_end": **********.96197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.962461, "relative_start": 0.6997430324554443, "end": **********.962461, "relative_end": **********.962461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.962839, "relative_start": 0.7001209259033203, "end": **********.962839, "relative_end": **********.962839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.964047, "relative_start": 0.7013289928436279, "end": **********.964047, "relative_end": **********.964047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.964786, "relative_start": 0.7020680904388428, "end": **********.964786, "relative_end": **********.964786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.965424, "relative_start": 0.7027060985565186, "end": **********.965424, "relative_end": **********.965424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.965958, "relative_start": 0.703240156173706, "end": **********.965958, "relative_end": **********.965958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.966479, "relative_start": 0.703761100769043, "end": **********.966479, "relative_end": **********.966479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": **********.966947, "relative_start": 0.7042291164398193, "end": **********.966947, "relative_end": **********.966947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.968138, "relative_start": 0.7054200172424316, "end": **********.968138, "relative_end": **********.968138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.970001, "relative_start": 0.7072830200195312, "end": **********.970001, "relative_end": **********.970001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.970531, "relative_start": 0.707813024520874, "end": **********.970531, "relative_end": **********.970531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.970982, "relative_start": 0.7082641124725342, "end": **********.970982, "relative_end": **********.970982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.971448, "relative_start": 0.7087299823760986, "end": **********.971448, "relative_end": **********.971448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.971922, "relative_start": 0.7092039585113525, "end": **********.971922, "relative_end": **********.971922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.975603, "relative_start": 0.7128851413726807, "end": **********.975603, "relative_end": **********.975603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.976224, "relative_start": 0.7135059833526611, "end": **********.976224, "relative_end": **********.976224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.976679, "relative_start": 0.713961124420166, "end": **********.976679, "relative_end": **********.976679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.978343, "relative_start": 0.7156250476837158, "end": **********.978343, "relative_end": **********.978343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.97918, "relative_start": 0.7164621353149414, "end": **********.97918, "relative_end": **********.97918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.979762, "relative_start": 0.7170441150665283, "end": **********.979762, "relative_end": **********.979762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.980254, "relative_start": 0.7175359725952148, "end": **********.980254, "relative_end": **********.980254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.98143, "relative_start": 0.7187120914459229, "end": **********.98143, "relative_end": **********.98143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.981995, "relative_start": 0.7192771434783936, "end": **********.981995, "relative_end": **********.981995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.982517, "relative_start": 0.7197990417480469, "end": **********.982517, "relative_end": **********.982517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.982827, "relative_start": 0.7201089859008789, "end": **********.982827, "relative_end": **********.982827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.98405, "relative_start": 0.7213320732116699, "end": **********.98405, "relative_end": **********.98405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.984944, "relative_start": 0.7222261428833008, "end": **********.984944, "relative_end": **********.984944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.985594, "relative_start": 0.7228760719299316, "end": **********.985594, "relative_end": **********.985594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.986336, "relative_start": 0.7236180305480957, "end": **********.986336, "relative_end": **********.986336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": **********.987659, "relative_start": 0.7249410152435303, "end": **********.987659, "relative_end": **********.987659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.994355, "relative_start": 0.7316370010375977, "end": **********.994355, "relative_end": **********.994355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.998292, "relative_start": 0.7355740070343018, "end": **********.998292, "relative_end": **********.998292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.000369, "relative_start": 0.7376511096954346, "end": **********.000369, "relative_end": **********.000369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.002006, "relative_start": 0.7392880916595459, "end": **********.002006, "relative_end": **********.002006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.003335, "relative_start": 0.740617036819458, "end": **********.003335, "relative_end": **********.003335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.004702, "relative_start": 0.7419841289520264, "end": **********.004702, "relative_end": **********.004702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.032878, "relative_start": 0.7701599597930908, "end": **********.032878, "relative_end": **********.032878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.034204, "relative_start": 0.7714860439300537, "end": **********.034204, "relative_end": **********.034204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.035838, "relative_start": 0.7731199264526367, "end": **********.035838, "relative_end": **********.035838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.047493, "relative_start": 0.7847750186920166, "end": **********.047493, "relative_end": **********.047493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app", "start": **********.051209, "relative_start": 0.7884910106658936, "end": **********.051209, "relative_end": **********.051209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::main", "start": **********.052277, "relative_start": 0.7895591259002686, "end": **********.052277, "relative_end": **********.052277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app.sidebar", "start": **********.053087, "relative_start": 0.7903690338134766, "end": **********.053087, "relative_end": **********.053087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.head", "start": **********.058381, "relative_start": 0.7956631183624268, "end": **********.058381, "relative_end": **********.058381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.toggle", "start": **********.061528, "relative_start": 0.7988100051879883, "end": **********.061528, "relative_end": **********.061528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.062824, "relative_start": 0.8001060485839844, "end": **********.062824, "relative_end": **********.062824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.065227, "relative_start": 0.802509069442749, "end": **********.065227, "relative_end": **********.065227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.065979, "relative_start": 0.8032610416412354, "end": **********.065979, "relative_end": **********.065979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.066567, "relative_start": 0.8038489818572998, "end": **********.066567, "relative_end": **********.066567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.067253, "relative_start": 0.8045351505279541, "end": **********.067253, "relative_end": **********.067253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.app-logo", "start": **********.068403, "relative_start": 0.8056850433349609, "end": **********.068403, "relative_end": **********.068403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.069974, "relative_start": 0.8072559833526611, "end": **********.069974, "relative_end": **********.069974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.071222, "relative_start": 0.8085041046142578, "end": **********.071222, "relative_end": **********.071222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.layout-dashboard", "start": **********.07264, "relative_start": 0.8099219799041748, "end": **********.07264, "relative_end": **********.07264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.073963, "relative_start": 0.8112449645996094, "end": **********.073963, "relative_end": **********.073963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.075036, "relative_start": 0.8123180866241455, "end": **********.075036, "relative_end": **********.075036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.076289, "relative_start": 0.8135709762573242, "end": **********.076289, "relative_end": **********.076289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.user", "start": **********.077494, "relative_start": 0.8147759437561035, "end": **********.077494, "relative_end": **********.077494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.078266, "relative_start": 0.8155479431152344, "end": **********.078266, "relative_end": **********.078266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.079255, "relative_start": 0.8165371417999268, "end": **********.079255, "relative_end": **********.079255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.081005, "relative_start": 0.8182871341705322, "end": **********.081005, "relative_end": **********.081005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.currency-dollar", "start": **********.082388, "relative_start": 0.8196699619293213, "end": **********.082388, "relative_end": **********.082388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.083463, "relative_start": 0.8207449913024902, "end": **********.083463, "relative_end": **********.083463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.085003, "relative_start": 0.8222849369049072, "end": **********.085003, "relative_end": **********.085003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.086798, "relative_start": 0.8240799903869629, "end": **********.086798, "relative_end": **********.086798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.scroll-text", "start": **********.088129, "relative_start": 0.8254110813140869, "end": **********.088129, "relative_end": **********.088129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.089876, "relative_start": 0.8271579742431641, "end": **********.089876, "relative_end": **********.089876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.091513, "relative_start": 0.8287949562072754, "end": **********.091513, "relative_end": **********.091513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.092817, "relative_start": 0.8300991058349609, "end": **********.092817, "relative_end": **********.092817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.queue-list", "start": **********.093965, "relative_start": 0.831247091293335, "end": **********.093965, "relative_end": **********.093965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.094852, "relative_start": 0.8321340084075928, "end": **********.094852, "relative_end": **********.094852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.096715, "relative_start": 0.8339970111846924, "end": **********.096715, "relative_end": **********.096715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.098507, "relative_start": 0.8357889652252197, "end": **********.098507, "relative_end": **********.098507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.tags", "start": **********.099575, "relative_start": 0.8368570804595947, "end": **********.099575, "relative_end": **********.099575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.100864, "relative_start": 0.8381459712982178, "end": **********.100864, "relative_end": **********.100864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.group", "start": **********.10196, "relative_start": 0.8392419815063477, "end": **********.10196, "relative_end": **********.10196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.index", "start": **********.103474, "relative_start": 0.8407559394836426, "end": **********.103474, "relative_end": **********.103474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.104527, "relative_start": 0.8418090343475342, "end": **********.104527, "relative_end": **********.104527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.106606, "relative_start": 0.8438880443572998, "end": **********.106606, "relative_end": **********.106606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.10814, "relative_start": 0.8454220294952393, "end": **********.10814, "relative_end": **********.10814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.folder-git-2", "start": **********.108845, "relative_start": 0.8461270332336426, "end": **********.108845, "relative_end": **********.108845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.109466, "relative_start": 0.8467481136322021, "end": **********.109466, "relative_end": **********.109466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.index", "start": **********.110112, "relative_start": 0.8473939895629883, "end": **********.110112, "relative_end": **********.110112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::profile", "start": **********.111283, "relative_start": 0.8485651016235352, "end": **********.111283, "relative_end": **********.111283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::avatar.index", "start": **********.11285, "relative_start": 0.8501319885253906, "end": **********.11285, "relative_end": **********.11285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.114287, "relative_start": 0.8515689373016357, "end": **********.114287, "relative_end": **********.114287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.114901, "relative_start": 0.8521831035614014, "end": **********.114901, "relative_end": **********.114901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.115559, "relative_start": 0.8528411388397217, "end": **********.115559, "relative_end": **********.115559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevrons-up-down", "start": **********.116167, "relative_start": 0.8534491062164307, "end": **********.116167, "relative_end": **********.116167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.117209, "relative_start": 0.8544909954071045, "end": **********.117209, "relative_end": **********.117209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.117823, "relative_start": 0.855104923248291, "end": **********.117823, "relative_end": **********.117823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.118616, "relative_start": 0.8558981418609619, "end": **********.118616, "relative_end": **********.118616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.119545, "relative_start": 0.8568270206451416, "end": **********.119545, "relative_end": **********.119545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.120373, "relative_start": 0.8576550483703613, "end": **********.120373, "relative_end": **********.120373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.121235, "relative_start": 0.8585169315338135, "end": **********.121235, "relative_end": **********.121235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.sun", "start": **********.122321, "relative_start": 0.8596029281616211, "end": **********.122321, "relative_end": **********.122321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.123841, "relative_start": 0.8611230850219727, "end": **********.123841, "relative_end": **********.123841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.124649, "relative_start": 0.8619310855865479, "end": **********.124649, "relative_end": **********.124649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.125399, "relative_start": 0.8626811504364014, "end": **********.125399, "relative_end": **********.125399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.moon", "start": **********.126166, "relative_start": 0.8634481430053711, "end": **********.126166, "relative_end": **********.126166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.127158, "relative_start": 0.8644399642944336, "end": **********.127158, "relative_end": **********.127158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.127769, "relative_start": 0.8650510311126709, "end": **********.127769, "relative_end": **********.127769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.128549, "relative_start": 0.8658311367034912, "end": **********.128549, "relative_end": **********.128549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.computer-desktop", "start": **********.129378, "relative_start": 0.8666601181030273, "end": **********.129378, "relative_end": **********.129378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.index", "start": **********.129992, "relative_start": 0.8672740459442139, "end": **********.129992, "relative_end": **********.129992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.variants.segmented", "start": **********.130782, "relative_start": 0.8680639266967773, "end": **********.130782, "relative_end": **********.130782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.131469, "relative_start": 0.868751049041748, "end": **********.131469, "relative_end": **********.131469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.132362, "relative_start": 0.8696439266204834, "end": **********.132362, "relative_end": **********.132362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.132794, "relative_start": 0.8700759410858154, "end": **********.132794, "relative_end": **********.132794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.133638, "relative_start": 0.870919942855835, "end": **********.133638, "relative_end": **********.133638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.134625, "relative_start": 0.8719069957733154, "end": **********.134625, "relative_end": **********.134625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.cog", "start": **********.135449, "relative_start": 0.8727309703826904, "end": **********.135449, "relative_end": **********.135449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.136055, "relative_start": 0.8733370304107666, "end": **********.136055, "relative_end": **********.136055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.136696, "relative_start": 0.8739781379699707, "end": **********.136696, "relative_end": **********.136696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.137065, "relative_start": 0.8743469715118408, "end": **********.137065, "relative_end": **********.137065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.137488, "relative_start": 0.874769926071167, "end": **********.137488, "relative_end": **********.137488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.138256, "relative_start": 0.8755381107330322, "end": **********.138256, "relative_end": **********.138256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.139875, "relative_start": 0.8771569728851318, "end": **********.139875, "relative_end": **********.139875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.arrow-right-start-on-rectangle", "start": **********.141157, "relative_start": 0.8784389495849609, "end": **********.141157, "relative_end": **********.141157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.142113, "relative_start": 0.8793950080871582, "end": **********.142113, "relative_end": **********.142113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.index", "start": **********.142817, "relative_start": 0.8800990581512451, "end": **********.142817, "relative_end": **********.142817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown", "start": **********.143309, "relative_start": 0.8805911540985107, "end": **********.143309, "relative_end": **********.143309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.index", "start": **********.143919, "relative_start": 0.8812010288238525, "end": **********.143919, "relative_end": **********.143919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.backdrop", "start": **********.14477, "relative_start": 0.882051944732666, "end": **********.14477, "relative_end": **********.14477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.toggle", "start": **********.145598, "relative_start": 0.8828799724578857, "end": **********.145598, "relative_end": **********.145598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.146213, "relative_start": 0.8834950923919678, "end": **********.146213, "relative_end": **********.146213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.147574, "relative_start": 0.8848559856414795, "end": **********.147574, "relative_end": **********.147574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.panel-left", "start": **********.148179, "relative_start": 0.8854610919952393, "end": **********.148179, "relative_end": **********.148179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.148768, "relative_start": 0.8860499858856201, "end": **********.148768, "relative_end": **********.148768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.149414, "relative_start": 0.8866961002349854, "end": **********.149414, "relative_end": **********.149414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.150035, "relative_start": 0.8873169422149658, "end": **********.150035, "relative_end": **********.150035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::profile", "start": **********.150533, "relative_start": 0.887814998626709, "end": **********.150533, "relative_end": **********.150533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::avatar.index", "start": **********.151341, "relative_start": 0.8886229991912842, "end": **********.151341, "relative_end": **********.151341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.152587, "relative_start": 0.889868974685669, "end": **********.152587, "relative_end": **********.152587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.15319, "relative_start": 0.8904719352722168, "end": **********.15319, "relative_end": **********.15319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.153789, "relative_start": 0.891071081161499, "end": **********.153789, "relative_end": **********.153789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevrons-up-down", "start": **********.154321, "relative_start": 0.8916029930114746, "end": **********.154321, "relative_end": **********.154321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.154953, "relative_start": 0.8922350406646729, "end": **********.154953, "relative_end": **********.154953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.15552, "relative_start": 0.8928020000457764, "end": **********.15552, "relative_end": **********.15552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.156346, "relative_start": 0.8936281204223633, "end": **********.156346, "relative_end": **********.156346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.157662, "relative_start": 0.8949439525604248, "end": **********.157662, "relative_end": **********.157662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.158713, "relative_start": 0.8959951400756836, "end": **********.158713, "relative_end": **********.158713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.159517, "relative_start": 0.8967990875244141, "end": **********.159517, "relative_end": **********.159517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.sun", "start": **********.160032, "relative_start": 0.8973140716552734, "end": **********.160032, "relative_end": **********.160032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.160572, "relative_start": 0.8978540897369385, "end": **********.160572, "relative_end": **********.160572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.161119, "relative_start": 0.8984010219573975, "end": **********.161119, "relative_end": **********.161119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.161782, "relative_start": 0.8990640640258789, "end": **********.161782, "relative_end": **********.161782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.moon", "start": **********.162241, "relative_start": 0.8995230197906494, "end": **********.162241, "relative_end": **********.162241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.162702, "relative_start": 0.8999841213226318, "end": **********.162702, "relative_end": **********.162702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.16316, "relative_start": 0.9004421234130859, "end": **********.16316, "relative_end": **********.16316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.163768, "relative_start": 0.9010500907897949, "end": **********.163768, "relative_end": **********.163768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.computer-desktop", "start": **********.164228, "relative_start": 0.9015100002288818, "end": **********.164228, "relative_end": **********.164228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.index", "start": **********.16468, "relative_start": 0.9019620418548584, "end": **********.16468, "relative_end": **********.16468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.variants.segmented", "start": **********.165118, "relative_start": 0.902400016784668, "end": **********.165118, "relative_end": **********.165118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.165627, "relative_start": 0.9029090404510498, "end": **********.165627, "relative_end": **********.165627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.166416, "relative_start": 0.9036979675292969, "end": **********.166416, "relative_end": **********.166416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.166785, "relative_start": 0.9040670394897461, "end": **********.166785, "relative_end": **********.166785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.167268, "relative_start": 0.9045500755310059, "end": **********.167268, "relative_end": **********.167268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.168044, "relative_start": 0.9053261280059814, "end": **********.168044, "relative_end": **********.168044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.cog", "start": **********.168528, "relative_start": 0.9058101177215576, "end": **********.168528, "relative_end": **********.168528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.168988, "relative_start": 0.9062700271606445, "end": **********.168988, "relative_end": **********.168988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.169531, "relative_start": 0.9068131446838379, "end": **********.169531, "relative_end": **********.169531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.169838, "relative_start": 0.9071199893951416, "end": **********.169838, "relative_end": **********.169838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.17025, "relative_start": 0.9075319766998291, "end": **********.17025, "relative_end": **********.17025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.170828, "relative_start": 0.9081101417541504, "end": **********.170828, "relative_end": **********.170828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.171642, "relative_start": 0.9089241027832031, "end": **********.171642, "relative_end": **********.171642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.arrow-right-start-on-rectangle", "start": **********.172933, "relative_start": 0.9102151393890381, "end": **********.172933, "relative_end": **********.172933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.17402, "relative_start": 0.9113020896911621, "end": **********.17402, "relative_end": **********.17402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.index", "start": **********.174633, "relative_start": 0.9119150638580322, "end": **********.174633, "relative_end": **********.174633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown", "start": **********.174999, "relative_start": 0.9122810363769531, "end": **********.174999, "relative_end": **********.174999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::header", "start": **********.175456, "relative_start": 0.9127380847930908, "end": **********.175456, "relative_end": **********.175456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::toast.index", "start": **********.17607, "relative_start": 0.9133520126342773, "end": **********.17607, "relative_end": **********.17607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.18041, "relative_start": 0.9176919460296631, "end": **********.180579, "relative_end": **********.180579, "duration": 0.0001690387725830078, "duration_str": "169μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 30784656, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 517, "nb_templates": 517, "templates": [{"name": "1x livewire.planned-spending", "param_count": null, "params": [], "start": **********.699786, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/planned-spending.blade.phplivewire.planned-spending", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fplanned-spending.blade.php&line=1", "ajax": false, "filename": "planned-spending.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.planned-spending"}, {"name": "13x ********************************::button.index", "param_count": null, "params": [], "start": **********.701778, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.php********************************::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 13, "name_original": "********************************::button.index"}, {"name": "66x ********************************::icon.index", "param_count": null, "params": [], "start": **********.704334, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.php********************************::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 66, "name_original": "********************************::icon.index"}, {"name": "2x ********************************::icon.plus", "param_count": null, "params": [], "start": **********.705221, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/plus.blade.php********************************::icon.plus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fplus.blade.php&line=1", "ajax": false, "filename": "plus.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.plus"}, {"name": "26x ********************************::button-or-link", "param_count": null, "params": [], "start": **********.706242, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.php********************************::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}, "render_count": 26, "name_original": "********************************::button-or-link"}, {"name": "15x ********************************::with-tooltip", "param_count": null, "params": [], "start": **********.707488, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.php********************************::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}, "render_count": 15, "name_original": "********************************::with-tooltip"}, {"name": "2x ********************************::modal.trigger", "param_count": null, "params": [], "start": **********.708179, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/trigger.blade.php********************************::modal.trigger", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Ftrigger.blade.php&line=1", "ajax": false, "filename": "trigger.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.trigger"}, {"name": "1x livewire.planned-spending-form", "param_count": null, "params": [], "start": **********.726438, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/planned-spending-form.blade.phplivewire.planned-spending-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fplanned-spending-form.blade.php&line=1", "ajax": false, "filename": "planned-spending-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.planned-spending-form"}, {"name": "3x ********************************::heading", "param_count": null, "params": [], "start": **********.728161, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.php********************************::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::heading"}, {"name": "5x ********************************::label", "param_count": null, "params": [], "start": **********.729181, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.php********************************::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::label"}, {"name": "3x ********************************::input.index", "param_count": null, "params": [], "start": **********.729823, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.php********************************::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::input.index"}, {"name": "47x ********************************::with-field", "param_count": null, "params": [], "start": **********.731919, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.php********************************::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}, "render_count": 47, "name_original": "********************************::with-field"}, {"name": "5x ********************************::error", "param_count": null, "params": [], "start": **********.733065, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.php********************************::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::error"}, {"name": "5x ********************************::field", "param_count": null, "params": [], "start": **********.733674, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.php********************************::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::field"}, {"name": "1x components.categories", "param_count": null, "params": [], "start": **********.734251, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/categories.blade.phpcomponents.categories", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcategories.blade.php&line=1", "ajax": false, "filename": "categories.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.categories"}, {"name": "1x ********************************::select.search", "param_count": null, "params": [], "start": **********.73619, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/search.blade.php********************************::select.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.search"}, {"name": "1x ********************************::icon.magnifying-glass", "param_count": null, "params": [], "start": **********.737498, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/magnifying-glass.blade.php********************************::icon.magnifying-glass", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmagnifying-glass.blade.php&line=1", "ajax": false, "filename": "magnifying-glass.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.magnifying-glass"}, {"name": "6x ********************************::icon.x-mark", "param_count": null, "params": [], "start": **********.741414, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/x-mark.blade.php********************************::icon.x-mark", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fx-mark.blade.php&line=1", "ajax": false, "filename": "x-mark.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::icon.x-mark"}, {"name": "40x ********************************::select.option.index", "param_count": null, "params": [], "start": **********.747417, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/option/index.blade.php********************************::select.option.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foption%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 40, "name_original": "********************************::select.option.index"}, {"name": "40x ********************************::select.option.variants.custom", "param_count": null, "params": [], "start": **********.748217, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/option/variants/custom.blade.php********************************::select.option.variants.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foption%2Fvariants%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}, "render_count": 40, "name_original": "********************************::select.option.variants.custom"}, {"name": "40x ********************************::select.indicator.index", "param_count": null, "params": [], "start": **********.749002, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/indicator/index.blade.php********************************::select.indicator.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findicator%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 40, "name_original": "********************************::select.indicator.index"}, {"name": "40x ********************************::select.indicator.variants.check", "param_count": null, "params": [], "start": **********.749634, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/indicator/variants/check.blade.php********************************::select.indicator.variants.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findicator%2Fvariants%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}, "render_count": 40, "name_original": "********************************::select.indicator.variants.check"}, {"name": "40x ********************************::icon.check", "param_count": null, "params": [], "start": **********.750854, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/check.blade.php********************************::icon.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}, "render_count": 40, "name_original": "********************************::icon.check"}, {"name": "2x ********************************::select.index", "param_count": null, "params": [], "start": **********.884264, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/index.blade.php********************************::select.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::select.index"}, {"name": "2x ********************************::select.variants.listbox", "param_count": null, "params": [], "start": **********.885023, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/variants/listbox.blade.php********************************::select.variants.listbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fvariants%2Flistbox.blade.php&line=1", "ajax": false, "filename": "listbox.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::select.variants.listbox"}, {"name": "2x ********************************::select.button", "param_count": null, "params": [], "start": **********.885946, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/button.blade.php********************************::select.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::select.button"}, {"name": "2x ********************************::select.selected", "param_count": null, "params": [], "start": **********.886949, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/selected.blade.php********************************::select.selected", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fselected.blade.php&line=1", "ajax": false, "filename": "selected.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::select.selected"}, {"name": "2x ********************************::icon.chevron-down", "param_count": null, "params": [], "start": **********.892543, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-down.blade.php********************************::icon.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.chevron-down"}, {"name": "2x ********************************::select.options", "param_count": null, "params": [], "start": **********.893278, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/options.blade.php********************************::select.options", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foptions.blade.php&line=1", "ajax": false, "filename": "options.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::select.options"}, {"name": "1x ********************************::select.empty", "param_count": null, "params": [], "start": **********.894218, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/empty.blade.php********************************::select.empty", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fempty.blade.php&line=1", "ajax": false, "filename": "empty.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.empty"}, {"name": "1x livewire.category-form", "param_count": null, "params": [], "start": **********.898074, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/category-form.blade.phplivewire.category-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fcategory-form.blade.php&line=1", "ajax": false, "filename": "category-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.category-form"}, {"name": "3x ********************************::spacer", "param_count": null, "params": [], "start": **********.959846, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/spacer.blade.php********************************::spacer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fspacer.blade.php&line=1", "ajax": false, "filename": "spacer.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::spacer"}, {"name": "4x ********************************::modal.close", "param_count": null, "params": [], "start": **********.962408, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/close.blade.php********************************::modal.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::modal.close"}, {"name": "3x ********************************::icon.loading", "param_count": null, "params": [], "start": **********.96473, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.php********************************::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::icon.loading"}, {"name": "2x ********************************::modal.index", "param_count": null, "params": [], "start": **********.966889, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/index.blade.php********************************::modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.index"}, {"name": "1x components.card", "param_count": null, "params": [], "start": **********.032723, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.card"}, {"name": "1x ********************************::card.index", "param_count": null, "params": [], "start": **********.035743, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/card/index.blade.php********************************::card.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::card.index"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.047402, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x components.layouts.app", "param_count": null, "params": [], "start": **********.051147, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app.blade.phpcomponents.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app"}, {"name": "1x ********************************::main", "param_count": null, "params": [], "start": **********.052216, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/main.blade.php********************************::main", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmain.blade.php&line=1", "ajax": false, "filename": "main.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::main"}, {"name": "1x components.layouts.app.sidebar", "param_count": null, "params": [], "start": **********.053026, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app/sidebar.blade.phpcomponents.layouts.app.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app.sidebar"}, {"name": "1x partials.head", "param_count": null, "params": [], "start": **********.058316, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/partials/head.blade.phppartials.head", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fpartials%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.head"}, {"name": "2x ********************************::sidebar.toggle", "param_count": null, "params": [], "start": **********.061434, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/toggle.blade.php********************************::sidebar.toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Ftoggle.blade.php&line=1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::sidebar.toggle"}, {"name": "1x components.app-logo", "param_count": null, "params": [], "start": **********.068205, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/app-logo.blade.phpcomponents.app-logo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fapp-logo.blade.php&line=1", "ajax": false, "filename": "app-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.app-logo"}, {"name": "7x ********************************::navlist.item", "param_count": null, "params": [], "start": **********.069903, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/item.blade.php********************************::navlist.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 7, "name_original": "********************************::navlist.item"}, {"name": "1x ********************************::icon.layout-dashboard", "param_count": null, "params": [], "start": **********.072503, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/layout-dashboard.blade.php********************************::icon.layout-dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Flayout-dashboard.blade.php&line=1", "ajax": false, "filename": "layout-dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.layout-dashboard"}, {"name": "1x ********************************::icon.user", "param_count": null, "params": [], "start": **********.077429, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/user.blade.php********************************::icon.user", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.user"}, {"name": "1x ********************************::icon.currency-dollar", "param_count": null, "params": [], "start": **********.082316, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/currency-dollar.blade.php********************************::icon.currency-dollar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcurrency-dollar.blade.php&line=1", "ajax": false, "filename": "currency-dollar.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.currency-dollar"}, {"name": "1x ********************************::icon.scroll-text", "param_count": null, "params": [], "start": **********.088023, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/scroll-text.blade.php********************************::icon.scroll-text", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fscroll-text.blade.php&line=1", "ajax": false, "filename": "scroll-text.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.scroll-text"}, {"name": "1x ********************************::icon.queue-list", "param_count": null, "params": [], "start": **********.0939, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/queue-list.blade.php********************************::icon.queue-list", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fqueue-list.blade.php&line=1", "ajax": false, "filename": "queue-list.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.queue-list"}, {"name": "1x ********************************::icon.tags", "param_count": null, "params": [], "start": **********.09948, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/tags.blade.php********************************::icon.tags", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ftags.blade.php&line=1", "ajax": false, "filename": "tags.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.tags"}, {"name": "1x ********************************::navlist.group", "param_count": null, "params": [], "start": **********.10186, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/navlist/group.blade.php********************************::navlist.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::navlist.group"}, {"name": "2x ********************************::navlist.index", "param_count": null, "params": [], "start": **********.103354, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/index.blade.php********************************::navlist.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::navlist.index"}, {"name": "1x ********************************::icon.folder-git-2", "param_count": null, "params": [], "start": **********.108784, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/folder-git-2.blade.php********************************::icon.folder-git-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ffolder-git-2.blade.php&line=1", "ajax": false, "filename": "folder-git-2.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.folder-git-2"}, {"name": "2x ********************************::profile", "param_count": null, "params": [], "start": **********.111222, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/profile.blade.php********************************::profile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fprofile.blade.php&line=1", "ajax": false, "filename": "profile.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::profile"}, {"name": "2x ********************************::avatar.index", "param_count": null, "params": [], "start": **********.112787, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/avatar/index.blade.php********************************::avatar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Favatar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::avatar.index"}, {"name": "2x ********************************::icon.chevrons-up-down", "param_count": null, "params": [], "start": **********.116105, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/chevrons-up-down.blade.php********************************::icon.chevrons-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevrons-up-down.blade.php&line=1", "ajax": false, "filename": "chevrons-up-down.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.chevrons-up-down"}, {"name": "4x ********************************::menu.radio.group", "param_count": null, "params": [], "start": **********.117145, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/radio/group.blade.php********************************::menu.radio.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fradio%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::menu.radio.group"}, {"name": "6x ********************************::menu.separator", "param_count": null, "params": [], "start": **********.117758, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/separator.blade.php********************************::menu.separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::menu.separator"}, {"name": "6x ********************************::separator", "param_count": null, "params": [], "start": **********.118553, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/separator.blade.php********************************::separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::separator"}, {"name": "6x ********************************::radio.index", "param_count": null, "params": [], "start": **********.119482, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/index.blade.php********************************::radio.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::radio.index"}, {"name": "6x ********************************::radio.variants.segmented", "param_count": null, "params": [], "start": **********.120298, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/variants/segmented.blade.php********************************::radio.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::radio.variants.segmented"}, {"name": "2x ********************************::icon.sun", "param_count": null, "params": [], "start": **********.122248, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/sun.blade.php********************************::icon.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.sun"}, {"name": "2x ********************************::icon.moon", "param_count": null, "params": [], "start": **********.126109, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/moon.blade.php********************************::icon.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.moon"}, {"name": "2x ********************************::icon.computer-desktop", "param_count": null, "params": [], "start": **********.12931, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/computer-desktop.blade.php********************************::icon.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.computer-desktop"}, {"name": "2x ********************************::radio.group.index", "param_count": null, "params": [], "start": **********.129931, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/index.blade.php********************************::radio.group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::radio.group.index"}, {"name": "2x ********************************::radio.group.variants.segmented", "param_count": null, "params": [], "start": **********.130719, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/variants/segmented.blade.php********************************::radio.group.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::radio.group.variants.segmented"}, {"name": "4x ********************************::menu.item", "param_count": null, "params": [], "start": **********.133576, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/item.blade.php********************************::menu.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::menu.item"}, {"name": "2x ********************************::icon.cog", "param_count": null, "params": [], "start": **********.135386, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/cog.blade.php********************************::icon.cog", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcog.blade.php&line=1", "ajax": false, "filename": "cog.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.cog"}, {"name": "2x ********************************::icon.arrow-right-start-on-rectangle", "param_count": null, "params": [], "start": **********.141091, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/arrow-right-start-on-rectangle.blade.php********************************::icon.arrow-right-start-on-rectangle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Farrow-right-start-on-rectangle.blade.php&line=1", "ajax": false, "filename": "arrow-right-start-on-rectangle.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.arrow-right-start-on-rectangle"}, {"name": "2x ********************************::menu.index", "param_count": null, "params": [], "start": **********.142753, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/index.blade.php********************************::menu.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::menu.index"}, {"name": "2x ********************************::dropdown", "param_count": null, "params": [], "start": **********.143248, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/dropdown.blade.php********************************::dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::dropdown"}, {"name": "1x ********************************::sidebar.index", "param_count": null, "params": [], "start": **********.143856, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/index.blade.php********************************::sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::sidebar.index"}, {"name": "1x ********************************::sidebar.backdrop", "param_count": null, "params": [], "start": **********.144701, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/backdrop.blade.php********************************::sidebar.backdrop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Fbackdrop.blade.php&line=1", "ajax": false, "filename": "backdrop.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::sidebar.backdrop"}, {"name": "1x ********************************::icon.panel-left", "param_count": null, "params": [], "start": **********.148119, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/panel-left.blade.php********************************::icon.panel-left", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fpanel-left.blade.php&line=1", "ajax": false, "filename": "panel-left.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.panel-left"}, {"name": "1x ********************************::header", "param_count": null, "params": [], "start": **********.175379, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/header.blade.php********************************::header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::header"}, {"name": "1x ********************************::toast.index", "param_count": null, "params": [], "start": **********.176019, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/toast/index.blade.php********************************::toast.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ftoast%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::toast.index"}]}, "queries": {"count": 8, "nb_statements": 7, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.032920000000000005, "accumulated_duration_str": "32.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.60939, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr' limit 1", "type": "query", "params": [], "bindings": ["kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.6181679, "duration": 0.027960000000000002, "duration_str": "27.96ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 84.933}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.671189, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 84.933, "width_percent": 1.914}, {"sql": "select `planned_expenses`.*, `categories`.`user_id` as `laravel_through_key` from `planned_expenses` inner join `categories` on `categories`.`id` = `planned_expenses`.`category_id` where `categories`.`user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/PlannedSpending.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpending.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.6878312, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "PlannedSpending.php:19", "source": {"index": 20, "namespace": null, "name": "app/Livewire/PlannedSpending.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpending.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpending.php&line=19", "ajax": false, "filename": "PlannedSpending.php", "line": "19"}, "connection": "daily", "explain": null, "start_percent": 86.847, "width_percent": 1.974}, {"sql": "select\ntransactions.category_id,\ncategories.parent_id,\nSUM(\nCASE\nWHEN transactions.type IN (\"credit\", \"deposit\") THEN -transactions.amount\nELSE transactions.amount\nEND\n) as total_spent\nfrom `transactions` inner join `categories` on `transactions`.`category_id` = `categories`.`id` where (`transactions`.`category_id` in (1) or `categories`.`parent_id` in (1)) and `transactions`.`date` between '2025-05-01' and '2025-05-31' group by `transactions`.`category_id`, `categories`.`parent_id`", "type": "query", "params": [], "bindings": [1, 1, "2025-05-01", "2025-05-31"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/PlannedSpending.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpending.php", "line": 47}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.6926749, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "PlannedSpending.php:47", "source": {"index": 15, "namespace": null, "name": "app/Livewire/PlannedSpending.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpending.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpending.php&line=47", "ajax": false, "filename": "PlannedSpending.php", "line": "47"}, "connection": "daily", "explain": null, "start_percent": 88.821, "width_percent": 3.433}, {"sql": "select `id`, `name`, `parent_id` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null and `parent_id` is null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/PlannedSpendingForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpendingForm.php", "line": 57}, {"index": 17, "namespace": null, "name": "app/Livewire/PlannedSpendingForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpendingForm.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.711601, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "PlannedSpendingForm.php:57", "source": {"index": 16, "namespace": null, "name": "app/Livewire/PlannedSpendingForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpendingForm.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpendingForm.php&line=57", "ajax": false, "filename": "PlannedSpendingForm.php", "line": "57"}, "connection": "daily", "explain": null, "start_percent": 92.254, "width_percent": 2.339}, {"sql": "select * from `categories` where `categories`.`parent_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Livewire/PlannedSpendingForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpendingForm.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Livewire/PlannedSpendingForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpendingForm.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.714714, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "PlannedSpendingForm.php:57", "source": {"index": 21, "namespace": null, "name": "app/Livewire/PlannedSpendingForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpendingForm.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpendingForm.php&line=57", "ajax": false, "filename": "PlannedSpendingForm.php", "line": "57"}, "connection": "daily", "explain": null, "start_percent": 94.593, "width_percent": 2.157}, {"sql": "select `id`, `name` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null and `parent_id` is null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/CategoryForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryForm.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/helpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\helpers.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportComputed/BaseComputed.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\BaseComputed.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportComputed/BaseComputed.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\BaseComputed.php", "line": 60}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/EventBus.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\EventBus.php", "line": 60}], "start": **********.9050999, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "CategoryForm.php:74", "source": {"index": 16, "namespace": null, "name": "app/Livewire/CategoryForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryForm.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryForm.php&line=74", "ajax": false, "filename": "CategoryForm.php", "line": "74"}, "connection": "daily", "explain": null, "start_percent": 96.75, "width_percent": 3.25}]}, "models": {"data": {"App\\Models\\Category": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PlannedExpense": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FPlannedExpense.php&line=1", "ajax": false, "filename": "PlannedExpense.php", "line": "?"}}}, "count": 42, "is_counter": true}, "livewire": {"data": {"planned-spending #zTuY8qHkfHCTZXSQc6VB": "array:4 [\n  \"data\" => []\n  \"name\" => \"planned-spending\"\n  \"component\" => \"App\\Livewire\\PlannedSpending\"\n  \"id\" => \"zTuY8qHkfHCTZXSQc6VB\"\n]", "planned-spending-form #kcrU6ZTnePHLvUnCRuJp": "array:4 [\n  \"data\" => array:5 [\n    \"name\" => \"\"\n    \"category_id\" => null\n    \"monthly_amount\" => null\n    \"expense\" => null\n    \"categories\" => array:10 [\n      0 => array:4 [\n        \"id\" => 1\n        \"name\" => \"Auto & Transport\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 11\n            \"user_id\" => 1\n            \"name\" => \"Car Insurance\"\n            \"parent_id\" => 1\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 12\n            \"user_id\" => 1\n            \"name\" => \"Car Payment\"\n            \"parent_id\" => 1\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      1 => array:4 [\n        \"id\" => 2\n        \"name\" => \"Food\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 13\n            \"user_id\" => 1\n            \"name\" => \"Fast Food\"\n            \"parent_id\" => 2\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 14\n            \"user_id\" => 1\n            \"name\" => \"Restaurants\"\n            \"parent_id\" => 2\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      2 => array:4 [\n        \"id\" => 4\n        \"name\" => \"Health\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 17\n            \"user_id\" => 1\n            \"name\" => \"Doctor\"\n            \"parent_id\" => 4\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 18\n            \"user_id\" => 1\n            \"name\" => \"Pharmacy\"\n            \"parent_id\" => 4\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      3 => array:4 [\n        \"id\" => 3\n        \"name\" => \"Home\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 15\n            \"user_id\" => 1\n            \"name\" => \"Mortgage\"\n            \"parent_id\" => 3\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 16\n            \"user_id\" => 1\n            \"name\" => \"Rent\"\n            \"parent_id\" => 3\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      4 => array:4 [\n        \"id\" => 5\n        \"name\" => \"Personal Care\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 19\n            \"user_id\" => 1\n            \"name\" => \"Haircut\"\n            \"parent_id\" => 5\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 20\n            \"user_id\" => 1\n            \"name\" => \"Laundry\"\n            \"parent_id\" => 5\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      5 => array:4 [\n        \"id\" => 6\n        \"name\" => \"Personal Income\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 22\n            \"user_id\" => 1\n            \"name\" => \"Bonus\"\n            \"parent_id\" => 6\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 21\n            \"user_id\" => 1\n            \"name\" => \"Paycheck\"\n            \"parent_id\" => 6\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      6 => array:4 [\n        \"id\" => 7\n        \"name\" => \"Pets\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 23\n            \"user_id\" => 1\n            \"name\" => \"Pet Food\"\n            \"parent_id\" => 7\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 24\n            \"user_id\" => 1\n            \"name\" => \"Veterinary\"\n            \"parent_id\" => 7\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      7 => array:4 [\n        \"id\" => 8\n        \"name\" => \"Shopping\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 25\n            \"user_id\" => 1\n            \"name\" => \"Clothing\"\n            \"parent_id\" => 8\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 26\n            \"user_id\" => 1\n            \"name\" => \"Gifts\"\n            \"parent_id\" => 8\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      8 => array:4 [\n        \"id\" => 9\n        \"name\" => \"Travel\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 28\n            \"user_id\" => 1\n            \"name\" => \"Airfare\"\n            \"parent_id\" => 9\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 27\n            \"user_id\" => 1\n            \"name\" => \"Hotel\"\n            \"parent_id\" => 9\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      9 => array:4 [\n        \"id\" => 10\n        \"name\" => \"Utilities\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 30\n            \"user_id\" => 1\n            \"name\" => \"Electric\"\n            \"parent_id\" => 10\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 29\n            \"user_id\" => 1\n            \"name\" => \"Gas\"\n            \"parent_id\" => 10\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n    ]\n  ]\n  \"name\" => \"planned-spending-form\"\n  \"component\" => \"App\\Livewire\\PlannedSpendingForm\"\n  \"id\" => \"kcrU6ZTnePHLvUnCRuJp\"\n]", "category-form #0acX1a78ho4FUi3usyW1": "array:4 [\n  \"data\" => array:4 [\n    \"show_category_form\" => false\n    \"category\" => null\n    \"parent_id\" => null\n    \"name\" => \"\"\n  ]\n  \"name\" => \"category-form\"\n  \"component\" => \"App\\Livewire\\CategoryForm\"\n  \"id\" => \"0acX1a78ho4FUi3usyW1\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/planned-spending", "action_name": "planned-spending", "controller_action": "App\\Livewire\\PlannedSpending", "uri": "GET planned-spending", "controller": "App\\Livewire\\PlannedSpending@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpending.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpending.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/PlannedSpending.php:17-58</a>", "middleware": "web, auth", "duration": "922ms", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1186468417 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1186468417\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-366269865 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-366269865\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1693077814 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire-navigate</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/accounts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"751 characters\">PHPSESSID=aa0mh29tpvppc77i3l7peen7g0; XSRF-TOKEN=eyJpdiI6Ik1pcGllMTFxV1lwK0ZwV09zblB0cFE9PSIsInZhbHVlIjoiNmlvZ2QzK0Zma1lIbGpBOGJmSFZDY1crV3l1eTduSVBuYjZuc3MvYWJLQW9wTWJndVlhT2lRaHlxZjA3cGVJSXpOVXlJaWtHZkZxRXZWUjArY2pkelBqQjdkVUNrRXQ0R01zcFV4Qnk3cnQ3ckxBR3g2aTNyR3IwNlpPWjJkUHAiLCJtYWMiOiJmOTk5NzliMzAwZWVlODdkNTc1N2VkNDhiZThlNWZhZGY4N2M2YTEzMzVjZjM0NzE5Njg0NDdiZTIxYjVhNmZmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImJMbkFSMHBib0VBOHB0TGRLYWlQdmc9PSIsInZhbHVlIjoiMldsdFB6QTVVc0pvM2RHaDdlR3k2QjhNZU03Rm1BWnhOSkJUSDdLSEFteEVJODk0RG4yMFpBS1ZEbHgweEZmbDNrVkZ3ek9jRXRLNm5VRjNydW8wZ0JMM05rSFhGYXk1bXFaTHZESU1vcXRlcHVJNW95aVFIVmlMVWJhQTFnbTYiLCJtYWMiOiI2MThkZDQzOWE0YTYyNGI5NDkxZGRmOGVhZjY0Zjg0MWNmZDdjMmQ1NTZmZmZhOTJhMWJjZWE1YjdjYmE3ZWMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693077814\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1213830203 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 25 May 2025 09:15:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213830203\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/accounts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/planned-spending", "action_name": "planned-spending", "controller_action": "App\\Livewire\\PlannedSpending"}, "badge": null}}