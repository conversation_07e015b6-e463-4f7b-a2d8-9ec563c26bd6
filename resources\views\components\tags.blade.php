@props(['user_tags', 'disabled' => null])

<flux:field>
    <flux:label @class([
        'opacity-50' => $disabled
    ])>
        Tags
    </flux:label>

    <div class="space-y-2">
        <x-select multiple wire:model="tags">
            @foreach ($user_tags as $tag)
                <x-option value="{{ $tag }}" class="font-semibold">
                    {{ $tag }}
                </x-option>
            @endforeach
        </x-select>

        <div class="flex justify-between items-center">
            <div class="text-xs text-zinc-500 dark:text-zinc-400">
                Hold Ctrl (Cmd on Mac) to select multiple tags
            </div>

            <!-- Add tag button -->
            <flux:modal.trigger name="add-tag">
                <flux:button square variant="subtle" size="sm" aria-label="Add new tag">
                    <flux:icon.plus variant="micro" />
                    <span class="ml-1 text-xs">Add Tag</span>
                </flux:button>
            </flux:modal.trigger>
        </div>
    </div>

    <flux:error name="tags" />

    <div class="absolute">
        <livewire:tag-form />
    </div>

    <div x-cloak x-show="$wire.tags.length" class="flex flex-wrap gap-1.5">
        <template x-for="(tag, index) in $wire.tags" :key="index">
            <flux:badge color="emerald">
                <p x-text="tag"></p>
                <flux:badge.close x-on:click="$wire.tags.splice(index, 1)" />
            </flux:badge>
        </template>
    </div>
</flux:field>