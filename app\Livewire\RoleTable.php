<?php

declare(strict_types=1);

namespace App\Livewire;

use Flux\Flux;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\WithPagination;
use Spatie\Permission\Models\Role;
use Illuminate\Contracts\View\View;
use Illuminate\Contracts\Database\Eloquent\Builder;

class RoleTable extends Component
{
    use WithPagination;

    public string $search = '';

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    public function delete(int $role_id): void
    {
        $role = Role::find($role_id);

        // Prevent deletion of super admin role
        if ($role && $role->name === 'super-admin') {
            Flux::toast(
                variant: 'danger',
                text: 'Cannot delete super admin role',
            );
            return;
        }

        Flux::toast(
            variant: 'success',
            text: "Successfully deleted the {$role?->name} role",
        );

        Flux::modals()->close();

        $role?->delete();
    }

    #[On('role-saved')]
    public function render(): View
    {
        return view('livewire.role-table', [
            'roles' => Role::query()
                ->withCount('permissions', 'users')
                ->when(strlen($this->search) >= 1, function (Builder $query): void {
                    $query->where('name', 'like', "%{$this->search}%");
                })
                ->orderBy('name')
                ->paginate(15),
        ]);
    }
}
