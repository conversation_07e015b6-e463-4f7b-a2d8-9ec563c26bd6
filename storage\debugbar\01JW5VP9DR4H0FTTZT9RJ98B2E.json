{"__meta": {"id": "01JW5VP9DR4H0FTTZT9RJ98B2E", "datetime": "2025-05-26 08:35:03", "utime": **********.737949, "method": "GET", "uri": "/tags", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748248502.465967, "end": **********.737966, "duration": 1.2719991207122803, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": 1748248502.465967, "relative_start": 0, "end": **********.05997, "relative_end": **********.05997, "duration": 0.****************, "duration_str": "594ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.05999, "relative_start": 0.****************, "end": **********.737968, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "678ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.157968, "relative_start": 0.****************, "end": **********.169157, "relative_end": **********.169157, "duration": 0.011188983917236328, "duration_str": "11.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.tag-table", "start": **********.304733, "relative_start": 0.****************, "end": **********.304733, "relative_end": **********.304733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.308874, "relative_start": 0.****************, "end": **********.308874, "relative_end": **********.308874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.310335, "relative_start": 0.8443679809570312, "end": **********.310335, "relative_end": **********.310335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.31287, "relative_start": 0.8469030857086182, "end": **********.31287, "relative_end": **********.31287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "start": **********.315859, "relative_start": 0.8498921394348145, "end": **********.315859, "relative_end": **********.315859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.316592, "relative_start": 0.8506250381469727, "end": **********.316592, "relative_end": **********.316592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.317318, "relative_start": 0.8513510227203369, "end": **********.317318, "relative_end": **********.317318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.318052, "relative_start": 0.8520851135253906, "end": **********.318052, "relative_end": **********.318052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.tag-form", "start": **********.320556, "relative_start": 0.8545889854431152, "end": **********.320556, "relative_end": **********.320556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.322599, "relative_start": 0.8566319942474365, "end": **********.322599, "relative_end": **********.322599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.324403, "relative_start": 0.858436107635498, "end": **********.324403, "relative_end": **********.324403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.325759, "relative_start": 0.8597919940948486, "end": **********.325759, "relative_end": **********.325759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.331099, "relative_start": 0.8651320934295654, "end": **********.331099, "relative_end": **********.331099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.333241, "relative_start": 0.8672740459442139, "end": **********.333241, "relative_end": **********.333241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.334119, "relative_start": 0.8681521415710449, "end": **********.334119, "relative_end": **********.334119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.335148, "relative_start": 0.8691811561584473, "end": **********.335148, "relative_end": **********.335148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.335828, "relative_start": 0.8698611259460449, "end": **********.335828, "relative_end": **********.335828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.337283, "relative_start": 0.8713159561157227, "end": **********.337283, "relative_end": **********.337283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.337956, "relative_start": 0.8719890117645264, "end": **********.337956, "relative_end": **********.337956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.338638, "relative_start": 0.8726711273193359, "end": **********.338638, "relative_end": **********.338638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.33917, "relative_start": 0.8732030391693115, "end": **********.33917, "relative_end": **********.33917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.340928, "relative_start": 0.8749611377716064, "end": **********.340928, "relative_end": **********.340928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.341956, "relative_start": 0.8759889602661133, "end": **********.341956, "relative_end": **********.341956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.342744, "relative_start": 0.876777172088623, "end": **********.342744, "relative_end": **********.342744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.343366, "relative_start": 0.8773989677429199, "end": **********.343366, "relative_end": **********.343366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.343942, "relative_start": 0.8779749870300293, "end": **********.343942, "relative_end": **********.343942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.345305, "relative_start": 0.8793380260467529, "end": **********.345305, "relative_end": **********.345305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.349391, "relative_start": 0.8834240436553955, "end": **********.349391, "relative_end": **********.349391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.350554, "relative_start": 0.8845870494842529, "end": **********.350554, "relative_end": **********.350554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.351299, "relative_start": 0.8853321075439453, "end": **********.351299, "relative_end": **********.351299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.351985, "relative_start": 0.8860180377960205, "end": **********.351985, "relative_end": **********.351985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.352555, "relative_start": 0.8865880966186523, "end": **********.352555, "relative_end": **********.352555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.354614, "relative_start": 0.8886470794677734, "end": **********.354614, "relative_end": **********.354614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.357628, "relative_start": 0.8916611671447754, "end": **********.357628, "relative_end": **********.357628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "start": **********.358787, "relative_start": 0.8928201198577881, "end": **********.358787, "relative_end": **********.358787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.359534, "relative_start": 0.8935670852661133, "end": **********.359534, "relative_end": **********.359534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.360076, "relative_start": 0.8941090106964111, "end": **********.360076, "relative_end": **********.360076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "start": **********.360788, "relative_start": 0.8948211669921875, "end": **********.360788, "relative_end": **********.360788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.361896, "relative_start": 0.8959290981292725, "end": **********.361896, "relative_end": **********.361896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.362703, "relative_start": 0.8967361450195312, "end": **********.362703, "relative_end": **********.362703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.367008, "relative_start": 0.9010410308837891, "end": **********.367008, "relative_end": **********.367008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.367727, "relative_start": 0.9017601013183594, "end": **********.367727, "relative_end": **********.367727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.368452, "relative_start": 0.9024851322174072, "end": **********.368452, "relative_end": **********.368452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": **********.370488, "relative_start": 0.9045209884643555, "end": **********.370488, "relative_end": **********.370488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": **********.371408, "relative_start": 0.9054410457611084, "end": **********.371408, "relative_end": **********.371408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.columns", "start": **********.372076, "relative_start": 0.906109094619751, "end": **********.372076, "relative_end": **********.372076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.373021, "relative_start": 0.9070539474487305, "end": **********.373021, "relative_end": **********.373021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.373841, "relative_start": 0.9078741073608398, "end": **********.373841, "relative_end": **********.373841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.375437, "relative_start": 0.9094700813293457, "end": **********.375437, "relative_end": **********.375437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.37637, "relative_start": 0.9104030132293701, "end": **********.37637, "relative_end": **********.37637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.377054, "relative_start": 0.9110870361328125, "end": **********.377054, "relative_end": **********.377054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.378026, "relative_start": 0.9120590686798096, "end": **********.378026, "relative_end": **********.378026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.378876, "relative_start": 0.9129090309143066, "end": **********.378876, "relative_end": **********.378876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.tag-form", "start": **********.383778, "relative_start": 0.9178111553192139, "end": **********.383778, "relative_end": **********.383778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.385349, "relative_start": 0.9193820953369141, "end": **********.385349, "relative_end": **********.385349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.386079, "relative_start": 0.920112133026123, "end": **********.386079, "relative_end": **********.386079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.386673, "relative_start": 0.920706033706665, "end": **********.386673, "relative_end": **********.386673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.388813, "relative_start": 0.9228460788726807, "end": **********.388813, "relative_end": **********.388813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.389777, "relative_start": 0.9238100051879883, "end": **********.389777, "relative_end": **********.389777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.390481, "relative_start": 0.9245140552520752, "end": **********.390481, "relative_end": **********.390481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.391093, "relative_start": 0.9251260757446289, "end": **********.391093, "relative_end": **********.391093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.391541, "relative_start": 0.9255740642547607, "end": **********.391541, "relative_end": **********.391541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.393002, "relative_start": 0.9270350933074951, "end": **********.393002, "relative_end": **********.393002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.393983, "relative_start": 0.928015947341919, "end": **********.393983, "relative_end": **********.393983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.394954, "relative_start": 0.9289870262145996, "end": **********.394954, "relative_end": **********.394954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.395458, "relative_start": 0.9294910430908203, "end": **********.395458, "relative_end": **********.395458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.398517, "relative_start": 0.9325499534606934, "end": **********.398517, "relative_end": **********.398517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.399142, "relative_start": 0.9331750869750977, "end": **********.399142, "relative_end": **********.399142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.399689, "relative_start": 0.9337220191955566, "end": **********.399689, "relative_end": **********.399689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.400276, "relative_start": 0.9343090057373047, "end": **********.400276, "relative_end": **********.400276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.400854, "relative_start": 0.934887170791626, "end": **********.400854, "relative_end": **********.400854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.401659, "relative_start": 0.9356920719146729, "end": **********.401659, "relative_end": **********.401659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.402898, "relative_start": 0.9369311332702637, "end": **********.402898, "relative_end": **********.402898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.403425, "relative_start": 0.9374580383300781, "end": **********.403425, "relative_end": **********.403425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.403995, "relative_start": 0.93802809715271, "end": **********.403995, "relative_end": **********.403995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.404934, "relative_start": 0.9389669895172119, "end": **********.404934, "relative_end": **********.404934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.406004, "relative_start": 0.9400370121002197, "end": **********.406004, "relative_end": **********.406004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.412951, "relative_start": 0.946984052658081, "end": **********.412951, "relative_end": **********.412951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.417442, "relative_start": 0.9514751434326172, "end": **********.417442, "relative_end": **********.417442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.418743, "relative_start": 0.9527759552001953, "end": **********.418743, "relative_end": **********.418743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.419832, "relative_start": 0.9538650512695312, "end": **********.419832, "relative_end": **********.419832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.42081, "relative_start": 0.9548430442810059, "end": **********.42081, "relative_end": **********.42081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.421656, "relative_start": 0.9556889533996582, "end": **********.421656, "relative_end": **********.421656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.422296, "relative_start": 0.9563291072845459, "end": **********.422296, "relative_end": **********.422296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.423573, "relative_start": 0.9576060771942139, "end": **********.423573, "relative_end": **********.423573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.424541, "relative_start": 0.9585740566253662, "end": **********.424541, "relative_end": **********.424541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.425376, "relative_start": 0.9594089984893799, "end": **********.425376, "relative_end": **********.425376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.427468, "relative_start": 0.9615011215209961, "end": **********.427468, "relative_end": **********.427468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.428299, "relative_start": 0.962332010269165, "end": **********.428299, "relative_end": **********.428299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.428872, "relative_start": 0.9629051685333252, "end": **********.428872, "relative_end": **********.428872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.429215, "relative_start": 0.9632480144500732, "end": **********.429215, "relative_end": **********.429215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.433292, "relative_start": 0.96732497215271, "end": **********.433292, "relative_end": **********.433292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.433973, "relative_start": 0.9680061340332031, "end": **********.433973, "relative_end": **********.433973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.434607, "relative_start": 0.9686400890350342, "end": **********.434607, "relative_end": **********.434607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.435233, "relative_start": 0.9692661762237549, "end": **********.435233, "relative_end": **********.435233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.435795, "relative_start": 0.9698281288146973, "end": **********.435795, "relative_end": **********.435795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.436615, "relative_start": 0.9706480503082275, "end": **********.436615, "relative_end": **********.436615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.437901, "relative_start": 0.9719340801239014, "end": **********.437901, "relative_end": **********.437901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.43846, "relative_start": 0.9724931716918945, "end": **********.43846, "relative_end": **********.43846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.438928, "relative_start": 0.9729609489440918, "end": **********.438928, "relative_end": **********.438928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.439575, "relative_start": 0.9736080169677734, "end": **********.439575, "relative_end": **********.439575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.440505, "relative_start": 0.9745380878448486, "end": **********.440505, "relative_end": **********.440505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.441126, "relative_start": 0.9751591682434082, "end": **********.441126, "relative_end": **********.441126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.441935, "relative_start": 0.9759681224822998, "end": **********.441935, "relative_end": **********.441935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.442645, "relative_start": 0.9766781330108643, "end": **********.442645, "relative_end": **********.442645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.443235, "relative_start": 0.9772679805755615, "end": **********.443235, "relative_end": **********.443235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.444665, "relative_start": 0.9786980152130127, "end": **********.444665, "relative_end": **********.444665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.44531, "relative_start": 0.9793431758880615, "end": **********.44531, "relative_end": **********.44531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.445857, "relative_start": 0.9798901081085205, "end": **********.445857, "relative_end": **********.445857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.448535, "relative_start": 0.9825680255889893, "end": **********.448535, "relative_end": **********.448535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.449269, "relative_start": 0.983302116394043, "end": **********.449269, "relative_end": **********.449269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.tag-form", "start": **********.450949, "relative_start": 0.9849820137023926, "end": **********.450949, "relative_end": **********.450949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.451997, "relative_start": 0.986030101776123, "end": **********.451997, "relative_end": **********.451997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.452627, "relative_start": 0.9866600036621094, "end": **********.452627, "relative_end": **********.452627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.453224, "relative_start": 0.9872570037841797, "end": **********.453224, "relative_end": **********.453224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.455086, "relative_start": 0.9891190528869629, "end": **********.455086, "relative_end": **********.455086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.456053, "relative_start": 0.9900860786437988, "end": **********.456053, "relative_end": **********.456053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.456738, "relative_start": 0.9907710552215576, "end": **********.456738, "relative_end": **********.456738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.457668, "relative_start": 0.9917011260986328, "end": **********.457668, "relative_end": **********.457668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.45824, "relative_start": 0.9922730922698975, "end": **********.45824, "relative_end": **********.45824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.459877, "relative_start": 0.9939100742340088, "end": **********.459877, "relative_end": **********.459877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.460597, "relative_start": 0.9946300983428955, "end": **********.460597, "relative_end": **********.460597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.461256, "relative_start": 0.9952890872955322, "end": **********.461256, "relative_end": **********.461256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.461681, "relative_start": 0.9957139492034912, "end": **********.461681, "relative_end": **********.461681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.465302, "relative_start": 0.9993350505828857, "end": **********.465302, "relative_end": **********.465302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.465931, "relative_start": 0.9999639987945557, "end": **********.465931, "relative_end": **********.465931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.466513, "relative_start": 1.0005459785461426, "end": **********.466513, "relative_end": **********.466513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.467153, "relative_start": 1.0011861324310303, "end": **********.467153, "relative_end": **********.467153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.467681, "relative_start": 1.0017139911651611, "end": **********.467681, "relative_end": **********.467681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.468518, "relative_start": 1.0025510787963867, "end": **********.468518, "relative_end": **********.468518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.469764, "relative_start": 1.0037970542907715, "end": **********.469764, "relative_end": **********.469764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.470316, "relative_start": 1.0043489933013916, "end": **********.470316, "relative_end": **********.470316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.470893, "relative_start": 1.0049259662628174, "end": **********.470893, "relative_end": **********.470893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.47156, "relative_start": 1.0055930614471436, "end": **********.47156, "relative_end": **********.47156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.472192, "relative_start": 1.0062251091003418, "end": **********.472192, "relative_end": **********.472192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.474291, "relative_start": 1.008324146270752, "end": **********.474291, "relative_end": **********.474291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.475788, "relative_start": 1.0098211765289307, "end": **********.475788, "relative_end": **********.475788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.47648, "relative_start": 1.0105130672454834, "end": **********.47648, "relative_end": **********.47648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.477026, "relative_start": 1.011059045791626, "end": **********.477026, "relative_end": **********.477026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.477679, "relative_start": 1.0117120742797852, "end": **********.477679, "relative_end": **********.477679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.478334, "relative_start": 1.0123670101165771, "end": **********.478334, "relative_end": **********.478334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.478888, "relative_start": 1.0129210948944092, "end": **********.478888, "relative_end": **********.478888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.479491, "relative_start": 1.013524055480957, "end": **********.479491, "relative_end": **********.479491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.481911, "relative_start": 1.015944004058838, "end": **********.481911, "relative_end": **********.481911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.482624, "relative_start": 1.0166571140289307, "end": **********.482624, "relative_end": **********.482624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.484119, "relative_start": 1.0181519985198975, "end": **********.484119, "relative_end": **********.484119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.484853, "relative_start": 1.0188860893249512, "end": **********.484853, "relative_end": **********.484853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.485567, "relative_start": 1.0196001529693604, "end": **********.485567, "relative_end": **********.485567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.486015, "relative_start": 1.0200481414794922, "end": **********.486015, "relative_end": **********.486015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.487506, "relative_start": 1.0215389728546143, "end": **********.487506, "relative_end": **********.487506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.488383, "relative_start": 1.022416114807129, "end": **********.488383, "relative_end": **********.488383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.490074, "relative_start": 1.0241069793701172, "end": **********.490074, "relative_end": **********.490074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.49155, "relative_start": 1.025583028793335, "end": **********.49155, "relative_end": **********.49155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.492523, "relative_start": 1.0265560150146484, "end": **********.492523, "relative_end": **********.492523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.493649, "relative_start": 1.0276820659637451, "end": **********.493649, "relative_end": **********.493649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.495498, "relative_start": 1.0295310020446777, "end": **********.495498, "relative_end": **********.495498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.496078, "relative_start": 1.0301110744476318, "end": **********.496078, "relative_end": **********.496078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.499521, "relative_start": 1.0335540771484375, "end": **********.499521, "relative_end": **********.499521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.500258, "relative_start": 1.0342910289764404, "end": **********.500258, "relative_end": **********.500258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.500854, "relative_start": 1.0348870754241943, "end": **********.500854, "relative_end": **********.500854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.501173, "relative_start": 1.0352060794830322, "end": **********.501173, "relative_end": **********.501173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.501609, "relative_start": 1.035642147064209, "end": **********.501609, "relative_end": **********.501609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.502106, "relative_start": 1.0361390113830566, "end": **********.502106, "relative_end": **********.502106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.502558, "relative_start": 1.0365910530090332, "end": **********.502558, "relative_end": **********.502558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.503897, "relative_start": 1.0379300117492676, "end": **********.503897, "relative_end": **********.503897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.504531, "relative_start": 1.0385639667510986, "end": **********.504531, "relative_end": **********.504531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.505749, "relative_start": 1.0397820472717285, "end": **********.505749, "relative_end": **********.505749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.50671, "relative_start": 1.040743112564087, "end": **********.50671, "relative_end": **********.50671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.507912, "relative_start": 1.041944980621338, "end": **********.507912, "relative_end": **********.507912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.tag-form", "start": **********.510119, "relative_start": 1.044152021408081, "end": **********.510119, "relative_end": **********.510119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.51122, "relative_start": 1.045253038406372, "end": **********.51122, "relative_end": **********.51122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.51187, "relative_start": 1.045902967453003, "end": **********.51187, "relative_end": **********.51187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.512394, "relative_start": 1.0464270114898682, "end": **********.512394, "relative_end": **********.512394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.516106, "relative_start": 1.0501389503479004, "end": **********.516106, "relative_end": **********.516106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.517034, "relative_start": 1.0510671138763428, "end": **********.517034, "relative_end": **********.517034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.517746, "relative_start": 1.05177903175354, "end": **********.517746, "relative_end": **********.517746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.518398, "relative_start": 1.0524311065673828, "end": **********.518398, "relative_end": **********.518398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.518857, "relative_start": 1.0528900623321533, "end": **********.518857, "relative_end": **********.518857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.520275, "relative_start": 1.0543081760406494, "end": **********.520275, "relative_end": **********.520275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.520981, "relative_start": 1.0550141334533691, "end": **********.520981, "relative_end": **********.520981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.523126, "relative_start": 1.0571589469909668, "end": **********.523126, "relative_end": **********.523126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.523777, "relative_start": 1.0578100681304932, "end": **********.523777, "relative_end": **********.523777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.526205, "relative_start": 1.0602381229400635, "end": **********.526205, "relative_end": **********.526205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.527247, "relative_start": 1.0612800121307373, "end": **********.527247, "relative_end": **********.527247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.52814, "relative_start": 1.0621731281280518, "end": **********.52814, "relative_end": **********.52814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.529201, "relative_start": 1.0632340908050537, "end": **********.529201, "relative_end": **********.529201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.531647, "relative_start": 1.0656800270080566, "end": **********.531647, "relative_end": **********.531647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.533303, "relative_start": 1.067336082458496, "end": **********.533303, "relative_end": **********.533303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.53566, "relative_start": 1.0696930885314941, "end": **********.53566, "relative_end": **********.53566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.536492, "relative_start": 1.0705251693725586, "end": **********.536492, "relative_end": **********.536492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.537695, "relative_start": 1.071727991104126, "end": **********.537695, "relative_end": **********.537695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.538576, "relative_start": 1.0726089477539062, "end": **********.538576, "relative_end": **********.538576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.539948, "relative_start": 1.0739810466766357, "end": **********.539948, "relative_end": **********.539948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.542426, "relative_start": 1.0764591693878174, "end": **********.542426, "relative_end": **********.542426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.544555, "relative_start": 1.0785880088806152, "end": **********.544555, "relative_end": **********.544555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.545876, "relative_start": 1.079909086227417, "end": **********.545876, "relative_end": **********.545876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.548894, "relative_start": 1.0829269886016846, "end": **********.548894, "relative_end": **********.548894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.550052, "relative_start": 1.0840849876403809, "end": **********.550052, "relative_end": **********.550052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.551128, "relative_start": 1.0851609706878662, "end": **********.551128, "relative_end": **********.551128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.551876, "relative_start": 1.085909128189087, "end": **********.551876, "relative_end": **********.551876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.552971, "relative_start": 1.0870039463043213, "end": **********.552971, "relative_end": **********.552971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.553787, "relative_start": 1.087820053100586, "end": **********.553787, "relative_end": **********.553787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.554303, "relative_start": 1.0883359909057617, "end": **********.554303, "relative_end": **********.554303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.556244, "relative_start": 1.0902769565582275, "end": **********.556244, "relative_end": **********.556244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.557063, "relative_start": 1.0910961627960205, "end": **********.557063, "relative_end": **********.557063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.557852, "relative_start": 1.0918850898742676, "end": **********.557852, "relative_end": **********.557852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.558252, "relative_start": 1.09228515625, "end": **********.558252, "relative_end": **********.558252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.559791, "relative_start": 1.0938241481781006, "end": **********.559791, "relative_end": **********.559791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.560418, "relative_start": 1.0944509506225586, "end": **********.560418, "relative_end": **********.560418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.561076, "relative_start": 1.095108985900879, "end": **********.561076, "relative_end": **********.561076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.561759, "relative_start": 1.0957920551300049, "end": **********.561759, "relative_end": **********.561759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.562387, "relative_start": 1.0964200496673584, "end": **********.562387, "relative_end": **********.562387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.566249, "relative_start": 1.1002819538116455, "end": **********.566249, "relative_end": **********.566249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.569088, "relative_start": 1.103121042251587, "end": **********.569088, "relative_end": **********.569088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.571049, "relative_start": 1.1050820350646973, "end": **********.571049, "relative_end": **********.571049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.572015, "relative_start": 1.1060481071472168, "end": **********.572015, "relative_end": **********.572015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.57295, "relative_start": 1.106982946395874, "end": **********.57295, "relative_end": **********.57295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.575387, "relative_start": 1.1094200611114502, "end": **********.575387, "relative_end": **********.575387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.576645, "relative_start": 1.11067795753479, "end": **********.576645, "relative_end": **********.576645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.577918, "relative_start": 1.1119511127471924, "end": **********.577918, "relative_end": **********.577918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.rows", "start": **********.583283, "relative_start": 1.1173160076141357, "end": **********.583283, "relative_end": **********.583283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.index", "start": **********.58446, "relative_start": 1.1184930801391602, "end": **********.58446, "relative_end": **********.58446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.587299, "relative_start": 1.1213321685791016, "end": **********.587299, "relative_end": **********.587299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.588307, "relative_start": 1.1223399639129639, "end": **********.588307, "relative_end": **********.588307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "start": **********.58936, "relative_start": 1.1233930587768555, "end": **********.58936, "relative_end": **********.58936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.600465, "relative_start": 1.134498119354248, "end": **********.600465, "relative_end": **********.600465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app", "start": **********.603909, "relative_start": 1.1379420757293701, "end": **********.603909, "relative_end": **********.603909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::main", "start": **********.605076, "relative_start": 1.1391091346740723, "end": **********.605076, "relative_end": **********.605076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app.sidebar", "start": **********.605907, "relative_start": 1.1399400234222412, "end": **********.605907, "relative_end": **********.605907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.head", "start": **********.610138, "relative_start": 1.1441709995269775, "end": **********.610138, "relative_end": **********.610138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle", "start": **********.61162, "relative_start": 1.1456530094146729, "end": **********.61162, "relative_end": **********.61162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.618219, "relative_start": 1.152251958847046, "end": **********.618219, "relative_end": **********.618219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.619578, "relative_start": 1.1536109447479248, "end": **********.619578, "relative_end": **********.619578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.620131, "relative_start": 1.1541640758514404, "end": **********.620131, "relative_end": **********.620131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.620665, "relative_start": 1.154698133468628, "end": **********.620665, "relative_end": **********.620665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.621241, "relative_start": 1.1552741527557373, "end": **********.621241, "relative_end": **********.621241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.app-logo", "start": **********.62275, "relative_start": 1.156783103942871, "end": **********.62275, "relative_end": **********.62275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": **********.623978, "relative_start": 1.1580109596252441, "end": **********.623978, "relative_end": **********.623978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.625044, "relative_start": 1.1590771675109863, "end": **********.625044, "relative_end": **********.625044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.layout-dashboard", "start": **********.625625, "relative_start": 1.1596579551696777, "end": **********.625625, "relative_end": **********.625625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.626233, "relative_start": 1.1602661609649658, "end": **********.626233, "relative_end": **********.626233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": **********.62707, "relative_start": 1.1611030101776123, "end": **********.62707, "relative_end": **********.62707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.628051, "relative_start": 1.1620841026306152, "end": **********.628051, "relative_end": **********.628051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.user", "start": **********.628836, "relative_start": 1.1628689765930176, "end": **********.628836, "relative_end": **********.628836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.629441, "relative_start": 1.1634740829467773, "end": **********.629441, "relative_end": **********.629441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": **********.633497, "relative_start": 1.1675300598144531, "end": **********.633497, "relative_end": **********.633497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.634552, "relative_start": 1.1685850620269775, "end": **********.634552, "relative_end": **********.634552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.currency-dollar", "start": **********.635395, "relative_start": 1.1694281101226807, "end": **********.635395, "relative_end": **********.635395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.636003, "relative_start": 1.1700360774993896, "end": **********.636003, "relative_end": **********.636003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": **********.636813, "relative_start": 1.1708459854125977, "end": **********.636813, "relative_end": **********.636813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.637773, "relative_start": 1.1718060970306396, "end": **********.637773, "relative_end": **********.637773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.scroll-text", "start": **********.638345, "relative_start": 1.1723780632019043, "end": **********.638345, "relative_end": **********.638345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.638888, "relative_start": 1.1729209423065186, "end": **********.638888, "relative_end": **********.638888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": **********.639706, "relative_start": 1.173738956451416, "end": **********.639706, "relative_end": **********.639706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.640642, "relative_start": 1.1746749877929688, "end": **********.640642, "relative_end": **********.640642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.queue-list", "start": **********.641457, "relative_start": 1.175490140914917, "end": **********.641457, "relative_end": **********.641457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.642057, "relative_start": 1.1760900020599365, "end": **********.642057, "relative_end": **********.642057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": **********.642871, "relative_start": 1.1769039630889893, "end": **********.642871, "relative_end": **********.642871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.643801, "relative_start": 1.1778340339660645, "end": **********.643801, "relative_end": **********.643801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.tags", "start": **********.644393, "relative_start": 1.1784260272979736, "end": **********.644393, "relative_end": **********.644393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.644977, "relative_start": 1.1790101528167725, "end": **********.644977, "relative_end": **********.644977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group", "start": **********.645757, "relative_start": 1.1797900199890137, "end": **********.645757, "relative_end": **********.645757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": **********.649122, "relative_start": 1.1831550598144531, "end": **********.649122, "relative_end": **********.649122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.650364, "relative_start": 1.1843969821929932, "end": **********.650364, "relative_end": **********.650364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.shield-check", "start": **********.651202, "relative_start": 1.1852350234985352, "end": **********.651202, "relative_end": **********.651202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.651777, "relative_start": 1.1858100891113281, "end": **********.651777, "relative_end": **********.651777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": **********.652548, "relative_start": 1.1865811347961426, "end": **********.652548, "relative_end": **********.652548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.65344, "relative_start": 1.1874730587005615, "end": **********.65344, "relative_end": **********.65344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.key", "start": **********.654158, "relative_start": 1.1881911754608154, "end": **********.654158, "relative_end": **********.654158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.654662, "relative_start": 1.188694953918457, "end": **********.654662, "relative_end": **********.654662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": **********.655398, "relative_start": 1.1894309520721436, "end": **********.655398, "relative_end": **********.655398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.656237, "relative_start": 1.190269947052002, "end": **********.656237, "relative_end": **********.656237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.users", "start": **********.656909, "relative_start": 1.1909420490264893, "end": **********.656909, "relative_end": **********.656909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.657414, "relative_start": 1.1914470195770264, "end": **********.657414, "relative_end": **********.657414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group", "start": **********.657909, "relative_start": 1.1919419765472412, "end": **********.657909, "relative_end": **********.657909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-down", "start": **********.658704, "relative_start": 1.192737102508545, "end": **********.658704, "relative_end": **********.658704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-right", "start": **********.659428, "relative_start": 1.1934609413146973, "end": **********.659428, "relative_end": **********.659428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index", "start": **********.659962, "relative_start": 1.1939949989318848, "end": **********.659962, "relative_end": **********.659962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.660433, "relative_start": 1.1944661140441895, "end": **********.660433, "relative_end": **********.660433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": **********.660903, "relative_start": 1.1949360370635986, "end": **********.660903, "relative_end": **********.660903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.662583, "relative_start": 1.1966161727905273, "end": **********.662583, "relative_end": **********.662583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.folder-git-2", "start": **********.663882, "relative_start": 1.1979150772094727, "end": **********.663882, "relative_end": **********.663882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.66492, "relative_start": 1.1989531517028809, "end": **********.66492, "relative_end": **********.66492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index", "start": **********.665595, "relative_start": 1.1996281147003174, "end": **********.665595, "relative_end": **********.665595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::profile", "start": **********.6666, "relative_start": 1.2006330490112305, "end": **********.6666, "relative_end": **********.6666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index", "start": **********.667509, "relative_start": 1.2015421390533447, "end": **********.667509, "relative_end": **********.667509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.668675, "relative_start": 1.2027080059051514, "end": **********.668675, "relative_end": **********.668675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.669174, "relative_start": 1.203207015991211, "end": **********.669174, "relative_end": **********.669174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.66967, "relative_start": 1.2037031650543213, "end": **********.66967, "relative_end": **********.66967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down", "start": **********.670182, "relative_start": 1.2042150497436523, "end": **********.670182, "relative_end": **********.670182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "start": **********.671061, "relative_start": 1.2050940990447998, "end": **********.671061, "relative_end": **********.671061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": **********.671547, "relative_start": 1.2055799961090088, "end": **********.671547, "relative_end": **********.671547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": **********.672192, "relative_start": 1.2062251567840576, "end": **********.672192, "relative_end": **********.672192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": **********.672995, "relative_start": 1.2070281505584717, "end": **********.672995, "relative_end": **********.672995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": **********.673679, "relative_start": 1.207712173461914, "end": **********.673679, "relative_end": **********.673679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.674405, "relative_start": 1.2084381580352783, "end": **********.674405, "relative_end": **********.674405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun", "start": **********.67512, "relative_start": 1.209153175354004, "end": **********.67512, "relative_end": **********.67512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": **********.675664, "relative_start": 1.2096970081329346, "end": **********.675664, "relative_end": **********.675664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": **********.676151, "relative_start": 1.210184097290039, "end": **********.676151, "relative_end": **********.676151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.676797, "relative_start": 1.2108299732208252, "end": **********.676797, "relative_end": **********.676797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon", "start": **********.677778, "relative_start": 1.2118110656738281, "end": **********.677778, "relative_end": **********.677778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": **********.678467, "relative_start": 1.2125000953674316, "end": **********.678467, "relative_end": **********.678467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": **********.678997, "relative_start": 1.2130300998687744, "end": **********.678997, "relative_end": **********.678997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.679651, "relative_start": 1.21368408203125, "end": **********.679651, "relative_end": **********.679651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop", "start": **********.682207, "relative_start": 1.2162401676177979, "end": **********.682207, "relative_end": **********.682207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index", "start": **********.682878, "relative_start": 1.2169110774993896, "end": **********.682878, "relative_end": **********.682878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented", "start": **********.683594, "relative_start": 1.2176270484924316, "end": **********.683594, "relative_end": **********.683594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.684218, "relative_start": 1.2182509899139404, "end": **********.684218, "relative_end": **********.684218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": **********.685046, "relative_start": 1.2190790176391602, "end": **********.685046, "relative_end": **********.685046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": **********.685453, "relative_start": 1.2194859981536865, "end": **********.685453, "relative_end": **********.685453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "start": **********.686218, "relative_start": 1.2202510833740234, "end": **********.686218, "relative_end": **********.686218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.687131, "relative_start": 1.2211639881134033, "end": **********.687131, "relative_end": **********.687131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog", "start": **********.687863, "relative_start": 1.2218961715698242, "end": **********.687863, "relative_end": **********.687863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.688425, "relative_start": 1.2224581241607666, "end": **********.688425, "relative_end": **********.688425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "start": **********.68898, "relative_start": 1.223013162612915, "end": **********.68898, "relative_end": **********.68898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": **********.689326, "relative_start": 1.2233591079711914, "end": **********.689326, "relative_end": **********.689326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": **********.689695, "relative_start": 1.2237279415130615, "end": **********.689695, "relative_end": **********.689695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "start": **********.69037, "relative_start": 1.2244031429290771, "end": **********.69037, "relative_end": **********.69037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.691201, "relative_start": 1.225234031677246, "end": **********.691201, "relative_end": **********.691201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle", "start": **********.691917, "relative_start": 1.225950002670288, "end": **********.691917, "relative_end": **********.691917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.692788, "relative_start": 1.226820945739746, "end": **********.692788, "relative_end": **********.692788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.index", "start": **********.693421, "relative_start": 1.2274539470672607, "end": **********.693421, "relative_end": **********.693421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::dropdown", "start": **********.693869, "relative_start": 1.2279021739959717, "end": **********.693869, "relative_end": **********.693869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.index", "start": **********.694355, "relative_start": 1.2283880710601807, "end": **********.694355, "relative_end": **********.694355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.backdrop", "start": **********.695283, "relative_start": 1.229315996170044, "end": **********.695283, "relative_end": **********.695283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle", "start": **********.698828, "relative_start": 1.232861042022705, "end": **********.698828, "relative_end": **********.698828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.699957, "relative_start": 1.233989953994751, "end": **********.699957, "relative_end": **********.699957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.701565, "relative_start": 1.235598087310791, "end": **********.701565, "relative_end": **********.701565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.panel-left", "start": **********.702184, "relative_start": 1.2362170219421387, "end": **********.702184, "relative_end": **********.702184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.702729, "relative_start": 1.2367620468139648, "end": **********.702729, "relative_end": **********.702729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.703306, "relative_start": 1.2373390197753906, "end": **********.703306, "relative_end": **********.703306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.703849, "relative_start": 1.237882137298584, "end": **********.703849, "relative_end": **********.703849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::profile", "start": **********.704329, "relative_start": 1.2383620738983154, "end": **********.704329, "relative_end": **********.704329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index", "start": **********.705056, "relative_start": 1.239089012145996, "end": **********.705056, "relative_end": **********.705056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.706177, "relative_start": 1.2402100563049316, "end": **********.706177, "relative_end": **********.706177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.706695, "relative_start": 1.2407281398773193, "end": **********.706695, "relative_end": **********.706695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.707439, "relative_start": 1.2414720058441162, "end": **********.707439, "relative_end": **********.707439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down", "start": **********.708052, "relative_start": 1.2420849800109863, "end": **********.708052, "relative_end": **********.708052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "start": **********.70867, "relative_start": 1.2427029609680176, "end": **********.70867, "relative_end": **********.70867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": **********.709016, "relative_start": 1.243049144744873, "end": **********.709016, "relative_end": **********.709016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": **********.709562, "relative_start": 1.2435951232910156, "end": **********.709562, "relative_end": **********.709562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": **********.710156, "relative_start": 1.2441890239715576, "end": **********.710156, "relative_end": **********.710156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": **********.710724, "relative_start": 1.2447571754455566, "end": **********.710724, "relative_end": **********.710724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.711422, "relative_start": 1.245455026626587, "end": **********.711422, "relative_end": **********.711422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun", "start": **********.71192, "relative_start": 1.24595308303833, "end": **********.71192, "relative_end": **********.71192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": **********.712377, "relative_start": 1.2464101314544678, "end": **********.712377, "relative_end": **********.712377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": **********.712844, "relative_start": 1.2468769550323486, "end": **********.712844, "relative_end": **********.712844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.715045, "relative_start": 1.2490780353546143, "end": **********.715045, "relative_end": **********.715045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon", "start": **********.715618, "relative_start": 1.2496509552001953, "end": **********.715618, "relative_end": **********.715618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": **********.716131, "relative_start": 1.2501640319824219, "end": **********.716131, "relative_end": **********.716131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": **********.716611, "relative_start": 1.2506439685821533, "end": **********.716611, "relative_end": **********.716611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.717339, "relative_start": 1.2513720989227295, "end": **********.717339, "relative_end": **********.717339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop", "start": **********.717826, "relative_start": 1.2518589496612549, "end": **********.717826, "relative_end": **********.717826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index", "start": **********.718293, "relative_start": 1.2523260116577148, "end": **********.718293, "relative_end": **********.718293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented", "start": **********.718786, "relative_start": 1.2528190612792969, "end": **********.718786, "relative_end": **********.718786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.719329, "relative_start": 1.2533621788024902, "end": **********.719329, "relative_end": **********.719329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": **********.720135, "relative_start": 1.2541680335998535, "end": **********.720135, "relative_end": **********.720135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": **********.720506, "relative_start": 1.2545390129089355, "end": **********.720506, "relative_end": **********.720506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "start": **********.721025, "relative_start": 1.2550580501556396, "end": **********.721025, "relative_end": **********.721025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.72206, "relative_start": 1.2560930252075195, "end": **********.72206, "relative_end": **********.72206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog", "start": **********.722676, "relative_start": 1.256709098815918, "end": **********.722676, "relative_end": **********.722676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.723218, "relative_start": 1.2572510242462158, "end": **********.723218, "relative_end": **********.723218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "start": **********.723784, "relative_start": 1.257817029953003, "end": **********.723784, "relative_end": **********.723784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": **********.724121, "relative_start": 1.2581541538238525, "end": **********.724121, "relative_end": **********.724121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": **********.724503, "relative_start": 1.2585361003875732, "end": **********.724503, "relative_end": **********.724503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "start": **********.725084, "relative_start": 1.2591171264648438, "end": **********.725084, "relative_end": **********.725084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.725954, "relative_start": 1.2599871158599854, "end": **********.725954, "relative_end": **********.725954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle", "start": **********.726449, "relative_start": 1.2604820728302002, "end": **********.726449, "relative_end": **********.726449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.726919, "relative_start": 1.2609519958496094, "end": **********.726919, "relative_end": **********.726919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.index", "start": **********.727446, "relative_start": 1.261479139328003, "end": **********.727446, "relative_end": **********.727446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::dropdown", "start": **********.727835, "relative_start": 1.2618680000305176, "end": **********.727835, "relative_end": **********.727835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::header", "start": **********.728281, "relative_start": 1.2623140811920166, "end": **********.728281, "relative_end": **********.728281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::toast.index", "start": **********.728955, "relative_start": 1.2629880905151367, "end": **********.728955, "relative_end": **********.728955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.734891, "relative_start": 1.2689239978790283, "end": **********.735068, "relative_end": **********.735068, "duration": 0.00017714500427246094, "duration_str": "177μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 30696792, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 373, "nb_templates": 373, "templates": [{"name": "1x livewire.tag-table", "param_count": null, "params": [], "start": **********.304661, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/tag-table.blade.phplivewire.tag-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Ftag-table.blade.php&line=1", "ajax": false, "filename": "tag-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.tag-table"}, {"name": "8x e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "param_count": null, "params": [], "start": **********.308786, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 8, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::heading"}, {"name": "31x e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "param_count": null, "params": [], "start": **********.310271, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 31, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button.index"}, {"name": "47x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "param_count": null, "params": [], "start": **********.312813, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 47, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "param_count": null, "params": [], "start": **********.31579, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/plus.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fplus.blade.php&line=1", "ajax": false, "filename": "plus.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus"}, {"name": "47x e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "param_count": null, "params": [], "start": **********.316534, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}, "render_count": 47, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link"}, {"name": "33x e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "param_count": null, "params": [], "start": **********.31726, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}, "render_count": 33, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip"}, {"name": "7x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "param_count": null, "params": [], "start": **********.317995, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/trigger.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Ftrigger.blade.php&line=1", "ajax": false, "filename": "trigger.blade.php", "line": "?"}, "render_count": 7, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger"}, {"name": "4x livewire.tag-form", "param_count": null, "params": [], "start": **********.320453, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/tag-form.blade.phplivewire.tag-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Ftag-form.blade.php&line=1", "ajax": false, "filename": "tag-form.blade.php", "line": "?"}, "render_count": 4, "name_original": "livewire.tag-form"}, {"name": "4x e60dd9d2c3a62d619c9acb38f20d5aa5::label", "param_count": null, "params": [], "start": **********.324293, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 4, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::label"}, {"name": "5x e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "param_count": null, "params": [], "start": **********.325643, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.index"}, {"name": "7x e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "param_count": null, "params": [], "start": **********.330937, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}, "render_count": 7, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-field"}, {"name": "4x e60dd9d2c3a62d619c9acb38f20d5aa5::error", "param_count": null, "params": [], "start": **********.333178, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 4, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::error"}, {"name": "4x e60dd9d2c3a62d619c9acb38f20d5aa5::field", "param_count": null, "params": [], "start": **********.334062, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 4, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::field"}, {"name": "9x e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "param_count": null, "params": [], "start": **********.33509, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/spacer.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fspacer.blade.php&line=1", "ajax": false, "filename": "spacer.blade.php", "line": "?"}, "render_count": 9, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::spacer"}, {"name": "14x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "param_count": null, "params": [], "start": **********.338579, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/close.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 14, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close"}, {"name": "8x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "param_count": null, "params": [], "start": **********.341894, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 8, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading"}, {"name": "7x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "param_count": null, "params": [], "start": **********.343885, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 7, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index"}, {"name": "9x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "param_count": null, "params": [], "start": **********.350495, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/x-mark.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fx-mark.blade.php&line=1", "ajax": false, "filename": "x-mark.blade.php", "line": "?"}, "render_count": 9, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "param_count": null, "params": [], "start": **********.358727, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/magnifying-glass.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmagnifying-glass.blade.php&line=1", "ajax": false, "filename": "magnifying-glass.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "param_count": null, "params": [], "start": **********.36073, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/clearable.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Fclearable.blade.php&line=1", "ajax": false, "filename": "clearable.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable"}, {"name": "2x components.table.column", "param_count": null, "params": [], "start": **********.370414, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/column.blade.phpcomponents.table.column", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.table.column"}, {"name": "1x components.table.columns", "param_count": null, "params": [], "start": **********.372011, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/columns.blade.phpcomponents.table.columns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcolumns.blade.php&line=1", "ajax": false, "filename": "columns.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.columns"}, {"name": "6x components.table.cell", "param_count": null, "params": [], "start": **********.372964, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/cell.blade.phpcomponents.table.cell", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}, "render_count": 6, "name_original": "components.table.cell"}, {"name": "3x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "param_count": null, "params": [], "start": **********.37631, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/pencil-square.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fpencil-square.blade.php&line=1", "ajax": false, "filename": "pencil-square.blade.php", "line": "?"}, "render_count": 3, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square"}, {"name": "3x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "param_count": null, "params": [], "start": **********.418657, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/trash.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Ftrash.blade.php&line=1", "ajax": false, "filename": "trash.blade.php", "line": "?"}, "render_count": 3, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash"}, {"name": "3x e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "param_count": null, "params": [], "start": **********.423479, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/subheading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsubheading.blade.php&line=1", "ajax": false, "filename": "subheading.blade.php", "line": "?"}, "render_count": 3, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::subheading"}, {"name": "3x components.table.row", "param_count": null, "params": [], "start": **********.441876, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/row.blade.phpcomponents.table.row", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}, "render_count": 3, "name_original": "components.table.row"}, {"name": "1x components.table.rows", "param_count": null, "params": [], "start": **********.58317, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/rows.blade.phpcomponents.table.rows", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Frows.blade.php&line=1", "ajax": false, "filename": "rows.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.rows"}, {"name": "1x components.table.index", "param_count": null, "params": [], "start": **********.584367, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/index.blade.phpcomponents.table.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.index"}, {"name": "1x livewire::tailwind", "param_count": null, "params": [], "start": **********.587237, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire::tailwind"}, {"name": "1x components.card", "param_count": null, "params": [], "start": **********.588198, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.card"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "param_count": null, "params": [], "start": **********.589297, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/card/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::card.index"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.600348, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x components.layouts.app", "param_count": null, "params": [], "start": **********.603849, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app.blade.phpcomponents.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::main", "param_count": null, "params": [], "start": **********.605011, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/main.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::main", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmain.blade.php&line=1", "ajax": false, "filename": "main.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::main"}, {"name": "1x components.layouts.app.sidebar", "param_count": null, "params": [], "start": **********.60582, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app/sidebar.blade.phpcomponents.layouts.app.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app.sidebar"}, {"name": "1x partials.head", "param_count": null, "params": [], "start": **********.610074, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/partials/head.blade.phppartials.head", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fpartials%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.head"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle", "param_count": null, "params": [], "start": **********.61156, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/toggle.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Ftoggle.blade.php&line=1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle"}, {"name": "1x components.app-logo", "param_count": null, "params": [], "start": **********.622692, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/app-logo.blade.phpcomponents.app-logo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fapp-logo.blade.php&line=1", "ajax": false, "filename": "app-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.app-logo"}, {"name": "10x e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "param_count": null, "params": [], "start": **********.623921, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/item.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 10, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.layout-dashboard", "param_count": null, "params": [], "start": **********.625569, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/layout-dashboard.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.layout-dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Flayout-dashboard.blade.php&line=1", "ajax": false, "filename": "layout-dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.layout-dashboard"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.user", "param_count": null, "params": [], "start": **********.628776, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/user.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.user", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.user"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.currency-dollar", "param_count": null, "params": [], "start": **********.635339, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/currency-dollar.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.currency-dollar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcurrency-dollar.blade.php&line=1", "ajax": false, "filename": "currency-dollar.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.currency-dollar"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.scroll-text", "param_count": null, "params": [], "start": **********.63829, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/scroll-text.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.scroll-text", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fscroll-text.blade.php&line=1", "ajax": false, "filename": "scroll-text.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.scroll-text"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.queue-list", "param_count": null, "params": [], "start": **********.641398, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/queue-list.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.queue-list", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fqueue-list.blade.php&line=1", "ajax": false, "filename": "queue-list.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.queue-list"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.tags", "param_count": null, "params": [], "start": **********.644336, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/tags.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.tags", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ftags.blade.php&line=1", "ajax": false, "filename": "tags.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.tags"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group", "param_count": null, "params": [], "start": **********.645578, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/navlist/group.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.shield-check", "param_count": null, "params": [], "start": **********.651148, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/shield-check.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.shield-check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fshield-check.blade.php&line=1", "ajax": false, "filename": "shield-check.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.shield-check"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.key", "param_count": null, "params": [], "start": **********.654105, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/key.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.key", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fkey.blade.php&line=1", "ajax": false, "filename": "key.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.key"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.users", "param_count": null, "params": [], "start": **********.656858, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/users.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.users", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fusers.blade.php&line=1", "ajax": false, "filename": "users.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.users"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-down", "param_count": null, "params": [], "start": **********.65865, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-down.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-down"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-right", "param_count": null, "params": [], "start": **********.659376, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-right.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-right", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-right.blade.php&line=1", "ajax": false, "filename": "chevron-right.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-right"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index", "param_count": null, "params": [], "start": **********.659909, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.folder-git-2", "param_count": null, "params": [], "start": **********.66379, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/folder-git-2.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.folder-git-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ffolder-git-2.blade.php&line=1", "ajax": false, "filename": "folder-git-2.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.folder-git-2"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::profile", "param_count": null, "params": [], "start": **********.66655, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/profile.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::profile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fprofile.blade.php&line=1", "ajax": false, "filename": "profile.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::profile"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index", "param_count": null, "params": [], "start": **********.667457, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/avatar/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Favatar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down", "param_count": null, "params": [], "start": **********.670129, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/chevrons-up-down.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevrons-up-down.blade.php&line=1", "ajax": false, "filename": "chevrons-up-down.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down"}, {"name": "4x e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "param_count": null, "params": [], "start": **********.671007, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/radio/group.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fradio%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 4, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group"}, {"name": "6x e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "param_count": null, "params": [], "start": **********.671492, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/separator.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator"}, {"name": "6x e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "param_count": null, "params": [], "start": **********.672139, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/separator.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::separator"}, {"name": "6x e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "param_count": null, "params": [], "start": **********.672942, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index"}, {"name": "6x e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "param_count": null, "params": [], "start": **********.673626, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/variants/segmented.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 6, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun", "param_count": null, "params": [], "start": **********.675066, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/sun.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon", "param_count": null, "params": [], "start": **********.677722, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/moon.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop", "param_count": null, "params": [], "start": **********.682153, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/computer-desktop.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index", "param_count": null, "params": [], "start": **********.682826, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented", "param_count": null, "params": [], "start": **********.683542, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/variants/segmented.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented"}, {"name": "4x e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "param_count": null, "params": [], "start": **********.686167, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/item.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog", "param_count": null, "params": [], "start": **********.68781, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/cog.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcog.blade.php&line=1", "ajax": false, "filename": "cog.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle", "param_count": null, "params": [], "start": **********.691863, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/arrow-right-start-on-rectangle.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Farrow-right-start-on-rectangle.blade.php&line=1", "ajax": false, "filename": "arrow-right-start-on-rectangle.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::menu.index", "param_count": null, "params": [], "start": **********.693366, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::menu.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::menu.index"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::dropdown", "param_count": null, "params": [], "start": **********.693818, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/dropdown.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::dropdown"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.index", "param_count": null, "params": [], "start": **********.694302, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.backdrop", "param_count": null, "params": [], "start": **********.695228, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/backdrop.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.backdrop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Fbackdrop.blade.php&line=1", "ajax": false, "filename": "backdrop.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.backdrop"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.panel-left", "param_count": null, "params": [], "start": **********.702129, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/panel-left.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.panel-left", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fpanel-left.blade.php&line=1", "ajax": false, "filename": "panel-left.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.panel-left"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::header", "param_count": null, "params": [], "start": **********.728225, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/header.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::header"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::toast.index", "param_count": null, "params": [], "start": **********.728901, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/toast/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::toast.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ftoast%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::toast.index"}]}, "queries": {"count": 5, "nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.006840000000000001, "accumulated_duration_str": "6.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 131}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 111}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 60}, {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 88}], "start": **********.145886, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CacheManager.php:245", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheManager.php&line=245", "ajax": false, "filename": "CacheManager.php", "line": "245"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn' limit 1", "type": "query", "params": [], "bindings": ["dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.210384, "duration": 0.00445, "duration_str": "4.45ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 65.058}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.262037, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 65.058, "width_percent": 11.988}, {"sql": "select count(*) as aggregate from `tags` where `tags`.`user_id` = 1 and `tags`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Livewire/TagTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TagTable.php", "line": 52}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2875671, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "TagTable.php:52", "source": {"index": 19, "namespace": null, "name": "app/Livewire/TagTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TagTable.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTagTable.php&line=52", "ajax": false, "filename": "TagTable.php", "line": "52"}, "connection": "daily", "explain": null, "start_percent": 77.047, "width_percent": 12.427}, {"sql": "select `id`, `name` from `tags` where `tags`.`user_id` = 1 and `tags`.`user_id` is not null order by `name` asc limit 15 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Livewire/TagTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TagTable.php", "line": 52}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.292153, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "TagTable.php:52", "source": {"index": 19, "namespace": null, "name": "app/Livewire/TagTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TagTable.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTagTable.php&line=52", "ajax": false, "filename": "TagTable.php", "line": "52"}, "connection": "daily", "explain": null, "start_percent": 89.474, "width_percent": 10.526}]}, "models": {"data": {"App\\Models\\Tag": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FTag.php&line=1", "ajax": false, "filename": "Tag.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 4, "is_counter": true}, "livewire": {"data": {"tag-table #An3j0NFWMOZi0Xvv2IVR": "array:4 [\n  \"data\" => array:2 [\n    \"search\" => \"\"\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"tag-table\"\n  \"component\" => \"App\\Livewire\\TagTable\"\n  \"id\" => \"An3j0NFWMOZi0Xvv2IVR\"\n]", "tag-form #VpFjA11FfvBCrCgheTBn": "array:4 [\n  \"data\" => array:3 [\n    \"show_tag_form\" => false\n    \"tag\" => null\n    \"name\" => \"\"\n  ]\n  \"name\" => \"tag-form\"\n  \"component\" => \"App\\Livewire\\TagForm\"\n  \"id\" => \"VpFjA11FfvBCrCgheTBn\"\n]", "tag-form #Zt9YdOcpUiOOIlhuGkxK": "array:4 [\n  \"data\" => array:3 [\n    \"show_tag_form\" => false\n    \"tag\" => App\\Models\\Tag {#1739\n      #connection: \"mysql\"\n      #table: \"tags\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:2 [\n        \"id\" => 2\n        \"name\" => \"Bills\"\n      ]\n      #original: array:2 [\n        \"id\" => 2\n        \"name\" => \"Bills\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"name\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"name\" => \"Bills\"\n  ]\n  \"name\" => \"tag-form\"\n  \"component\" => \"App\\Livewire\\TagForm\"\n  \"id\" => \"Zt9YdOcpUiOOIlhuGkxK\"\n]", "tag-form #Jbg7DFWG2qa6PmK0EdSC": "array:4 [\n  \"data\" => array:3 [\n    \"show_tag_form\" => false\n    \"tag\" => App\\Models\\Tag {#1733\n      #connection: \"mysql\"\n      #table: \"tags\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:2 [\n        \"id\" => 3\n        \"name\" => \"Entertainment\"\n      ]\n      #original: array:2 [\n        \"id\" => 3\n        \"name\" => \"Entertainment\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"name\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"name\" => \"Entertainment\"\n  ]\n  \"name\" => \"tag-form\"\n  \"component\" => \"App\\Livewire\\TagForm\"\n  \"id\" => \"Jbg7DFWG2qa6PmK0EdSC\"\n]", "tag-form #SsduCAEoxCKh2ZImybom": "array:4 [\n  \"data\" => array:3 [\n    \"show_tag_form\" => false\n    \"tag\" => App\\Models\\Tag {#1734\n      #connection: \"mysql\"\n      #table: \"tags\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:2 [\n        \"id\" => 1\n        \"name\" => \"Groceries\"\n      ]\n      #original: array:2 [\n        \"id\" => 1\n        \"name\" => \"Groceries\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"name\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"name\" => \"Groceries\"\n  ]\n  \"name\" => \"tag-form\"\n  \"component\" => \"App\\Livewire\\TagForm\"\n  \"id\" => \"SsduCAEoxCKh2ZImybom\"\n]"}, "count": 5}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/tags", "action_name": "tags", "controller_action": "App\\Livewire\\TagTable", "uri": "GET tags", "controller": "App\\Livewire\\TagTable@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTagTable.php&line=41\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTagTable.php&line=41\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/TagTable.php:41-54</a>", "middleware": "web, auth", "duration": "1.27s", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2015310935 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2015310935\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-614456334 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-614456334\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1100627489 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/tags</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ilk0Yng1eDRCL1JERUJUYnFwK0ZrZ0E9PSIsInZhbHVlIjoiRFZjMFlGdWRUYkxlaDJQL2pOdTZraGlPb1Q5S2N3QkwrVXNZdzRGa3k4d3RTMXFUTk5uaExmOEh3UkRNR1FwMmluSGpTRXVJWi9QYnBLQmFGVXZxSmlPcDN1b0VtQk1yZ2RmSDJtU3p5OWQ1dHZubGZRbnpzcnUxbHJnM0RFMzkiLCJtYWMiOiJmMTQ1YTI5MDlmZGM0MjNiMmJkNjkyZmZkZGRlZDAyMmNkMDJkZjI2OTliOTFkOTRkNWI5NmIyZDJhN2M2NjJmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjVyR2VyOE5leUdVR3Rxb25CSlM3alE9PSIsInZhbHVlIjoiMmpsaitoQlZyUUgySFkxUUxYemhlTEkzTU9QcmZvN2c0RGxRMkplMEVCM2pIZ2hJUExTT1ZaQ0F4NFhzaGp6QjRJNktZMXVGa2xLRlJxRmFUUjRzVTFJRE9QOHVBdVVzSUZXc1N3ZlRUQ05aN2czOFMvT3FOU0gzcFpDWTMzQ0EiLCJtYWMiOiIzYWI4ZTU2NzFlY2YyZTBmYTk0NzBmOTgyODk3NDc3ZTU5YTZmY2FiY2UxNzBjMzQ5ODlkNjUxNTJmNmE1Nzk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1100627489\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-367923376 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367923376\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1469446816 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 26 May 2025 08:35:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1469446816\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1410786993 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/tags</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1410786993\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/tags", "action_name": "tags", "controller_action": "App\\Livewire\\TagTable"}, "badge": null}}