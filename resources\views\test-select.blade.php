<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Select</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-8 bg-gray-100">
    <h1 class="text-2xl font-bold mb-4">Select Component Test</h1>
    
    <div class="space-y-4 max-w-md">
        <div>
            <label class="block text-sm font-medium mb-2">Test Native Select</label>
            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="" disabled selected>Choose an option</option>
                <option value="1">Option 1</option>
                <option value="2">Option 2</option>
                <option value="3">Option 3</option>
            </select>
        </div>
        
        <div>
            <label class="block text-sm font-medium mb-2">Test Custom Component</label>
            <x-select placeholder="Choose an option">
                <x-option value="1">Option 1</x-option>
                <x-option value="2">Option 2</x-option>
                <x-option value="3">Option 3</x-option>
            </x-select>
        </div>
        
        <div>
            <label class="block text-sm font-medium mb-2">Test Multiple Select</label>
            <x-select multiple>
                <x-option value="1">Tag 1</x-option>
                <x-option value="2">Tag 2</x-option>
                <x-option value="3">Tag 3</x-option>
            </x-select>
        </div>
    </div>
</body>
</html>
