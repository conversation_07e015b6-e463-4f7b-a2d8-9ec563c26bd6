{"$schema": "https://getcomposer.org/schema.json", "name": "nickdillon/pure-finance", "type": "project", "description": "Pure Finance", "license": "MIT", "require": {"php": "^8.4", "aws/aws-sdk-php": "^3.342", "cviebrock/eloquent-sluggable": "^12.0", "intervention/image-laravel": "^1.5", "laravel/framework": "^12.0", "laravel/tinker": "^2.10.1", "league/flysystem-aws-s3-v3": "^3.0", "livewire/flux": "^2.1.1", "livewire/volt": "^1.6.7", "spatie/image": "^3.8"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "fakerphp/faker": "^1.23", "laravel/pail": "^1.2.2", "laravel/pint": "^1.18", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.7", "pestphp/pest-plugin-drift": "^3.0", "pestphp/pest-plugin-laravel": "^3.1", "pestphp/pest-plugin-livewire": "^3.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}}}