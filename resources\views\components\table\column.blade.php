@props([
    'sortable' => false,
    'sorted' => false,
    'direction' => 'asc',
    'align' => 'left',
    'class' => '',
])

@php
$alignClasses = [
    'left' => 'text-left',
    'center' => 'text-center',
    'right' => 'text-right',
    'end' => 'text-right',
];

$classes = 'px-3 py-3 text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider border-b border-zinc-200 dark:border-white/20 ' . 
           ($alignClasses[$align] ?? 'text-left') . ' ' . 
           ($sortable ? 'cursor-pointer hover:bg-zinc-100 dark:hover:bg-zinc-700 select-none' : '') . ' ' . 
           $class;
@endphp

<th {{ $attributes->merge(['class' => $classes]) }}>
    <div class="flex items-center {{ $align === 'end' || $align === 'right' ? 'justify-end' : ($align === 'center' ? 'justify-center' : 'justify-start') }}">
        {{ $slot }}
        
        @if($sortable)
            <div class="ml-2 flex flex-col">
                @if($sorted && $direction === 'asc')
                    <svg class="w-3 h-3 text-zinc-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                    </svg>
                @elseif($sorted && $direction === 'desc')
                    <svg class="w-3 h-3 text-zinc-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                @else
                    <svg class="w-3 h-3 text-zinc-300 dark:text-zinc-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5 12l5-5 5 5H5z"></path>
                    </svg>
                @endif
            </div>
        @endif
    </div>
</th>
