<div class="space-y-4 mx-auto max-w-6xl">
    <div class="flex items-center justify-between">
        <flux:heading size="xl">
            Permissions
        </flux:heading>

        <div>
            <flux:modal.trigger name="permission-form">
                <flux:button icon="plus" variant="primary" size="sm">
                    Add Permission
                </flux:button>
            </flux:modal.trigger>
            
            <livewire:permission-form data-name="permission-form" />
        </div>
    </div>

    <x-card>
        <x-slot:content>
            <div class="p-3">
                <flux:input icon="magnifying-glass" placeholder="Search permissions..." wire:model.live.debounce.300ms='search' clearable />
            </div>

            @if ($permissions->count() > 0)
                <x-table :paginate="$permissions" class="border-t border-zinc-200 dark:border-white/20">
                    <x-table.columns class="[&>tr>th]:px-3! bg-zinc-50 dark:bg-zinc-800">
                        <x-table.column>
                            Name
                        </x-table.column>

                        <x-table.column>
                            Guard
                        </x-table.column>

                        <x-table.column>
                            Roles
                        </x-table.column>

                        <x-table.column>
                            Users
                        </x-table.column>

                        <x-table.column class="[&>div]:justify-end!">
                            Actions
                        </x-table.column>
                    </x-table.columns>

                    <x-table.rows class="dark:bg-zinc-900">
                        @foreach ($permissions as $permission)
                            <x-table.row :key="$permission->id" class="[&>td]:px-3! [&>td]:py-2!">
                                <x-table.cell class="whitespace-nowrap" variant="strong">
                                    {{ $permission->name }}
                                </x-table.cell>

                                <x-table.cell class="whitespace-nowrap">
                                    {{ $permission->guard_name }}
                                </x-table.cell>

                                <x-table.cell class="whitespace-nowrap">
                                    <flux:badge color="blue" size="sm">
                                        {{ $permission->roles_count }} roles
                                    </flux:badge>
                                </x-table.cell>

                                <x-table.cell class="whitespace-nowrap">
                                    <flux:badge color="green" size="sm">
                                        {{ $permission->users_count }} users
                                    </flux:badge>
                                </x-table.cell>

                                <x-table.cell class="[&>div]:justify-end!">
                                    <div class="flex items-center">
                                        <div>
                                            <flux:modal.trigger name="edit-permission-{{ $permission->id }}">
                                                <flux:button icon="pencil-square" variant="ghost" size="sm"
                                                    class="text-indigo-500!" 
                                                    x-on:click="$dispatch('load-permission', { permission: {{ $permission }} })" />
                                            </flux:modal.trigger>

                                            <livewire:permission-form data-name="edit-permission-{{ $permission->id }}" :key="$permission->id" />
                                        </div>

                                        <div>
                                            <flux:modal.trigger name="delete-permission-{{ $permission->id }}">
                                                <flux:button icon="trash" variant="ghost" size="sm"
                                                    class="text-red-500!" />
                                            </flux:modal.trigger>

                                            <flux:modal name="delete-permission-{{ $permission->id }}" class="min-w-[22rem]">
                                                <form wire:submit="delete({{ $permission->id }})" class="space-y-6">
                                                    <div class="space-y-4!">
                                                        <flux:heading size="lg" class="font-semibold -mt-1.5!">
                                                            Delete Permission
                                                        </flux:heading>

                                                        <flux:subheading>
                                                            Are you sure you want to delete the "{{ $permission->name }}" permission? This action cannot be undone.
                                                        </flux:subheading>
                                                    </div>

                                                    <div class="flex gap-2">
                                                        <flux:spacer />

                                                        <flux:modal.close>
                                                            <flux:button variant="ghost" size="sm">
                                                                Cancel
                                                            </flux:button>
                                                        </flux:modal.close>

                                                        <flux:button type="submit" variant="danger" size="sm">
                                                            Confirm
                                                        </flux:button>
                                                    </div>
                                                </form>
                                            </flux:modal>
                                        </div>
                                    </div>
                                </x-table.cell>
                            </x-table.row>
                        @endforeach
                    </x-table.rows>
                </x-table>
            @else
                <flux:heading class="italic! font-medium text-center pb-3">
                    No permissions found...
                </flux:heading>
            @endif
        </x-slot:content>
    </x-card>
</div>
