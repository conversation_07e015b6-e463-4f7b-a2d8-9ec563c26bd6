{"__meta": {"id": "01JW5WTTDT2Q97HBDDHH0C16MG", "datetime": "2025-05-26 08:55:00", "utime": **********.795221, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748249699.978591, "end": **********.795238, "duration": 0.8166470527648926, "duration_str": "817ms", "measures": [{"label": "Booting", "start": 1748249699.978591, "relative_start": 0, "end": **********.473486, "relative_end": **********.473486, "duration": 0.*****************, "duration_str": "495ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.473505, "relative_start": 0.*****************, "end": **********.79524, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "322ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.537344, "relative_start": 0.****************, "end": **********.541687, "relative_end": **********.541687, "duration": 0.0043430328369140625, "duration_str": "4.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.category-table", "start": **********.674976, "relative_start": 0.****************, "end": **********.674976, "relative_end": **********.674976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.680219, "relative_start": 0.****************, "end": **********.680219, "relative_end": **********.680219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.681638, "relative_start": 0.7030470371246338, "end": **********.681638, "relative_end": **********.681638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.683861, "relative_start": 0.7052700519561768, "end": **********.683861, "relative_end": **********.683861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "start": **********.684732, "relative_start": 0.7061409950256348, "end": **********.684732, "relative_end": **********.684732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.68567, "relative_start": 0.7070789337158203, "end": **********.68567, "relative_end": **********.68567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.687067, "relative_start": 0.7084760665893555, "end": **********.687067, "relative_end": **********.687067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.687934, "relative_start": 0.7093429565429688, "end": **********.687934, "relative_end": **********.687934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.688842, "relative_start": 0.7102510929107666, "end": **********.688842, "relative_end": **********.688842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.69185, "relative_start": 0.7132589817047119, "end": **********.69185, "relative_end": **********.69185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "start": **********.692863, "relative_start": 0.7142720222473145, "end": **********.692863, "relative_end": **********.692863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.694323, "relative_start": 0.7157320976257324, "end": **********.694323, "relative_end": **********.694323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.695281, "relative_start": 0.7166900634765625, "end": **********.695281, "relative_end": **********.695281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "start": **********.696119, "relative_start": 0.7175281047821045, "end": **********.696119, "relative_end": **********.696119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.697048, "relative_start": 0.7184569835662842, "end": **********.697048, "relative_end": **********.697048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.697663, "relative_start": 0.7190721035003662, "end": **********.697663, "relative_end": **********.697663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.699004, "relative_start": 0.7204129695892334, "end": **********.699004, "relative_end": **********.699004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.699593, "relative_start": 0.7210021018981934, "end": **********.699593, "relative_end": **********.699593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.700142, "relative_start": 0.7215509414672852, "end": **********.700142, "relative_end": **********.700142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": **********.701244, "relative_start": 0.7226531505584717, "end": **********.701244, "relative_end": **********.701244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": **********.701855, "relative_start": 0.7232639789581299, "end": **********.701855, "relative_end": **********.701855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": **********.703406, "relative_start": 0.7248151302337646, "end": **********.703406, "relative_end": **********.703406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.columns", "start": **********.704363, "relative_start": 0.7257721424102783, "end": **********.704363, "relative_end": **********.704363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.705695, "relative_start": 0.7271039485931396, "end": **********.705695, "relative_end": **********.705695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.706682, "relative_start": 0.7280910015106201, "end": **********.706682, "relative_end": **********.706682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.712003, "relative_start": 0.7334120273590088, "end": **********.712003, "relative_end": **********.712003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.713724, "relative_start": 0.7351329326629639, "end": **********.713724, "relative_end": **********.713724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.715003, "relative_start": 0.7364120483398438, "end": **********.715003, "relative_end": **********.715003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.715732, "relative_start": 0.7371411323547363, "end": **********.715732, "relative_end": **********.715732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.716358, "relative_start": 0.7377669811248779, "end": **********.716358, "relative_end": **********.716358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.716897, "relative_start": 0.7383060455322266, "end": **********.716897, "relative_end": **********.716897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.717368, "relative_start": 0.7387769222259521, "end": **********.717368, "relative_end": **********.717368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.718986, "relative_start": 0.7403950691223145, "end": **********.718986, "relative_end": **********.718986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.720351, "relative_start": 0.7417600154876709, "end": **********.720351, "relative_end": **********.720351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.721326, "relative_start": 0.7427351474761963, "end": **********.721326, "relative_end": **********.721326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.722164, "relative_start": 0.7435729503631592, "end": **********.722164, "relative_end": **********.722164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.722991, "relative_start": 0.7444000244140625, "end": **********.722991, "relative_end": **********.722991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.72382, "relative_start": 0.7452290058135986, "end": **********.72382, "relative_end": **********.72382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.725919, "relative_start": 0.7473280429840088, "end": **********.725919, "relative_end": **********.725919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.727099, "relative_start": 0.7485079765319824, "end": **********.727099, "relative_end": **********.727099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.727675, "relative_start": 0.7490839958190918, "end": **********.727675, "relative_end": **********.727675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.729461, "relative_start": 0.7508699893951416, "end": **********.729461, "relative_end": **********.729461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.730142, "relative_start": 0.7515511512756348, "end": **********.730142, "relative_end": **********.730142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.730727, "relative_start": 0.7521359920501709, "end": **********.730727, "relative_end": **********.730727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.731201, "relative_start": 0.7526099681854248, "end": **********.731201, "relative_end": **********.731201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.732484, "relative_start": 0.7538931369781494, "end": **********.732484, "relative_end": **********.732484, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.733035, "relative_start": 0.7544441223144531, "end": **********.733035, "relative_end": **********.733035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.733545, "relative_start": 0.7549540996551514, "end": **********.733545, "relative_end": **********.733545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.734103, "relative_start": 0.755511999130249, "end": **********.734103, "relative_end": **********.734103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.734653, "relative_start": 0.7560620307922363, "end": **********.734653, "relative_end": **********.734653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.736699, "relative_start": 0.7581081390380859, "end": **********.736699, "relative_end": **********.736699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.738361, "relative_start": 0.7597699165344238, "end": **********.738361, "relative_end": **********.738361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.739075, "relative_start": 0.760483980178833, "end": **********.739075, "relative_end": **********.739075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.739681, "relative_start": 0.7610900402069092, "end": **********.739681, "relative_end": **********.739681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.740638, "relative_start": 0.7620470523834229, "end": **********.740638, "relative_end": **********.740638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.741371, "relative_start": 0.762779951095581, "end": **********.741371, "relative_end": **********.741371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.741946, "relative_start": 0.763355016708374, "end": **********.741946, "relative_end": **********.741946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.742555, "relative_start": 0.7639639377593994, "end": **********.742555, "relative_end": **********.742555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.743279, "relative_start": 0.7646880149841309, "end": **********.743279, "relative_end": **********.743279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.743828, "relative_start": 0.7652370929718018, "end": **********.743828, "relative_end": **********.743828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.745478, "relative_start": 0.7668869495391846, "end": **********.745478, "relative_end": **********.745478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.746906, "relative_start": 0.7683150768280029, "end": **********.746906, "relative_end": **********.746906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.747441, "relative_start": 0.7688500881195068, "end": **********.747441, "relative_end": **********.747441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.747918, "relative_start": 0.76932692527771, "end": **********.747918, "relative_end": **********.747918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.748477, "relative_start": 0.7698860168457031, "end": **********.748477, "relative_end": **********.748477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.748992, "relative_start": 0.7704010009765625, "end": **********.748992, "relative_end": **********.748992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.749447, "relative_start": 0.7708561420440674, "end": **********.749447, "relative_end": **********.749447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.750679, "relative_start": 0.7720880508422852, "end": **********.750679, "relative_end": **********.750679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.751174, "relative_start": 0.7725830078125, "end": **********.751174, "relative_end": **********.751174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.751698, "relative_start": 0.7731070518493652, "end": **********.751698, "relative_end": **********.751698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.75449, "relative_start": 0.7758989334106445, "end": **********.75449, "relative_end": **********.75449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.756601, "relative_start": 0.7780101299285889, "end": **********.756601, "relative_end": **********.756601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.757523, "relative_start": 0.7789320945739746, "end": **********.757523, "relative_end": **********.757523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.758488, "relative_start": 0.7798969745635986, "end": **********.758488, "relative_end": **********.758488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.75931, "relative_start": 0.7807190418243408, "end": **********.75931, "relative_end": **********.75931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.759959, "relative_start": 0.7813680171966553, "end": **********.759959, "relative_end": **********.759959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.762974, "relative_start": 0.7843830585479736, "end": **********.762974, "relative_end": **********.762974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.764319, "relative_start": 0.7857279777526855, "end": **********.764319, "relative_end": **********.764319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.765208, "relative_start": 0.7866170406341553, "end": **********.765208, "relative_end": **********.765208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.765658, "relative_start": 0.7870669364929199, "end": **********.765658, "relative_end": **********.765658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.767778, "relative_start": 0.789186954498291, "end": **********.767778, "relative_end": **********.767778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.768788, "relative_start": 0.7901971340179443, "end": **********.768788, "relative_end": **********.768788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.770586, "relative_start": 0.7919950485229492, "end": **********.770586, "relative_end": **********.770586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.773687, "relative_start": 0.7950959205627441, "end": **********.773687, "relative_end": **********.773687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.77449, "relative_start": 0.7958991527557373, "end": **********.77449, "relative_end": **********.77449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.775464, "relative_start": 0.7968730926513672, "end": **********.775464, "relative_end": **********.775464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.777271, "relative_start": 0.7986800670623779, "end": **********.777271, "relative_end": **********.777271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.77815, "relative_start": 0.7995591163635254, "end": **********.77815, "relative_end": **********.77815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.778773, "relative_start": 0.8001821041107178, "end": **********.778773, "relative_end": **********.778773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.779745, "relative_start": 0.8011541366577148, "end": **********.779745, "relative_end": **********.779745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.78065, "relative_start": 0.8020589351654053, "end": **********.78065, "relative_end": **********.78065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.781001, "relative_start": 0.8024101257324219, "end": **********.781001, "relative_end": **********.781001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.781648, "relative_start": 0.8030569553375244, "end": **********.781648, "relative_end": **********.781648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.rows", "start": **********.782095, "relative_start": 0.8035039901733398, "end": **********.782095, "relative_end": **********.782095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.index", "start": **********.782586, "relative_start": 0.8039951324462891, "end": **********.782586, "relative_end": **********.782586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.783451, "relative_start": 0.8048601150512695, "end": **********.783451, "relative_end": **********.783451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.784102, "relative_start": 0.8055109977722168, "end": **********.784102, "relative_end": **********.784102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "start": **********.784812, "relative_start": 0.8062210083007812, "end": **********.784812, "relative_end": **********.784812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.78993, "relative_start": 0.8113391399383545, "end": **********.791344, "relative_end": **********.791344, "duration": 0.0014138221740722656, "duration_str": "1.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 29687584, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 98, "nb_templates": 98, "templates": [{"name": "1x livewire.category-table", "param_count": null, "params": [], "start": **********.674907, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/category-table.blade.phplivewire.category-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fcategory-table.blade.php&line=1", "ajax": false, "filename": "category-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.category-table"}, {"name": "3x e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "param_count": null, "params": [], "start": **********.680152, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 3, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::heading"}, {"name": "12x e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "param_count": null, "params": [], "start": **********.681577, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 12, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button.index"}, {"name": "11x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "param_count": null, "params": [], "start": **********.683801, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 11, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "param_count": null, "params": [], "start": **********.684673, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/plus.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fplus.blade.php&line=1", "ajax": false, "filename": "plus.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus"}, {"name": "12x e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "param_count": null, "params": [], "start": **********.685567, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}, "render_count": 12, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link"}, {"name": "12x e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "param_count": null, "params": [], "start": **********.686992, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}, "render_count": 12, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip"}, {"name": "5x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "param_count": null, "params": [], "start": **********.687874, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/trigger.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Ftrigger.blade.php&line=1", "ajax": false, "filename": "trigger.blade.php", "line": "?"}, "render_count": 5, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "param_count": null, "params": [], "start": **********.688729, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "param_count": null, "params": [], "start": **********.692772, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/magnifying-glass.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmagnifying-glass.blade.php&line=1", "ajax": false, "filename": "magnifying-glass.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass"}, {"name": "3x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "param_count": null, "params": [], "start": **********.695222, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 3, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "param_count": null, "params": [], "start": **********.696061, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/clearable.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Fclearable.blade.php&line=1", "ajax": false, "filename": "clearable.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable"}, {"name": "3x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "param_count": null, "params": [], "start": **********.696991, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/x-mark.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fx-mark.blade.php&line=1", "ajax": false, "filename": "x-mark.blade.php", "line": "?"}, "render_count": 3, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "param_count": null, "params": [], "start": **********.700081, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-field"}, {"name": "3x components.table.column", "param_count": null, "params": [], "start": **********.701185, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/column.blade.phpcomponents.table.column", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}, "render_count": 3, "name_original": "components.table.column"}, {"name": "1x components.table.columns", "param_count": null, "params": [], "start": **********.704267, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/columns.blade.phpcomponents.table.columns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcolumns.blade.php&line=1", "ajax": false, "filename": "columns.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.columns"}, {"name": "6x components.table.cell", "param_count": null, "params": [], "start": **********.705596, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/cell.blade.phpcomponents.table.cell", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}, "render_count": 6, "name_original": "components.table.cell"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "param_count": null, "params": [], "start": **********.714916, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/pencil-square.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fpencil-square.blade.php&line=1", "ajax": false, "filename": "pencil-square.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "param_count": null, "params": [], "start": **********.720263, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/trash.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Ftrash.blade.php&line=1", "ajax": false, "filename": "trash.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "param_count": null, "params": [], "start": **********.72585, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/subheading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsubheading.blade.php&line=1", "ajax": false, "filename": "subheading.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::subheading"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "param_count": null, "params": [], "start": **********.727027, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/spacer.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fspacer.blade.php&line=1", "ajax": false, "filename": "spacer.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::spacer"}, {"name": "4x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "param_count": null, "params": [], "start": **********.730668, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/close.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 4, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "param_count": null, "params": [], "start": **********.734592, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index"}, {"name": "2x components.table.row", "param_count": null, "params": [], "start": **********.742497, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/row.blade.phpcomponents.table.row", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.table.row"}, {"name": "1x components.table.rows", "param_count": null, "params": [], "start": **********.782037, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/rows.blade.phpcomponents.table.rows", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Frows.blade.php&line=1", "ajax": false, "filename": "rows.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.rows"}, {"name": "1x components.table.index", "param_count": null, "params": [], "start": **********.782533, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/index.blade.phpcomponents.table.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.index"}, {"name": "1x livewire::tailwind", "param_count": null, "params": [], "start": **********.783396, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire::tailwind"}, {"name": "1x components.card", "param_count": null, "params": [], "start": **********.784046, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.card"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "param_count": null, "params": [], "start": **********.784756, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/card/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::card.index"}]}, "queries": {"count": 6, "nb_statements": 5, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00971, "accumulated_duration_str": "9.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 131}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 111}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 60}, {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 88}], "start": **********.527516, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CacheManager.php:245", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheManager.php&line=245", "ajax": false, "filename": "CacheManager.php", "line": "245"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn' limit 1", "type": "query", "params": [], "bindings": ["dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.565305, "duration": 0.0058200000000000005, "duration_str": "5.82ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 59.938}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.612599, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 59.938, "width_percent": 7.621}, {"sql": "select count(*) as aggregate from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null and `name` like '%He%'", "type": "query", "params": [], "bindings": [1, "%He%"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.6529849, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "CategoryTable.php:53", "source": {"index": 19, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryTable.php&line=53", "ajax": false, "filename": "CategoryTable.php", "line": "53"}, "connection": "daily", "explain": null, "start_percent": 67.559, "width_percent": 13.491}, {"sql": "select `id`, `name`, `parent_id` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null and `name` like '%He%' order by `name` asc limit 15 offset 0", "type": "query", "params": [], "bindings": [1, "%He%"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.6573648, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "CategoryTable.php:53", "source": {"index": 19, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryTable.php&line=53", "ajax": false, "filename": "CategoryTable.php", "line": "53"}, "connection": "daily", "explain": null, "start_percent": 81.05, "width_percent": 13.079}, {"sql": "select * from `categories` where `categories`.`id` in (6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.662714, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CategoryTable.php:53", "source": {"index": 24, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryTable.php&line=53", "ajax": false, "filename": "CategoryTable.php", "line": "53"}, "connection": "daily", "explain": null, "start_percent": 94.13, "width_percent": 5.87}]}, "models": {"data": {"App\\Models\\Category": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 4, "is_counter": true}, "livewire": {"data": {"category-table #g3dK2eCemiah9QJBO4XZ": "array:4 [\n  \"data\" => array:2 [\n    \"search\" => \"He\"\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"category-table\"\n  \"component\" => \"App\\Livewire\\CategoryTable\"\n  \"id\" => \"g3dK2eCemiah9QJBO4XZ\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "818ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1503847975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1503847975\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-470827587 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"350 characters\">{&quot;data&quot;:{&quot;search&quot;:&quot;Heak&quot;,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;g3dK2eCemiah9QJBO4XZ&quot;,&quot;name&quot;:&quot;category-table&quot;,&quot;path&quot;:&quot;categories&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-706289484-0&quot;:[&quot;div&quot;,&quot;e901eisav6YiU6EF1BdD&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;84ef3cdfd2bb0440953c177388e33d1aa17a43ea3db205be81e676ae31760c7f&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">He</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-470827587\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1138656569 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">525</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/categories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Imw3THdIRjZPYnZKYkhPKzFrVkl1WVE9PSIsInZhbHVlIjoicmxxTXo2NTIwdFZtbDZYOHphRjkzV1VmY1QxaUpFa1dKeGZyMWZIVWZwVmlXYThQajJ1NjRSUjR4bkpVM1YwdElDVDlxbTlkUDdWZFVKT05ia25vMGZ1ZGIzSVFqOTl0cCs5dHFacGhvTnMrTFgxVmplNmdJemxjK3RtZHhuK3giLCJtYWMiOiI5MjBmNGU4ZDdlMmY0MTZhOWZkMTY5YzVhMWE0YjQ0YWEwNGIzN2JlNTE0Yjc3ZGM5ZDNhMjI3NWNmZWMzYzI3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjVkYVZnSnJwY1JEZ2JtdjAxVmxQQ2c9PSIsInZhbHVlIjoiTUw4NVVNanJDbWk3QnZ0b1JlZStBbmN2SEp2c2orNEVZSlpmaG5KNXFoRXRzNm9KVk5vaTd5dFlCYnVMU2pDeEtwVGNDdGFxOEhCQ1pBLzE5ZGJNUmIzNXRxZnd1MmhkMTlVc3pVa1RZUkZvME0wOEpna2NwTytvQUhXOHArTm4iLCJtYWMiOiIwYTUxYTY2NGY3YjIxZjkwODcxNDY1ODlkY2ZmODMzNmVkMDRiNTc1Y2U2ZDFmNmRlYTY5YzRkN2Q3YzQ5YjBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138656569\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1455815403 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1455815403\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2004408581 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 26 May 2025 08:55:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2004408581\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-617585261 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/categories</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-617585261\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}