<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Models\User;
use Flux\Flux;
use Livewire\Component;
use Spatie\Permission\Models\Role;
use Illuminate\Contracts\View\View;

class UserRoleForm extends Component
{
    public bool $show_user_role_form = false;

    public ?User $user = null;

    public array $roles = [];

    protected function rules(): array
    {
        return [
            'roles' => ['array'],
            'roles.*' => ['exists:roles,id'],
        ];
    }

    protected function messages(): array
    {
        return [
            'roles.*.exists' => 'One or more selected roles are invalid.',
        ];
    }

    public function mount(): void
    {
        if ($this->user) {
            $this->roles = $this->user->roles->pluck('id')->toArray();
        }
    }

    public function submit(): void
    {
        $validated_data = $this->validate();

        if ($this->user) {
            // Sync roles
            $this->user->syncRoles($validated_data['roles']);
        }

        $this->dispatch('user-roles-updated');

        Flux::toast(
            variant: 'success',
            text: 'User roles successfully updated',
        );

        Flux::modals()->close();
    }

    public function render(): View
    {
        return view('livewire.user-role-form', [
            'availableRoles' => Role::orderBy('name')->get(),
        ]);
    }
}
