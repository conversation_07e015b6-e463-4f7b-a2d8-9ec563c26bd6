{"__meta": {"id": "01JW3BN54PYDFMR56FPP26C9TP", "datetime": "2025-05-25 09:16:20", "utime": **********.503562, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.690288, "end": **********.503581, "duration": 0.8132929801940918, "duration_str": "813ms", "measures": [{"label": "Booting", "start": **********.690288, "relative_start": 0, "end": **********.991312, "relative_end": **********.991312, "duration": 0.****************, "duration_str": "301ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.991325, "relative_start": 0.****************, "end": **********.503583, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "512ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.015347, "relative_start": 0.****************, "end": **********.019327, "relative_end": **********.019327, "duration": 0.003979921340942383, "duration_str": "3.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.category-table", "start": **********.11639, "relative_start": 0.****************, "end": **********.11639, "relative_end": **********.11639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.120644, "relative_start": 0.****************, "end": **********.120644, "relative_end": **********.120644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.121729, "relative_start": 0.4314408302307129, "end": **********.121729, "relative_end": **********.121729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.1239, "relative_start": 0.4336118698120117, "end": **********.1239, "relative_end": **********.1239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "start": **********.124805, "relative_start": 0.43451690673828125, "end": **********.124805, "relative_end": **********.124805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.125374, "relative_start": 0.4350860118865967, "end": **********.125374, "relative_end": **********.125374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.12603, "relative_start": 0.4357419013977051, "end": **********.12603, "relative_end": **********.12603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.126665, "relative_start": 0.43637704849243164, "end": **********.126665, "relative_end": **********.126665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.127353, "relative_start": 0.43706488609313965, "end": **********.127353, "relative_end": **********.127353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.129668, "relative_start": 0.4393799304962158, "end": **********.129668, "relative_end": **********.129668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "start": **********.130481, "relative_start": 0.44019293785095215, "end": **********.130481, "relative_end": **********.130481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.131121, "relative_start": 0.44083285331726074, "end": **********.131121, "relative_end": **********.131121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.132513, "relative_start": 0.44222497940063477, "end": **********.132513, "relative_end": **********.132513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "start": **********.133982, "relative_start": 0.4436938762664795, "end": **********.133982, "relative_end": **********.133982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.135109, "relative_start": 0.4448208808898926, "end": **********.135109, "relative_end": **********.135109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.135788, "relative_start": 0.44549989700317383, "end": **********.135788, "relative_end": **********.135788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.137042, "relative_start": 0.44675397872924805, "end": **********.137042, "relative_end": **********.137042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.137551, "relative_start": 0.4472630023956299, "end": **********.137551, "relative_end": **********.137551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.138052, "relative_start": 0.44776391983032227, "end": **********.138052, "relative_end": **********.138052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": **********.139066, "relative_start": 0.4487779140472412, "end": **********.139066, "relative_end": **********.139066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": **********.139585, "relative_start": 0.4492969512939453, "end": **********.139585, "relative_end": **********.139585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": **********.140004, "relative_start": 0.44971585273742676, "end": **********.140004, "relative_end": **********.140004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.columns", "start": **********.140414, "relative_start": 0.45012593269348145, "end": **********.140414, "relative_end": **********.140414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.141075, "relative_start": 0.450786828994751, "end": **********.141075, "relative_end": **********.141075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.141588, "relative_start": 0.45129990577697754, "end": **********.141588, "relative_end": **********.141588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.144983, "relative_start": 0.4546949863433838, "end": **********.144983, "relative_end": **********.144983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.146236, "relative_start": 0.4559478759765625, "end": **********.146236, "relative_end": **********.146236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.146999, "relative_start": 0.4567108154296875, "end": **********.146999, "relative_end": **********.146999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.14773, "relative_start": 0.457442045211792, "end": **********.14773, "relative_end": **********.14773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.148641, "relative_start": 0.45835304260253906, "end": **********.148641, "relative_end": **********.148641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.14971, "relative_start": 0.45942187309265137, "end": **********.14971, "relative_end": **********.14971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.150532, "relative_start": 0.46024394035339355, "end": **********.150532, "relative_end": **********.150532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.151844, "relative_start": 0.46155595779418945, "end": **********.151844, "relative_end": **********.151844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.152584, "relative_start": 0.4622960090637207, "end": **********.152584, "relative_end": **********.152584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.153148, "relative_start": 0.4628598690032959, "end": **********.153148, "relative_end": **********.153148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.153653, "relative_start": 0.463364839553833, "end": **********.153653, "relative_end": **********.153653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.154126, "relative_start": 0.4638378620147705, "end": **********.154126, "relative_end": **********.154126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.1546, "relative_start": 0.4643118381500244, "end": **********.1546, "relative_end": **********.1546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.155501, "relative_start": 0.4652128219604492, "end": **********.155501, "relative_end": **********.155501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.156312, "relative_start": 0.46602392196655273, "end": **********.156312, "relative_end": **********.156312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.1568, "relative_start": 0.46651196479797363, "end": **********.1568, "relative_end": **********.1568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.158007, "relative_start": 0.46771883964538574, "end": **********.158007, "relative_end": **********.158007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.158552, "relative_start": 0.4682638645172119, "end": **********.158552, "relative_end": **********.158552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.159069, "relative_start": 0.4687809944152832, "end": **********.159069, "relative_end": **********.159069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.159471, "relative_start": 0.46918296813964844, "end": **********.159471, "relative_end": **********.159471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.160649, "relative_start": 0.47036099433898926, "end": **********.160649, "relative_end": **********.160649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.161127, "relative_start": 0.4708390235900879, "end": **********.161127, "relative_end": **********.161127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.161579, "relative_start": 0.47129082679748535, "end": **********.161579, "relative_end": **********.161579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.162084, "relative_start": 0.47179603576660156, "end": **********.162084, "relative_end": **********.162084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.162603, "relative_start": 0.47231483459472656, "end": **********.162603, "relative_end": **********.162603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.164055, "relative_start": 0.4737670421600342, "end": **********.164055, "relative_end": **********.164055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.165669, "relative_start": 0.47538089752197266, "end": **********.165669, "relative_end": **********.165669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.166632, "relative_start": 0.47634387016296387, "end": **********.166632, "relative_end": **********.166632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.167409, "relative_start": 0.47712087631225586, "end": **********.167409, "relative_end": **********.167409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.168023, "relative_start": 0.4777350425720215, "end": **********.168023, "relative_end": **********.168023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.168525, "relative_start": 0.4782369136810303, "end": **********.168525, "relative_end": **********.168525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.168802, "relative_start": 0.4785139560699463, "end": **********.168802, "relative_end": **********.168802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.169207, "relative_start": 0.47891902923583984, "end": **********.169207, "relative_end": **********.169207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.169724, "relative_start": 0.47943592071533203, "end": **********.169724, "relative_end": **********.169724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.170145, "relative_start": 0.4798569679260254, "end": **********.170145, "relative_end": **********.170145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.170625, "relative_start": 0.48033690452575684, "end": **********.170625, "relative_end": **********.170625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.171842, "relative_start": 0.4815540313720703, "end": **********.171842, "relative_end": **********.171842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.17233, "relative_start": 0.4820418357849121, "end": **********.17233, "relative_end": **********.17233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.172763, "relative_start": 0.48247504234313965, "end": **********.172763, "relative_end": **********.172763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.173274, "relative_start": 0.4829859733581543, "end": **********.173274, "relative_end": **********.173274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.173737, "relative_start": 0.48344898223876953, "end": **********.173737, "relative_end": **********.173737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.174167, "relative_start": 0.48387885093688965, "end": **********.174167, "relative_end": **********.174167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.175293, "relative_start": 0.48500490188598633, "end": **********.175293, "relative_end": **********.175293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.175753, "relative_start": 0.48546504974365234, "end": **********.175753, "relative_end": **********.175753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.176185, "relative_start": 0.4858968257904053, "end": **********.176185, "relative_end": **********.176185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.176691, "relative_start": 0.4864029884338379, "end": **********.176691, "relative_end": **********.176691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.177182, "relative_start": 0.486893892288208, "end": **********.177182, "relative_end": **********.177182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.177622, "relative_start": 0.4873340129852295, "end": **********.177622, "relative_end": **********.177622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.178106, "relative_start": 0.48781800270080566, "end": **********.178106, "relative_end": **********.178106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.178526, "relative_start": 0.4882378578186035, "end": **********.178526, "relative_end": **********.178526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.178862, "relative_start": 0.4885740280151367, "end": **********.178862, "relative_end": **********.178862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.180343, "relative_start": 0.4900548458099365, "end": **********.180343, "relative_end": **********.180343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.18096, "relative_start": 0.49067187309265137, "end": **********.18096, "relative_end": **********.18096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.181444, "relative_start": 0.49115586280822754, "end": **********.181444, "relative_end": **********.181444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.181732, "relative_start": 0.4914438724517822, "end": **********.181732, "relative_end": **********.181732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.183894, "relative_start": 0.4936058521270752, "end": **********.183894, "relative_end": **********.183894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.184496, "relative_start": 0.49420785903930664, "end": **********.184496, "relative_end": **********.184496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.18496, "relative_start": 0.4946718215942383, "end": **********.18496, "relative_end": **********.18496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.185468, "relative_start": 0.4951798915863037, "end": **********.185468, "relative_end": **********.185468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.185951, "relative_start": 0.4956629276275635, "end": **********.185951, "relative_end": **********.185951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.186665, "relative_start": 0.49637699127197266, "end": **********.186665, "relative_end": **********.186665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.187835, "relative_start": 0.497546911239624, "end": **********.187835, "relative_end": **********.187835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.188299, "relative_start": 0.49801087379455566, "end": **********.188299, "relative_end": **********.188299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.188806, "relative_start": 0.4985179901123047, "end": **********.188806, "relative_end": **********.188806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.189327, "relative_start": 0.4990389347076416, "end": **********.189327, "relative_end": **********.189327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.189818, "relative_start": 0.4995298385620117, "end": **********.189818, "relative_end": **********.189818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.190113, "relative_start": 0.49982500076293945, "end": **********.190113, "relative_end": **********.190113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.19052, "relative_start": 0.5002319812774658, "end": **********.19052, "relative_end": **********.19052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.190991, "relative_start": 0.5007028579711914, "end": **********.190991, "relative_end": **********.190991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.191404, "relative_start": 0.5011160373687744, "end": **********.191404, "relative_end": **********.191404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.192309, "relative_start": 0.5020208358764648, "end": **********.192309, "relative_end": **********.192309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.193508, "relative_start": 0.5032198429107666, "end": **********.193508, "relative_end": **********.193508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.19402, "relative_start": 0.5037319660186768, "end": **********.19402, "relative_end": **********.19402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.194469, "relative_start": 0.504180908203125, "end": **********.194469, "relative_end": **********.194469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.195011, "relative_start": 0.5047228336334229, "end": **********.195011, "relative_end": **********.195011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.195764, "relative_start": 0.5054759979248047, "end": **********.195764, "relative_end": **********.195764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.196447, "relative_start": 0.5061588287353516, "end": **********.196447, "relative_end": **********.196447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.197672, "relative_start": 0.5073838233947754, "end": **********.197672, "relative_end": **********.197672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.198159, "relative_start": 0.5078709125518799, "end": **********.198159, "relative_end": **********.198159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.19879, "relative_start": 0.5085020065307617, "end": **********.19879, "relative_end": **********.19879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.199831, "relative_start": 0.5095429420471191, "end": **********.199831, "relative_end": **********.199831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.2007, "relative_start": 0.5104119777679443, "end": **********.2007, "relative_end": **********.2007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.201181, "relative_start": 0.5108928680419922, "end": **********.201181, "relative_end": **********.201181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.201657, "relative_start": 0.511368989944458, "end": **********.201657, "relative_end": **********.201657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.20204, "relative_start": 0.5117518901824951, "end": **********.20204, "relative_end": **********.20204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.202348, "relative_start": 0.5120599269866943, "end": **********.202348, "relative_end": **********.202348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.203481, "relative_start": 0.513192892074585, "end": **********.203481, "relative_end": **********.203481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.203996, "relative_start": 0.5137078762054443, "end": **********.203996, "relative_end": **********.203996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.204478, "relative_start": 0.5141899585723877, "end": **********.204478, "relative_end": **********.204478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.20478, "relative_start": 0.5144920349121094, "end": **********.20478, "relative_end": **********.20478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.205905, "relative_start": 0.5156168937683105, "end": **********.205905, "relative_end": **********.205905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.206374, "relative_start": 0.5160858631134033, "end": **********.206374, "relative_end": **********.206374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.206842, "relative_start": 0.5165538787841797, "end": **********.206842, "relative_end": **********.206842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.207363, "relative_start": 0.5170748233795166, "end": **********.207363, "relative_end": **********.207363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.20786, "relative_start": 0.5175719261169434, "end": **********.20786, "relative_end": **********.20786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.208624, "relative_start": 0.5183358192443848, "end": **********.208624, "relative_end": **********.208624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.209803, "relative_start": 0.5195150375366211, "end": **********.209803, "relative_end": **********.209803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.210282, "relative_start": 0.5199940204620361, "end": **********.210282, "relative_end": **********.210282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.210747, "relative_start": 0.5204589366912842, "end": **********.210747, "relative_end": **********.210747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.21125, "relative_start": 0.5209619998931885, "end": **********.21125, "relative_end": **********.21125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.212413, "relative_start": 0.5221250057220459, "end": **********.212413, "relative_end": **********.212413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.212783, "relative_start": 0.5224950313568115, "end": **********.212783, "relative_end": **********.212783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.213292, "relative_start": 0.5230038166046143, "end": **********.213292, "relative_end": **********.213292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.213771, "relative_start": 0.5234830379486084, "end": **********.213771, "relative_end": **********.213771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.214196, "relative_start": 0.5239078998565674, "end": **********.214196, "relative_end": **********.214196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.215122, "relative_start": 0.5248339176177979, "end": **********.215122, "relative_end": **********.215122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.217152, "relative_start": 0.5268640518188477, "end": **********.217152, "relative_end": **********.217152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.217755, "relative_start": 0.5274670124053955, "end": **********.217755, "relative_end": **********.217755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.218237, "relative_start": 0.5279488563537598, "end": **********.218237, "relative_end": **********.218237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.218761, "relative_start": 0.528472900390625, "end": **********.218761, "relative_end": **********.218761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.219273, "relative_start": 0.5289850234985352, "end": **********.219273, "relative_end": **********.219273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.219729, "relative_start": 0.5294408798217773, "end": **********.219729, "relative_end": **********.219729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.220909, "relative_start": 0.5306210517883301, "end": **********.220909, "relative_end": **********.220909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.2214, "relative_start": 0.5311119556427002, "end": **********.2214, "relative_end": **********.2214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.22185, "relative_start": 0.5315618515014648, "end": **********.22185, "relative_end": **********.22185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.222372, "relative_start": 0.5320839881896973, "end": **********.222372, "relative_end": **********.222372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.222881, "relative_start": 0.5325930118560791, "end": **********.222881, "relative_end": **********.222881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.22332, "relative_start": 0.5330319404602051, "end": **********.22332, "relative_end": **********.22332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.223807, "relative_start": 0.5335190296173096, "end": **********.223807, "relative_end": **********.223807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.224247, "relative_start": 0.533958911895752, "end": **********.224247, "relative_end": **********.224247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.224573, "relative_start": 0.5342848300933838, "end": **********.224573, "relative_end": **********.224573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.225732, "relative_start": 0.5354440212249756, "end": **********.225732, "relative_end": **********.225732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.226268, "relative_start": 0.5359799861907959, "end": **********.226268, "relative_end": **********.226268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.226756, "relative_start": 0.5364680290222168, "end": **********.226756, "relative_end": **********.226756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.227345, "relative_start": 0.5370569229125977, "end": **********.227345, "relative_end": **********.227345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.228664, "relative_start": 0.5383758544921875, "end": **********.228664, "relative_end": **********.228664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.2292, "relative_start": 0.5389118194580078, "end": **********.2292, "relative_end": **********.2292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.229687, "relative_start": 0.5393989086151123, "end": **********.229687, "relative_end": **********.229687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.230222, "relative_start": 0.5399339199066162, "end": **********.230222, "relative_end": **********.230222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.230727, "relative_start": 0.5404388904571533, "end": **********.230727, "relative_end": **********.230727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.231518, "relative_start": 0.5412299633026123, "end": **********.231518, "relative_end": **********.231518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.233775, "relative_start": 0.5434868335723877, "end": **********.233775, "relative_end": **********.233775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.234448, "relative_start": 0.5441598892211914, "end": **********.234448, "relative_end": **********.234448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.234943, "relative_start": 0.5446548461914062, "end": **********.234943, "relative_end": **********.234943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.235472, "relative_start": 0.5451838970184326, "end": **********.235472, "relative_end": **********.235472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.235969, "relative_start": 0.5456809997558594, "end": **********.235969, "relative_end": **********.235969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.23625, "relative_start": 0.545961856842041, "end": **********.23625, "relative_end": **********.23625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.236638, "relative_start": 0.5463500022888184, "end": **********.236638, "relative_end": **********.236638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.237088, "relative_start": 0.546799898147583, "end": **********.237088, "relative_end": **********.237088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.237474, "relative_start": 0.5471858978271484, "end": **********.237474, "relative_end": **********.237474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.238278, "relative_start": 0.5479898452758789, "end": **********.238278, "relative_end": **********.238278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.239401, "relative_start": 0.5491130352020264, "end": **********.239401, "relative_end": **********.239401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.23987, "relative_start": 0.5495820045471191, "end": **********.23987, "relative_end": **********.23987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.240313, "relative_start": 0.5500249862670898, "end": **********.240313, "relative_end": **********.240313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.2408, "relative_start": 0.5505118370056152, "end": **********.2408, "relative_end": **********.2408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.241279, "relative_start": 0.5509908199310303, "end": **********.241279, "relative_end": **********.241279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.241704, "relative_start": 0.5514159202575684, "end": **********.241704, "relative_end": **********.241704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.243156, "relative_start": 0.5528678894042969, "end": **********.243156, "relative_end": **********.243156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.243675, "relative_start": 0.553386926651001, "end": **********.243675, "relative_end": **********.243675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.244137, "relative_start": 0.5538489818572998, "end": **********.244137, "relative_end": **********.244137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.244606, "relative_start": 0.5543179512023926, "end": **********.244606, "relative_end": **********.244606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.245079, "relative_start": 0.5547909736633301, "end": **********.245079, "relative_end": **********.245079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.245504, "relative_start": 0.5552158355712891, "end": **********.245504, "relative_end": **********.245504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.245985, "relative_start": 0.555696964263916, "end": **********.245985, "relative_end": **********.245985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.246375, "relative_start": 0.5560870170593262, "end": **********.246375, "relative_end": **********.246375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.246683, "relative_start": 0.5563948154449463, "end": **********.246683, "relative_end": **********.246683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.247793, "relative_start": 0.5575048923492432, "end": **********.247793, "relative_end": **********.247793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.248341, "relative_start": 0.5580530166625977, "end": **********.248341, "relative_end": **********.248341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.249359, "relative_start": 0.5590708255767822, "end": **********.249359, "relative_end": **********.249359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.25, "relative_start": 0.5597119331359863, "end": **********.25, "relative_end": **********.25, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.251525, "relative_start": 0.5612368583679199, "end": **********.251525, "relative_end": **********.251525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.252042, "relative_start": 0.5617539882659912, "end": **********.252042, "relative_end": **********.252042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.252459, "relative_start": 0.5621709823608398, "end": **********.252459, "relative_end": **********.252459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.252941, "relative_start": 0.5626528263092041, "end": **********.252941, "relative_end": **********.252941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.253387, "relative_start": 0.5630989074707031, "end": **********.253387, "relative_end": **********.253387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.254095, "relative_start": 0.5638070106506348, "end": **********.254095, "relative_end": **********.254095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.255292, "relative_start": 0.5650038719177246, "end": **********.255292, "relative_end": **********.255292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.255785, "relative_start": 0.5654969215393066, "end": **********.255785, "relative_end": **********.255785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.256227, "relative_start": 0.5659389495849609, "end": **********.256227, "relative_end": **********.256227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.256717, "relative_start": 0.5664288997650146, "end": **********.256717, "relative_end": **********.256717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.257491, "relative_start": 0.5672030448913574, "end": **********.257491, "relative_end": **********.257491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.25781, "relative_start": 0.5675220489501953, "end": **********.25781, "relative_end": **********.25781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.258224, "relative_start": 0.5679359436035156, "end": **********.258224, "relative_end": **********.258224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.258664, "relative_start": 0.568375825881958, "end": **********.258664, "relative_end": **********.258664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.259086, "relative_start": 0.5687978267669678, "end": **********.259086, "relative_end": **********.259086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.259933, "relative_start": 0.5696449279785156, "end": **********.259933, "relative_end": **********.259933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.261128, "relative_start": 0.5708398818969727, "end": **********.261128, "relative_end": **********.261128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.26161, "relative_start": 0.571321964263916, "end": **********.26161, "relative_end": **********.26161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.262072, "relative_start": 0.5717840194702148, "end": **********.262072, "relative_end": **********.262072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.262571, "relative_start": 0.5722830295562744, "end": **********.262571, "relative_end": **********.262571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.263047, "relative_start": 0.5727589130401611, "end": **********.263047, "relative_end": **********.263047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.263494, "relative_start": 0.5732059478759766, "end": **********.263494, "relative_end": **********.263494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.264648, "relative_start": 0.5743598937988281, "end": **********.264648, "relative_end": **********.264648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.265335, "relative_start": 0.5750470161437988, "end": **********.265335, "relative_end": **********.265335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.26615, "relative_start": 0.575861930847168, "end": **********.26615, "relative_end": **********.26615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.267274, "relative_start": 0.5769858360290527, "end": **********.267274, "relative_end": **********.267274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.268057, "relative_start": 0.5777690410614014, "end": **********.268057, "relative_end": **********.268057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.268533, "relative_start": 0.5782449245452881, "end": **********.268533, "relative_end": **********.268533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.26904, "relative_start": 0.5787520408630371, "end": **********.26904, "relative_end": **********.26904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.269461, "relative_start": 0.5791728496551514, "end": **********.269461, "relative_end": **********.269461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.269784, "relative_start": 0.579495906829834, "end": **********.269784, "relative_end": **********.269784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.270955, "relative_start": 0.5806670188903809, "end": **********.270955, "relative_end": **********.270955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.271482, "relative_start": 0.5811939239501953, "end": **********.271482, "relative_end": **********.271482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.272211, "relative_start": 0.5819230079650879, "end": **********.272211, "relative_end": **********.272211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.272649, "relative_start": 0.5823609828948975, "end": **********.272649, "relative_end": **********.272649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.273892, "relative_start": 0.5836038589477539, "end": **********.273892, "relative_end": **********.273892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.274415, "relative_start": 0.5841269493103027, "end": **********.274415, "relative_end": **********.274415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.274872, "relative_start": 0.5845839977264404, "end": **********.274872, "relative_end": **********.274872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.27539, "relative_start": 0.585101842880249, "end": **********.27539, "relative_end": **********.27539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.275893, "relative_start": 0.5856049060821533, "end": **********.275893, "relative_end": **********.275893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.276647, "relative_start": 0.5863590240478516, "end": **********.276647, "relative_end": **********.276647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.277837, "relative_start": 0.5875489711761475, "end": **********.277837, "relative_end": **********.277837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.278345, "relative_start": 0.5880570411682129, "end": **********.278345, "relative_end": **********.278345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.2788, "relative_start": 0.5885119438171387, "end": **********.2788, "relative_end": **********.2788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.279322, "relative_start": 0.589033842086792, "end": **********.279322, "relative_end": **********.279322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.279838, "relative_start": 0.5895500183105469, "end": **********.279838, "relative_end": **********.279838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.280141, "relative_start": 0.589853048324585, "end": **********.280141, "relative_end": **********.280141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.28057, "relative_start": 0.5902819633483887, "end": **********.28057, "relative_end": **********.28057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.281038, "relative_start": 0.590749979019165, "end": **********.281038, "relative_end": **********.281038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.281475, "relative_start": 0.5911870002746582, "end": **********.281475, "relative_end": **********.281475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.283214, "relative_start": 0.592926025390625, "end": **********.283214, "relative_end": **********.283214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.284827, "relative_start": 0.5945389270782471, "end": **********.284827, "relative_end": **********.284827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.285401, "relative_start": 0.5951130390167236, "end": **********.285401, "relative_end": **********.285401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.285852, "relative_start": 0.5955638885498047, "end": **********.285852, "relative_end": **********.285852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.286342, "relative_start": 0.5960538387298584, "end": **********.286342, "relative_end": **********.286342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.286781, "relative_start": 0.5964930057525635, "end": **********.286781, "relative_end": **********.286781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.287231, "relative_start": 0.5969429016113281, "end": **********.287231, "relative_end": **********.287231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.288673, "relative_start": 0.5983848571777344, "end": **********.288673, "relative_end": **********.288673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.289219, "relative_start": 0.598930835723877, "end": **********.289219, "relative_end": **********.289219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.289676, "relative_start": 0.5993878841400146, "end": **********.289676, "relative_end": **********.289676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.29018, "relative_start": 0.5998919010162354, "end": **********.29018, "relative_end": **********.29018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.290639, "relative_start": 0.6003508567810059, "end": **********.290639, "relative_end": **********.290639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.29107, "relative_start": 0.6007819175720215, "end": **********.29107, "relative_end": **********.29107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.291538, "relative_start": 0.6012499332427979, "end": **********.291538, "relative_end": **********.291538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.291928, "relative_start": 0.601639986038208, "end": **********.291928, "relative_end": **********.291928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.292238, "relative_start": 0.60194993019104, "end": **********.292238, "relative_end": **********.292238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.293366, "relative_start": 0.6030778884887695, "end": **********.293366, "relative_end": **********.293366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.293875, "relative_start": 0.6035869121551514, "end": **********.293875, "relative_end": **********.293875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.294376, "relative_start": 0.6040878295898438, "end": **********.294376, "relative_end": **********.294376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.294688, "relative_start": 0.6043999195098877, "end": **********.294688, "relative_end": **********.294688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.295853, "relative_start": 0.6055648326873779, "end": **********.295853, "relative_end": **********.295853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.296337, "relative_start": 0.6060488224029541, "end": **********.296337, "relative_end": **********.296337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.296803, "relative_start": 0.6065149307250977, "end": **********.296803, "relative_end": **********.296803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.297312, "relative_start": 0.6070239543914795, "end": **********.297312, "relative_end": **********.297312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.297784, "relative_start": 0.6074960231781006, "end": **********.297784, "relative_end": **********.297784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.298714, "relative_start": 0.6084258556365967, "end": **********.298714, "relative_end": **********.298714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.30088, "relative_start": 0.6105918884277344, "end": **********.30088, "relative_end": **********.30088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.301463, "relative_start": 0.6111748218536377, "end": **********.301463, "relative_end": **********.301463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.301925, "relative_start": 0.6116368770599365, "end": **********.301925, "relative_end": **********.301925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.302462, "relative_start": 0.6121740341186523, "end": **********.302462, "relative_end": **********.302462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.302969, "relative_start": 0.6126809120178223, "end": **********.302969, "relative_end": **********.302969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.303261, "relative_start": 0.6129729747772217, "end": **********.303261, "relative_end": **********.303261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.303673, "relative_start": 0.6133849620819092, "end": **********.303673, "relative_end": **********.303673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.304388, "relative_start": 0.6140999794006348, "end": **********.304388, "relative_end": **********.304388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.30495, "relative_start": 0.6146619319915771, "end": **********.30495, "relative_end": **********.30495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.30601, "relative_start": 0.6157219409942627, "end": **********.30601, "relative_end": **********.30601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.30732, "relative_start": 0.6170320510864258, "end": **********.30732, "relative_end": **********.30732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.307847, "relative_start": 0.6175589561462402, "end": **********.307847, "relative_end": **********.307847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.308311, "relative_start": 0.6180229187011719, "end": **********.308311, "relative_end": **********.308311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.308837, "relative_start": 0.6185488700866699, "end": **********.308837, "relative_end": **********.308837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.309343, "relative_start": 0.6190550327301025, "end": **********.309343, "relative_end": **********.309343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.309811, "relative_start": 0.6195230484008789, "end": **********.309811, "relative_end": **********.309811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.311002, "relative_start": 0.6207139492034912, "end": **********.311002, "relative_end": **********.311002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.311512, "relative_start": 0.6212239265441895, "end": **********.311512, "relative_end": **********.311512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.311973, "relative_start": 0.6216850280761719, "end": **********.311973, "relative_end": **********.311973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.312493, "relative_start": 0.6222050189971924, "end": **********.312493, "relative_end": **********.312493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.312992, "relative_start": 0.622704029083252, "end": **********.312992, "relative_end": **********.312992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.313459, "relative_start": 0.6231708526611328, "end": **********.313459, "relative_end": **********.313459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.313982, "relative_start": 0.6236939430236816, "end": **********.313982, "relative_end": **********.313982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.314405, "relative_start": 0.6241168975830078, "end": **********.314405, "relative_end": **********.314405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.314742, "relative_start": 0.6244540214538574, "end": **********.314742, "relative_end": **********.314742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.316799, "relative_start": 0.6265108585357666, "end": **********.316799, "relative_end": **********.316799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.317691, "relative_start": 0.6274030208587646, "end": **********.317691, "relative_end": **********.317691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.318302, "relative_start": 0.6280138492584229, "end": **********.318302, "relative_end": **********.318302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.318611, "relative_start": 0.6283228397369385, "end": **********.318611, "relative_end": **********.318611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.32008, "relative_start": 0.6297919750213623, "end": **********.32008, "relative_end": **********.32008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.320587, "relative_start": 0.6302988529205322, "end": **********.320587, "relative_end": **********.320587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.321049, "relative_start": 0.630760908126831, "end": **********.321049, "relative_end": **********.321049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.321537, "relative_start": 0.631248950958252, "end": **********.321537, "relative_end": **********.321537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.322014, "relative_start": 0.6317260265350342, "end": **********.322014, "relative_end": **********.322014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.322749, "relative_start": 0.6324608325958252, "end": **********.322749, "relative_end": **********.322749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.32392, "relative_start": 0.6336319446563721, "end": **********.32392, "relative_end": **********.32392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.324438, "relative_start": 0.6341500282287598, "end": **********.324438, "relative_end": **********.324438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.3249, "relative_start": 0.6346118450164795, "end": **********.3249, "relative_end": **********.3249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.32541, "relative_start": 0.6351218223571777, "end": **********.32541, "relative_end": **********.32541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.325924, "relative_start": 0.6356358528137207, "end": **********.325924, "relative_end": **********.325924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.326212, "relative_start": 0.6359238624572754, "end": **********.326212, "relative_end": **********.326212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.32662, "relative_start": 0.6363320350646973, "end": **********.32662, "relative_end": **********.32662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.327048, "relative_start": 0.6367599964141846, "end": **********.327048, "relative_end": **********.327048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.327458, "relative_start": 0.6371698379516602, "end": **********.327458, "relative_end": **********.327458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.328337, "relative_start": 0.6380488872528076, "end": **********.328337, "relative_end": **********.328337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.329636, "relative_start": 0.639348030090332, "end": **********.329636, "relative_end": **********.329636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.330138, "relative_start": 0.6398499011993408, "end": **********.330138, "relative_end": **********.330138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.330615, "relative_start": 0.640326976776123, "end": **********.330615, "relative_end": **********.330615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.331159, "relative_start": 0.6408710479736328, "end": **********.331159, "relative_end": **********.331159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.331661, "relative_start": 0.6413729190826416, "end": **********.331661, "relative_end": **********.331661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.33263, "relative_start": 0.6423418521881104, "end": **********.33263, "relative_end": **********.33263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.334421, "relative_start": 0.6441328525543213, "end": **********.334421, "relative_end": **********.334421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.33535, "relative_start": 0.6450619697570801, "end": **********.33535, "relative_end": **********.33535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.335871, "relative_start": 0.645582914352417, "end": **********.335871, "relative_end": **********.335871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.336405, "relative_start": 0.6461169719696045, "end": **********.336405, "relative_end": **********.336405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.336905, "relative_start": 0.6466169357299805, "end": **********.336905, "relative_end": **********.336905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.33737, "relative_start": 0.6470818519592285, "end": **********.33737, "relative_end": **********.33737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.337884, "relative_start": 0.6475958824157715, "end": **********.337884, "relative_end": **********.337884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.338312, "relative_start": 0.6480238437652588, "end": **********.338312, "relative_end": **********.338312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.338664, "relative_start": 0.6483759880065918, "end": **********.338664, "relative_end": **********.338664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.339807, "relative_start": 0.6495189666748047, "end": **********.339807, "relative_end": **********.339807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.340346, "relative_start": 0.6500580310821533, "end": **********.340346, "relative_end": **********.340346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.340877, "relative_start": 0.6505889892578125, "end": **********.340877, "relative_end": **********.340877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.341175, "relative_start": 0.6508870124816895, "end": **********.341175, "relative_end": **********.341175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.342356, "relative_start": 0.6520678997039795, "end": **********.342356, "relative_end": **********.342356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.342879, "relative_start": 0.6525909900665283, "end": **********.342879, "relative_end": **********.342879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.343357, "relative_start": 0.653069019317627, "end": **********.343357, "relative_end": **********.343357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.343867, "relative_start": 0.6535789966583252, "end": **********.343867, "relative_end": **********.343867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.344374, "relative_start": 0.6540858745574951, "end": **********.344374, "relative_end": **********.344374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.345193, "relative_start": 0.654904842376709, "end": **********.345193, "relative_end": **********.345193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.346438, "relative_start": 0.6561498641967773, "end": **********.346438, "relative_end": **********.346438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.34694, "relative_start": 0.6566519737243652, "end": **********.34694, "relative_end": **********.34694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.347405, "relative_start": 0.6571168899536133, "end": **********.347405, "relative_end": **********.347405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.347963, "relative_start": 0.65767502784729, "end": **********.347963, "relative_end": **********.347963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.34869, "relative_start": 0.6584019660949707, "end": **********.34869, "relative_end": **********.34869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.349217, "relative_start": 0.6589288711547852, "end": **********.349217, "relative_end": **********.349217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.350225, "relative_start": 0.6599369049072266, "end": **********.350225, "relative_end": **********.350225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.351189, "relative_start": 0.6609008312225342, "end": **********.351189, "relative_end": **********.351189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.351769, "relative_start": 0.6614809036254883, "end": **********.351769, "relative_end": **********.351769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.352322, "relative_start": 0.6620340347290039, "end": **********.352322, "relative_end": **********.352322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.353598, "relative_start": 0.6633100509643555, "end": **********.353598, "relative_end": **********.353598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.3541, "relative_start": 0.6638119220733643, "end": **********.3541, "relative_end": **********.3541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.354588, "relative_start": 0.6642999649047852, "end": **********.354588, "relative_end": **********.354588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.355124, "relative_start": 0.6648359298706055, "end": **********.355124, "relative_end": **********.355124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.355644, "relative_start": 0.665355920791626, "end": **********.355644, "relative_end": **********.355644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.356091, "relative_start": 0.6658029556274414, "end": **********.356091, "relative_end": **********.356091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.357315, "relative_start": 0.6670269966125488, "end": **********.357315, "relative_end": **********.357315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.357853, "relative_start": 0.667564868927002, "end": **********.357853, "relative_end": **********.357853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.358348, "relative_start": 0.6680598258972168, "end": **********.358348, "relative_end": **********.358348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.358879, "relative_start": 0.6685910224914551, "end": **********.358879, "relative_end": **********.358879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.359401, "relative_start": 0.6691129207611084, "end": **********.359401, "relative_end": **********.359401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.359894, "relative_start": 0.6696059703826904, "end": **********.359894, "relative_end": **********.359894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.360427, "relative_start": 0.6701388359069824, "end": **********.360427, "relative_end": **********.360427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.360852, "relative_start": 0.6705639362335205, "end": **********.360852, "relative_end": **********.360852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.361187, "relative_start": 0.6708989143371582, "end": **********.361187, "relative_end": **********.361187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.362402, "relative_start": 0.6721138954162598, "end": **********.362402, "relative_end": **********.362402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.362985, "relative_start": 0.6726968288421631, "end": **********.362985, "relative_end": **********.362985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.363501, "relative_start": 0.673213005065918, "end": **********.363501, "relative_end": **********.363501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.363805, "relative_start": 0.6735169887542725, "end": **********.363805, "relative_end": **********.363805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.365078, "relative_start": 0.6747899055480957, "end": **********.365078, "relative_end": **********.365078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.366012, "relative_start": 0.6757240295410156, "end": **********.366012, "relative_end": **********.366012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.367505, "relative_start": 0.6772170066833496, "end": **********.367505, "relative_end": **********.367505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.368377, "relative_start": 0.678088903427124, "end": **********.368377, "relative_end": **********.368377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.369, "relative_start": 0.6787118911743164, "end": **********.369, "relative_end": **********.369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.369757, "relative_start": 0.6794688701629639, "end": **********.369757, "relative_end": **********.369757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.37104, "relative_start": 0.6807520389556885, "end": **********.37104, "relative_end": **********.37104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.371501, "relative_start": 0.6812129020690918, "end": **********.371501, "relative_end": **********.371501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.371929, "relative_start": 0.6816408634185791, "end": **********.371929, "relative_end": **********.371929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.372466, "relative_start": 0.6821780204772949, "end": **********.372466, "relative_end": **********.372466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.372923, "relative_start": 0.6826348304748535, "end": **********.372923, "relative_end": **********.372923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.373213, "relative_start": 0.6829249858856201, "end": **********.373213, "relative_end": **********.373213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.373615, "relative_start": 0.6833269596099854, "end": **********.373615, "relative_end": **********.373615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.374044, "relative_start": 0.6837558746337891, "end": **********.374044, "relative_end": **********.374044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.374443, "relative_start": 0.6841549873352051, "end": **********.374443, "relative_end": **********.374443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.375339, "relative_start": 0.6850509643554688, "end": **********.375339, "relative_end": **********.375339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.376509, "relative_start": 0.6862208843231201, "end": **********.376509, "relative_end": **********.376509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.37699, "relative_start": 0.6867020130157471, "end": **********.37699, "relative_end": **********.37699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.377428, "relative_start": 0.6871399879455566, "end": **********.377428, "relative_end": **********.377428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.377953, "relative_start": 0.6876649856567383, "end": **********.377953, "relative_end": **********.377953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.378424, "relative_start": 0.6881358623504639, "end": **********.378424, "relative_end": **********.378424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.378835, "relative_start": 0.688546895980835, "end": **********.378835, "relative_end": **********.378835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.379973, "relative_start": 0.6896848678588867, "end": **********.379973, "relative_end": **********.379973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.38044, "relative_start": 0.6901519298553467, "end": **********.38044, "relative_end": **********.38044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.380881, "relative_start": 0.6905930042266846, "end": **********.380881, "relative_end": **********.380881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.381388, "relative_start": 0.6910998821258545, "end": **********.381388, "relative_end": **********.381388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.382059, "relative_start": 0.6917710304260254, "end": **********.382059, "relative_end": **********.382059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.383381, "relative_start": 0.6930928230285645, "end": **********.383381, "relative_end": **********.383381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.384281, "relative_start": 0.6939928531646729, "end": **********.384281, "relative_end": **********.384281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.384754, "relative_start": 0.6944658756256104, "end": **********.384754, "relative_end": **********.384754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.385089, "relative_start": 0.694800853729248, "end": **********.385089, "relative_end": **********.385089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.386187, "relative_start": 0.6958990097045898, "end": **********.386187, "relative_end": **********.386187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.386684, "relative_start": 0.6963958740234375, "end": **********.386684, "relative_end": **********.386684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.387163, "relative_start": 0.6968748569488525, "end": **********.387163, "relative_end": **********.387163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.387463, "relative_start": 0.6971750259399414, "end": **********.387463, "relative_end": **********.387463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.388592, "relative_start": 0.6983039379119873, "end": **********.388592, "relative_end": **********.388592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.389099, "relative_start": 0.6988108158111572, "end": **********.389099, "relative_end": **********.389099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.38956, "relative_start": 0.6992719173431396, "end": **********.38956, "relative_end": **********.38956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.390065, "relative_start": 0.6997768878936768, "end": **********.390065, "relative_end": **********.390065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.390557, "relative_start": 0.7002689838409424, "end": **********.390557, "relative_end": **********.390557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.391309, "relative_start": 0.7010209560394287, "end": **********.391309, "relative_end": **********.391309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.392533, "relative_start": 0.7022449970245361, "end": **********.392533, "relative_end": **********.392533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.393014, "relative_start": 0.702725887298584, "end": **********.393014, "relative_end": **********.393014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.393464, "relative_start": 0.7031760215759277, "end": **********.393464, "relative_end": **********.393464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.393979, "relative_start": 0.7036910057067871, "end": **********.393979, "relative_end": **********.393979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.394483, "relative_start": 0.7041950225830078, "end": **********.394483, "relative_end": **********.394483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.39478, "relative_start": 0.7044918537139893, "end": **********.39478, "relative_end": **********.39478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.395189, "relative_start": 0.7049009799957275, "end": **********.395189, "relative_end": **********.395189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.395637, "relative_start": 0.7053489685058594, "end": **********.395637, "relative_end": **********.395637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.396067, "relative_start": 0.7057788372039795, "end": **********.396067, "relative_end": **********.396067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.396991, "relative_start": 0.7067029476165771, "end": **********.396991, "relative_end": **********.396991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.39824, "relative_start": 0.7079520225524902, "end": **********.39824, "relative_end": **********.39824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.399789, "relative_start": 0.7095010280609131, "end": **********.399789, "relative_end": **********.399789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.400682, "relative_start": 0.7103939056396484, "end": **********.400682, "relative_end": **********.400682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.40138, "relative_start": 0.7110919952392578, "end": **********.40138, "relative_end": **********.40138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.401904, "relative_start": 0.711616039276123, "end": **********.401904, "relative_end": **********.401904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.402359, "relative_start": 0.7120709419250488, "end": **********.402359, "relative_end": **********.402359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.403567, "relative_start": 0.7132790088653564, "end": **********.403567, "relative_end": **********.403567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.404065, "relative_start": 0.7137768268585205, "end": **********.404065, "relative_end": **********.404065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.404579, "relative_start": 0.7142908573150635, "end": **********.404579, "relative_end": **********.404579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.405115, "relative_start": 0.7148268222808838, "end": **********.405115, "relative_end": **********.405115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.405622, "relative_start": 0.7153339385986328, "end": **********.405622, "relative_end": **********.405622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.406068, "relative_start": 0.7157800197601318, "end": **********.406068, "relative_end": **********.406068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.406593, "relative_start": 0.7163050174713135, "end": **********.406593, "relative_end": **********.406593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.407012, "relative_start": 0.7167239189147949, "end": **********.407012, "relative_end": **********.407012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.407347, "relative_start": 0.7170588970184326, "end": **********.407347, "relative_end": **********.407347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.408535, "relative_start": 0.7182469367980957, "end": **********.408535, "relative_end": **********.408535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.409088, "relative_start": 0.7187998294830322, "end": **********.409088, "relative_end": **********.409088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.409613, "relative_start": 0.7193248271942139, "end": **********.409613, "relative_end": **********.409613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.409927, "relative_start": 0.7196388244628906, "end": **********.409927, "relative_end": **********.409927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.411159, "relative_start": 0.7208709716796875, "end": **********.411159, "relative_end": **********.411159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.411668, "relative_start": 0.7213799953460693, "end": **********.411668, "relative_end": **********.411668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.412151, "relative_start": 0.7218630313873291, "end": **********.412151, "relative_end": **********.412151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.412709, "relative_start": 0.7224209308624268, "end": **********.412709, "relative_end": **********.412709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.413223, "relative_start": 0.7229349613189697, "end": **********.413223, "relative_end": **********.413223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.41402, "relative_start": 0.7237319946289062, "end": **********.41402, "relative_end": **********.41402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.417047, "relative_start": 0.7267589569091797, "end": **********.417047, "relative_end": **********.417047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.417994, "relative_start": 0.7277059555053711, "end": **********.417994, "relative_end": **********.417994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.418733, "relative_start": 0.7284448146820068, "end": **********.418733, "relative_end": **********.418733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.419656, "relative_start": 0.7293679714202881, "end": **********.419656, "relative_end": **********.419656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.42055, "relative_start": 0.730262041091919, "end": **********.42055, "relative_end": **********.42055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.421085, "relative_start": 0.7307968139648438, "end": **********.421085, "relative_end": **********.421085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.421808, "relative_start": 0.7315199375152588, "end": **********.421808, "relative_end": **********.421808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.422567, "relative_start": 0.7322788238525391, "end": **********.422567, "relative_end": **********.422567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.423281, "relative_start": 0.7329928874969482, "end": **********.423281, "relative_end": **********.423281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.425084, "relative_start": 0.7347960472106934, "end": **********.425084, "relative_end": **********.425084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.42746, "relative_start": 0.7371718883514404, "end": **********.42746, "relative_end": **********.42746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.428125, "relative_start": 0.7378368377685547, "end": **********.428125, "relative_end": **********.428125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.428612, "relative_start": 0.7383239269256592, "end": **********.428612, "relative_end": **********.428612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.429176, "relative_start": 0.7388880252838135, "end": **********.429176, "relative_end": **********.429176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.429705, "relative_start": 0.7394168376922607, "end": **********.429705, "relative_end": **********.429705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.43058, "relative_start": 0.7402918338775635, "end": **********.43058, "relative_end": **********.43058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.432234, "relative_start": 0.7419459819793701, "end": **********.432234, "relative_end": **********.432234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.433242, "relative_start": 0.7429540157318115, "end": **********.433242, "relative_end": **********.433242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.434192, "relative_start": 0.7439038753509521, "end": **********.434192, "relative_end": **********.434192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.434813, "relative_start": 0.7445249557495117, "end": **********.434813, "relative_end": **********.434813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.435351, "relative_start": 0.7450628280639648, "end": **********.435351, "relative_end": **********.435351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.435792, "relative_start": 0.7455039024353027, "end": **********.435792, "relative_end": **********.435792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.436276, "relative_start": 0.7459878921508789, "end": **********.436276, "relative_end": **********.436276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.436696, "relative_start": 0.7464079856872559, "end": **********.436696, "relative_end": **********.436696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.437026, "relative_start": 0.7467379570007324, "end": **********.437026, "relative_end": **********.437026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.438111, "relative_start": 0.7478229999542236, "end": **********.438111, "relative_end": **********.438111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.43862, "relative_start": 0.7483320236206055, "end": **********.43862, "relative_end": **********.43862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.439159, "relative_start": 0.748870849609375, "end": **********.439159, "relative_end": **********.439159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.439449, "relative_start": 0.7491610050201416, "end": **********.439449, "relative_end": **********.439449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.440593, "relative_start": 0.7503049373626709, "end": **********.440593, "relative_end": **********.440593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.441074, "relative_start": 0.7507858276367188, "end": **********.441074, "relative_end": **********.441074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.441517, "relative_start": 0.7512290477752686, "end": **********.441517, "relative_end": **********.441517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.442018, "relative_start": 0.7517299652099609, "end": **********.442018, "relative_end": **********.442018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.442514, "relative_start": 0.7522258758544922, "end": **********.442514, "relative_end": **********.442514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.443239, "relative_start": 0.75295090675354, "end": **********.443239, "relative_end": **********.443239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.444402, "relative_start": 0.7541139125823975, "end": **********.444402, "relative_end": **********.444402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.444871, "relative_start": 0.7545828819274902, "end": **********.444871, "relative_end": **********.444871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.445322, "relative_start": 0.7550339698791504, "end": **********.445322, "relative_end": **********.445322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.445846, "relative_start": 0.7555580139160156, "end": **********.445846, "relative_end": **********.445846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.446641, "relative_start": 0.7563529014587402, "end": **********.446641, "relative_end": **********.446641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.446989, "relative_start": 0.7567009925842285, "end": **********.446989, "relative_end": **********.446989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.447418, "relative_start": 0.7571299076080322, "end": **********.447418, "relative_end": **********.447418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.447878, "relative_start": 0.7575898170471191, "end": **********.447878, "relative_end": **********.447878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.448299, "relative_start": 0.7580108642578125, "end": **********.448299, "relative_end": **********.448299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.449369, "relative_start": 0.7590808868408203, "end": **********.449369, "relative_end": **********.449369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.451204, "relative_start": 0.760915994644165, "end": **********.451204, "relative_end": **********.451204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.45174, "relative_start": 0.7614519596099854, "end": **********.45174, "relative_end": **********.45174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.452194, "relative_start": 0.7619059085845947, "end": **********.452194, "relative_end": **********.452194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.452699, "relative_start": 0.7624108791351318, "end": **********.452699, "relative_end": **********.452699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.453198, "relative_start": 0.7629098892211914, "end": **********.453198, "relative_end": **********.453198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.453639, "relative_start": 0.7633509635925293, "end": **********.453639, "relative_end": **********.453639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.454776, "relative_start": 0.7644879817962646, "end": **********.454776, "relative_end": **********.454776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.455245, "relative_start": 0.7649569511413574, "end": **********.455245, "relative_end": **********.455245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.455739, "relative_start": 0.7654509544372559, "end": **********.455739, "relative_end": **********.455739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.456238, "relative_start": 0.7659499645233154, "end": **********.456238, "relative_end": **********.456238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.456734, "relative_start": 0.7664458751678467, "end": **********.456734, "relative_end": **********.456734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.457173, "relative_start": 0.7668850421905518, "end": **********.457173, "relative_end": **********.457173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.457655, "relative_start": 0.767366886138916, "end": **********.457655, "relative_end": **********.457655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.458067, "relative_start": 0.7677788734436035, "end": **********.458067, "relative_end": **********.458067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.458418, "relative_start": 0.768129825592041, "end": **********.458418, "relative_end": **********.458418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.459547, "relative_start": 0.769258975982666, "end": **********.459547, "relative_end": **********.459547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.460091, "relative_start": 0.7698030471801758, "end": **********.460091, "relative_end": **********.460091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.460578, "relative_start": 0.7702898979187012, "end": **********.460578, "relative_end": **********.460578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.460876, "relative_start": 0.7705879211425781, "end": **********.460876, "relative_end": **********.460876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.462036, "relative_start": 0.7717478275299072, "end": **********.462036, "relative_end": **********.462036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.462926, "relative_start": 0.7726378440856934, "end": **********.462926, "relative_end": **********.462926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.463496, "relative_start": 0.7732079029083252, "end": **********.463496, "relative_end": **********.463496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.464038, "relative_start": 0.773749828338623, "end": **********.464038, "relative_end": **********.464038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.464524, "relative_start": 0.7742359638214111, "end": **********.464524, "relative_end": **********.464524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.465539, "relative_start": 0.7752509117126465, "end": **********.465539, "relative_end": **********.465539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.467467, "relative_start": 0.7771790027618408, "end": **********.467467, "relative_end": **********.467467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.468026, "relative_start": 0.7777378559112549, "end": **********.468026, "relative_end": **********.468026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.468483, "relative_start": 0.7781949043273926, "end": **********.468483, "relative_end": **********.468483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.468981, "relative_start": 0.7786929607391357, "end": **********.468981, "relative_end": **********.468981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.469467, "relative_start": 0.7791788578033447, "end": **********.469467, "relative_end": **********.469467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.469749, "relative_start": 0.7794609069824219, "end": **********.469749, "relative_end": **********.469749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.470164, "relative_start": 0.7798759937286377, "end": **********.470164, "relative_end": **********.470164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.470658, "relative_start": 0.7803699970245361, "end": **********.470658, "relative_end": **********.470658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.471096, "relative_start": 0.7808079719543457, "end": **********.471096, "relative_end": **********.471096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.471613, "relative_start": 0.7813248634338379, "end": **********.471613, "relative_end": **********.471613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.472789, "relative_start": 0.7825009822845459, "end": **********.472789, "relative_end": **********.472789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.473304, "relative_start": 0.7830159664154053, "end": **********.473304, "relative_end": **********.473304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.473796, "relative_start": 0.7835078239440918, "end": **********.473796, "relative_end": **********.473796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.474422, "relative_start": 0.7841339111328125, "end": **********.474422, "relative_end": **********.474422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.474939, "relative_start": 0.7846510410308838, "end": **********.474939, "relative_end": **********.474939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.475387, "relative_start": 0.7850990295410156, "end": **********.475387, "relative_end": **********.475387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.476577, "relative_start": 0.7862889766693115, "end": **********.476577, "relative_end": **********.476577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.47706, "relative_start": 0.7867720127105713, "end": **********.47706, "relative_end": **********.47706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.47751, "relative_start": 0.7872219085693359, "end": **********.47751, "relative_end": **********.47751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.478301, "relative_start": 0.7880129814147949, "end": **********.478301, "relative_end": **********.478301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.478877, "relative_start": 0.7885890007019043, "end": **********.478877, "relative_end": **********.478877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.479372, "relative_start": 0.7890839576721191, "end": **********.479372, "relative_end": **********.479372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.479883, "relative_start": 0.7895948886871338, "end": **********.479883, "relative_end": **********.479883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.480293, "relative_start": 0.7900049686431885, "end": **********.480293, "relative_end": **********.480293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.48062, "relative_start": 0.7903318405151367, "end": **********.48062, "relative_end": **********.48062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.48199, "relative_start": 0.7917020320892334, "end": **********.48199, "relative_end": **********.48199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.483174, "relative_start": 0.7928860187530518, "end": **********.483174, "relative_end": **********.483174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.484, "relative_start": 0.7937119007110596, "end": **********.484, "relative_end": **********.484, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.484355, "relative_start": 0.7940669059753418, "end": **********.484355, "relative_end": **********.484355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.485618, "relative_start": 0.7953300476074219, "end": **********.485618, "relative_end": **********.485618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.486105, "relative_start": 0.7958168983459473, "end": **********.486105, "relative_end": **********.486105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.486524, "relative_start": 0.7962360382080078, "end": **********.486524, "relative_end": **********.486524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.487023, "relative_start": 0.7967350482940674, "end": **********.487023, "relative_end": **********.487023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.48748, "relative_start": 0.797191858291626, "end": **********.48748, "relative_end": **********.48748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.488197, "relative_start": 0.7979090213775635, "end": **********.488197, "relative_end": **********.488197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.48936, "relative_start": 0.7990720272064209, "end": **********.48936, "relative_end": **********.48936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.489843, "relative_start": 0.7995548248291016, "end": **********.489843, "relative_end": **********.489843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.490293, "relative_start": 0.8000049591064453, "end": **********.490293, "relative_end": **********.490293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.490799, "relative_start": 0.8005108833312988, "end": **********.490799, "relative_end": **********.490799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.491273, "relative_start": 0.8009848594665527, "end": **********.491273, "relative_end": **********.491273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.491553, "relative_start": 0.8012650012969971, "end": **********.491553, "relative_end": **********.491553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.49197, "relative_start": 0.8016819953918457, "end": **********.49197, "relative_end": **********.49197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.rows", "start": **********.49243, "relative_start": 0.8021419048309326, "end": **********.49243, "relative_end": **********.49243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.index", "start": **********.493006, "relative_start": 0.802717924118042, "end": **********.493006, "relative_end": **********.493006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.494129, "relative_start": 0.8038408756256104, "end": **********.494129, "relative_end": **********.494129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.49601, "relative_start": 0.8057219982147217, "end": **********.49601, "relative_end": **********.49601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "start": **********.496805, "relative_start": 0.8065168857574463, "end": **********.496805, "relative_end": **********.496805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.498909, "relative_start": 0.8086209297180176, "end": **********.500961, "relative_end": **********.500961, "duration": 0.0020520687103271484, "duration_str": "2.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 30461312, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 553, "nb_templates": 553, "templates": [{"name": "1x livewire.category-table", "param_count": null, "params": [], "start": **********.116259, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/category-table.blade.phplivewire.category-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fcategory-table.blade.php&line=1", "ajax": false, "filename": "category-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.category-table"}, {"name": "16x e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "param_count": null, "params": [], "start": **********.120586, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 16, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::heading"}, {"name": "77x e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "param_count": null, "params": [], "start": **********.121673, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 77, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button.index"}, {"name": "63x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "param_count": null, "params": [], "start": **********.123835, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 63, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "param_count": null, "params": [], "start": **********.124747, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/plus.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fplus.blade.php&line=1", "ajax": false, "filename": "plus.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus"}, {"name": "77x e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "param_count": null, "params": [], "start": **********.125317, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}, "render_count": 77, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link"}, {"name": "77x e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "param_count": null, "params": [], "start": **********.125973, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}, "render_count": 77, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip"}, {"name": "31x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "param_count": null, "params": [], "start": **********.126607, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/trigger.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Ftrigger.blade.php&line=1", "ajax": false, "filename": "trigger.blade.php", "line": "?"}, "render_count": 31, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "param_count": null, "params": [], "start": **********.127296, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "param_count": null, "params": [], "start": **********.130411, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/magnifying-glass.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmagnifying-glass.blade.php&line=1", "ajax": false, "filename": "magnifying-glass.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass"}, {"name": "16x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "param_count": null, "params": [], "start": **********.132375, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 16, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "param_count": null, "params": [], "start": **********.133902, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/clearable.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Fclearable.blade.php&line=1", "ajax": false, "filename": "clearable.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable"}, {"name": "16x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "param_count": null, "params": [], "start": **********.135046, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/x-mark.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fx-mark.blade.php&line=1", "ajax": false, "filename": "x-mark.blade.php", "line": "?"}, "render_count": 16, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "param_count": null, "params": [], "start": **********.138, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-field"}, {"name": "3x components.table.column", "param_count": null, "params": [], "start": **********.139015, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/column.blade.phpcomponents.table.column", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}, "render_count": 3, "name_original": "components.table.column"}, {"name": "1x components.table.columns", "param_count": null, "params": [], "start": **********.140362, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/columns.blade.phpcomponents.table.columns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcolumns.blade.php&line=1", "ajax": false, "filename": "columns.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.columns"}, {"name": "45x components.table.cell", "param_count": null, "params": [], "start": **********.141023, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/cell.blade.phpcomponents.table.cell", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}, "render_count": 45, "name_original": "components.table.cell"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "param_count": null, "params": [], "start": **********.146939, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/pencil-square.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fpencil-square.blade.php&line=1", "ajax": false, "filename": "pencil-square.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "param_count": null, "params": [], "start": **********.152529, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/trash.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Ftrash.blade.php&line=1", "ajax": false, "filename": "trash.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "param_count": null, "params": [], "start": **********.155439, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/subheading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsubheading.blade.php&line=1", "ajax": false, "filename": "subheading.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::subheading"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "param_count": null, "params": [], "start": **********.156257, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/spacer.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fspacer.blade.php&line=1", "ajax": false, "filename": "spacer.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::spacer"}, {"name": "30x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "param_count": null, "params": [], "start": **********.159013, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/close.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 30, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "param_count": null, "params": [], "start": **********.162547, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index"}, {"name": "15x components.table.row", "param_count": null, "params": [], "start": **********.169158, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/row.blade.phpcomponents.table.row", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}, "render_count": 15, "name_original": "components.table.row"}, {"name": "1x components.table.rows", "param_count": null, "params": [], "start": **********.492371, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/rows.blade.phpcomponents.table.rows", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Frows.blade.php&line=1", "ajax": false, "filename": "rows.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.rows"}, {"name": "1x components.table.index", "param_count": null, "params": [], "start": **********.492953, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/index.blade.phpcomponents.table.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.index"}, {"name": "1x livewire::tailwind", "param_count": null, "params": [], "start": **********.493983, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire::tailwind"}, {"name": "1x components.card", "param_count": null, "params": [], "start": **********.495952, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.card"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "param_count": null, "params": [], "start": **********.496741, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/card/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::card.index"}]}, "queries": {"count": 6, "nb_statements": 5, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0058200000000000005, "accumulated_duration_str": "5.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.036419, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr' limit 1", "type": "query", "params": [], "bindings": ["kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.045474, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 51.375}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.0753832, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 51.375, "width_percent": 10.653}, {"sql": "select count(*) as aggregate from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.102046, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "CategoryTable.php:53", "source": {"index": 19, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryTable.php&line=53", "ajax": false, "filename": "CategoryTable.php", "line": "53"}, "connection": "daily", "explain": null, "start_percent": 62.027, "width_percent": 13.23}, {"sql": "select `id`, `name`, `parent_id` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null order by `name` asc limit 15 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.1052139, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "CategoryTable.php:53", "source": {"index": 19, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryTable.php&line=53", "ajax": false, "filename": "CategoryTable.php", "line": "53"}, "connection": "daily", "explain": null, "start_percent": 75.258, "width_percent": 13.23}, {"sql": "select * from `categories` where `categories`.`id` in (1, 2, 4, 5, 6, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.1094651, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "CategoryTable.php:53", "source": {"index": 24, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryTable.php&line=53", "ajax": false, "filename": "CategoryTable.php", "line": "53"}, "connection": "daily", "explain": null, "start_percent": 88.488, "width_percent": 11.512}]}, "models": {"data": {"App\\Models\\Category": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 24, "is_counter": true}, "livewire": {"data": {"category-table #jmyLn5BfCANcVRyek55z": "array:4 [\n  \"data\" => array:2 [\n    \"search\" => \"\"\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"category-table\"\n  \"component\" => \"App\\Livewire\\CategoryTable\"\n  \"id\" => \"jmyLn5BfCANcVRyek55z\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "815ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2082260979 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2082260979\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-910263203 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"349 characters\">{&quot;data&quot;:{&quot;search&quot;:&quot;Bon&quot;,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;jmyLn5BfCANcVRyek55z&quot;,&quot;name&quot;:&quot;category-table&quot;,&quot;path&quot;:&quot;categories&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-706289484-0&quot;:[&quot;div&quot;,&quot;ORqJ4lxMe3Sy4pCgQmbR&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;9b5d48fb78d053b01795743b8eb62d3f957af908faed99a30113252c7625243d&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>search</span>\" => \"\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-910263203\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1025046871 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">522</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/categories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"751 characters\">PHPSESSID=aa0mh29tpvppc77i3l7peen7g0; XSRF-TOKEN=eyJpdiI6ImpVeC9NWUhWWTRJN1hSSlU0ZDlKVGc9PSIsInZhbHVlIjoiNzBjNzVWdDh0SXBNUHB6ZTM4b2hGemxaeGowTHdGQ0dNUURtQzNSbERLSmFkemZmUFMwd2ZkdlRqT2UzNU9xQ2dNTlZoNVRPZDRFaWdVZjR3NStJTmp4cnBhRE5uQVVtTEcrcForZmhkSzN6MUR2QTlaS3cxN0plVnViVk1iTGUiLCJtYWMiOiIwMzAxZGIyYTY2ZGQ5YTZiZmYxNmUyMWEzZTk5MzA3M2YwMjNhOWU5ZjgyZjI1MGQ5NzFiZGQzYzE2M2IwZTA0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImRSWVhzcnpuM2EzT21zSGpkYWQvVEE9PSIsInZhbHVlIjoiMjVTWkovcE5KeW5hRWdGUG5oT2dQSXpkKzYrdFVKZzVGTWpLOUh3UEFlRnI3WXJESXF0dms2ZmNpZWcrM0c2Szc5Yy9lQllrSVZPM2NaTWRFNEFORnpYcHkxaTJSVTgwdG9CQ0JibVkzREJlRG90ZEZlU1FGUzZ6QldJTW5CM2kiLCJtYWMiOiJkN2E0OWI0ODhlZjAwNzYzYTY3NDJjYjA3YTY4MzY2Zjg3NmQ1OGQ3ZTkzNWQzNDJjMThhOTIzODE1YzlhNDM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025046871\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-421912273 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-421912273\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-81555516 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 25 May 2025 09:16:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81555516\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-120632239 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/categories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-120632239\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}