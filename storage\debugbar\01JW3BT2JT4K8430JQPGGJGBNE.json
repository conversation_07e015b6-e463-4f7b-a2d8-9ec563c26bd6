{"__meta": {"id": "01JW3BT2JT4K8430JQPGGJGBNE", "datetime": "2025-05-25 09:19:01", "utime": **********.724638, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.226493, "end": **********.724663, "duration": 1.4981701374053955, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": **********.226493, "relative_start": 0, "end": **********.831593, "relative_end": **********.831593, "duration": 0.***************, "duration_str": "605ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.831612, "relative_start": 0.****************, "end": **********.724666, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "893ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.899163, "relative_start": 0.****************, "end": **********.904293, "relative_end": **********.904293, "duration": 0.*****************, "duration_str": "5.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.planned-spending-form", "start": **********.12051, "relative_start": 0.***************, "end": **********.12051, "relative_end": **********.12051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.126268, "relative_start": 0.****************, "end": **********.126268, "relative_end": **********.126268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.129677, "relative_start": 0.903184175491333, "end": **********.129677, "relative_end": **********.129677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.132021, "relative_start": 0.9055280685424805, "end": **********.132021, "relative_end": **********.132021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.137738, "relative_start": 0.9112451076507568, "end": **********.137738, "relative_end": **********.137738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.140788, "relative_start": 0.9142951965332031, "end": **********.140788, "relative_end": **********.140788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.142385, "relative_start": 0.9158921241760254, "end": **********.142385, "relative_end": **********.142385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.categories", "start": **********.146358, "relative_start": 0.9198651313781738, "end": **********.146358, "relative_end": **********.146358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.149336, "relative_start": 0.9228432178497314, "end": **********.149336, "relative_end": **********.149336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.search", "start": **********.150624, "relative_start": 0.9241311550140381, "end": **********.150624, "relative_end": **********.150624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.magnifying-glass", "start": **********.153171, "relative_start": 0.9266781806945801, "end": **********.153171, "relative_end": **********.153171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.156114, "relative_start": 0.9296212196350098, "end": **********.156114, "relative_end": **********.156114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.156904, "relative_start": 0.9304111003875732, "end": **********.156904, "relative_end": **********.156904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.158938, "relative_start": 0.9324450492858887, "end": **********.158938, "relative_end": **********.158938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.160124, "relative_start": 0.933631181716919, "end": **********.160124, "relative_end": **********.160124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.plus", "start": **********.162142, "relative_start": 0.9356491565704346, "end": **********.162142, "relative_end": **********.162142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.163146, "relative_start": 0.9366531372070312, "end": **********.163146, "relative_end": **********.163146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.165393, "relative_start": 0.9389002323150635, "end": **********.165393, "relative_end": **********.165393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.166379, "relative_start": 0.9398860931396484, "end": **********.166379, "relative_end": **********.166379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.trigger", "start": **********.16747, "relative_start": 0.9409770965576172, "end": **********.16747, "relative_end": **********.16747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.168695, "relative_start": 0.942202091217041, "end": **********.168695, "relative_end": **********.168695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.169835, "relative_start": 0.9433422088623047, "end": **********.169835, "relative_end": **********.169835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.172574, "relative_start": 0.9460811614990234, "end": **********.172574, "relative_end": **********.172574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.173827, "relative_start": 0.9473340511322021, "end": **********.173827, "relative_end": **********.173827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.174953, "relative_start": 0.9484601020812988, "end": **********.174953, "relative_end": **********.174953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.17776, "relative_start": 0.9512670040130615, "end": **********.17776, "relative_end": **********.17776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.179076, "relative_start": 0.9525830745697021, "end": **********.179076, "relative_end": **********.179076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.181325, "relative_start": 0.9548320770263672, "end": **********.181325, "relative_end": **********.181325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.18225, "relative_start": 0.9557571411132812, "end": **********.18225, "relative_end": **********.18225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.183359, "relative_start": 0.9568660259246826, "end": **********.183359, "relative_end": **********.183359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.18449, "relative_start": 0.9579970836639404, "end": **********.18449, "relative_end": **********.18449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.184983, "relative_start": 0.9584901332855225, "end": **********.184983, "relative_end": **********.184983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.18661, "relative_start": 0.9601171016693115, "end": **********.18661, "relative_end": **********.18661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.188152, "relative_start": 0.9616591930389404, "end": **********.188152, "relative_end": **********.188152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.18989, "relative_start": 0.9633970260620117, "end": **********.18989, "relative_end": **********.18989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.19109, "relative_start": 0.964597225189209, "end": **********.19109, "relative_end": **********.19109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.191923, "relative_start": 0.9654300212860107, "end": **********.191923, "relative_end": **********.191923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.192855, "relative_start": 0.9663619995117188, "end": **********.192855, "relative_end": **********.192855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.1938, "relative_start": 0.9673070907592773, "end": **********.1938, "relative_end": **********.1938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.195201, "relative_start": 0.9687080383300781, "end": **********.195201, "relative_end": **********.195201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.196153, "relative_start": 0.9696600437164307, "end": **********.196153, "relative_end": **********.196153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.197698, "relative_start": 0.9712052345275879, "end": **********.197698, "relative_end": **********.197698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.19892, "relative_start": 0.9724271297454834, "end": **********.19892, "relative_end": **********.19892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.20046, "relative_start": 0.9739670753479004, "end": **********.20046, "relative_end": **********.20046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.202615, "relative_start": 0.9761221408843994, "end": **********.202615, "relative_end": **********.202615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.203489, "relative_start": 0.9769961833953857, "end": **********.203489, "relative_end": **********.203489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.204723, "relative_start": 0.9782299995422363, "end": **********.204723, "relative_end": **********.204723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.205751, "relative_start": 0.9792580604553223, "end": **********.205751, "relative_end": **********.205751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.206712, "relative_start": 0.9802191257476807, "end": **********.206712, "relative_end": **********.206712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.207379, "relative_start": 0.9808862209320068, "end": **********.207379, "relative_end": **********.207379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.208037, "relative_start": 0.981544017791748, "end": **********.208037, "relative_end": **********.208037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.208547, "relative_start": 0.9820542335510254, "end": **********.208547, "relative_end": **********.208547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.208913, "relative_start": 0.9824202060699463, "end": **********.208913, "relative_end": **********.208913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.209642, "relative_start": 0.9831490516662598, "end": **********.209642, "relative_end": **********.209642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.210552, "relative_start": 0.9840590953826904, "end": **********.210552, "relative_end": **********.210552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.212239, "relative_start": 0.9857461452484131, "end": **********.212239, "relative_end": **********.212239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.213354, "relative_start": 0.9868612289428711, "end": **********.213354, "relative_end": **********.213354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.214485, "relative_start": 0.9879920482635498, "end": **********.214485, "relative_end": **********.214485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.215381, "relative_start": 0.9888880252838135, "end": **********.215381, "relative_end": **********.215381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.216155, "relative_start": 0.9896621704101562, "end": **********.216155, "relative_end": **********.216155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.218651, "relative_start": 0.9921581745147705, "end": **********.218651, "relative_end": **********.218651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.219726, "relative_start": 0.9932332038879395, "end": **********.219726, "relative_end": **********.219726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.221147, "relative_start": 0.9946541786193848, "end": **********.221147, "relative_end": **********.221147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.221953, "relative_start": 0.995460033416748, "end": **********.221953, "relative_end": **********.221953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.222705, "relative_start": 0.9962120056152344, "end": **********.222705, "relative_end": **********.222705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.22334, "relative_start": 0.9968471527099609, "end": **********.22334, "relative_end": **********.22334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.223816, "relative_start": 0.9973230361938477, "end": **********.223816, "relative_end": **********.223816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.224373, "relative_start": 0.997880220413208, "end": **********.224373, "relative_end": **********.224373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.224945, "relative_start": 0.9984521865844727, "end": **********.224945, "relative_end": **********.224945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.226088, "relative_start": 0.9995951652526855, "end": **********.226088, "relative_end": **********.226088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.228909, "relative_start": 1.0024161338806152, "end": **********.228909, "relative_end": **********.228909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.230323, "relative_start": 1.0038301944732666, "end": **********.230323, "relative_end": **********.230323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.231339, "relative_start": 1.0048460960388184, "end": **********.231339, "relative_end": **********.231339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.232568, "relative_start": 1.006075143814087, "end": **********.232568, "relative_end": **********.232568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.233686, "relative_start": 1.0071930885314941, "end": **********.233686, "relative_end": **********.233686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.234534, "relative_start": 1.0080411434173584, "end": **********.234534, "relative_end": **********.234534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.236071, "relative_start": 1.0095782279968262, "end": **********.236071, "relative_end": **********.236071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.237013, "relative_start": 1.0105202198028564, "end": **********.237013, "relative_end": **********.237013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.238072, "relative_start": 1.0115790367126465, "end": **********.238072, "relative_end": **********.238072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.238823, "relative_start": 1.0123300552368164, "end": **********.238823, "relative_end": **********.238823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.239315, "relative_start": 1.012822151184082, "end": **********.239315, "relative_end": **********.239315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.239883, "relative_start": 1.013390064239502, "end": **********.239883, "relative_end": **********.239883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.240774, "relative_start": 1.0142810344696045, "end": **********.240774, "relative_end": **********.240774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.242456, "relative_start": 1.015963077545166, "end": **********.242456, "relative_end": **********.242456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.244577, "relative_start": 1.0180840492248535, "end": **********.244577, "relative_end": **********.244577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.24596, "relative_start": 1.0194671154022217, "end": **********.24596, "relative_end": **********.24596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.246945, "relative_start": 1.0204520225524902, "end": **********.246945, "relative_end": **********.246945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.248357, "relative_start": 1.0218641757965088, "end": **********.248357, "relative_end": **********.248357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.249498, "relative_start": 1.0230050086975098, "end": **********.249498, "relative_end": **********.249498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.250963, "relative_start": 1.0244700908660889, "end": **********.250963, "relative_end": **********.250963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.25288, "relative_start": 1.0263872146606445, "end": **********.25288, "relative_end": **********.25288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.254276, "relative_start": 1.0277831554412842, "end": **********.254276, "relative_end": **********.254276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.255528, "relative_start": 1.0290350914001465, "end": **********.255528, "relative_end": **********.255528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.256334, "relative_start": 1.0298411846160889, "end": **********.256334, "relative_end": **********.256334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.25681, "relative_start": 1.0303170680999756, "end": **********.25681, "relative_end": **********.25681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.257423, "relative_start": 1.0309300422668457, "end": **********.257423, "relative_end": **********.257423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.25802, "relative_start": 1.031527042388916, "end": **********.25802, "relative_end": **********.25802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.259087, "relative_start": 1.0325942039489746, "end": **********.259087, "relative_end": **********.259087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.260895, "relative_start": 1.0344021320343018, "end": **********.260895, "relative_end": **********.260895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.262381, "relative_start": 1.0358881950378418, "end": **********.262381, "relative_end": **********.262381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.264827, "relative_start": 1.0383341312408447, "end": **********.264827, "relative_end": **********.264827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.266002, "relative_start": 1.0395090579986572, "end": **********.266002, "relative_end": **********.266002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.267347, "relative_start": 1.0408542156219482, "end": **********.267347, "relative_end": **********.267347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.268567, "relative_start": 1.042074203491211, "end": **********.268567, "relative_end": **********.268567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.270872, "relative_start": 1.0443792343139648, "end": **********.270872, "relative_end": **********.270872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.272266, "relative_start": 1.0457730293273926, "end": **********.272266, "relative_end": **********.272266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.273914, "relative_start": 1.0474212169647217, "end": **********.273914, "relative_end": **********.273914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.274908, "relative_start": 1.048415184020996, "end": **********.274908, "relative_end": **********.274908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.275601, "relative_start": 1.0491080284118652, "end": **********.275601, "relative_end": **********.275601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.27728, "relative_start": 1.0507872104644775, "end": **********.27728, "relative_end": **********.27728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.279507, "relative_start": 1.0530140399932861, "end": **********.279507, "relative_end": **********.279507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.281139, "relative_start": 1.0546460151672363, "end": **********.281139, "relative_end": **********.281139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.282336, "relative_start": 1.0558431148529053, "end": **********.282336, "relative_end": **********.282336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.283762, "relative_start": 1.0572690963745117, "end": **********.283762, "relative_end": **********.283762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.284559, "relative_start": 1.0580661296844482, "end": **********.284559, "relative_end": **********.284559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.285153, "relative_start": 1.0586600303649902, "end": **********.285153, "relative_end": **********.285153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.285788, "relative_start": 1.0592951774597168, "end": **********.285788, "relative_end": **********.285788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.286376, "relative_start": 1.0598831176757812, "end": **********.286376, "relative_end": **********.286376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.287406, "relative_start": 1.0609130859375, "end": **********.287406, "relative_end": **********.287406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.288151, "relative_start": 1.0616581439971924, "end": **********.288151, "relative_end": **********.288151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.28888, "relative_start": 1.062387228012085, "end": **********.28888, "relative_end": **********.28888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.289439, "relative_start": 1.062946081161499, "end": **********.289439, "relative_end": **********.289439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.28985, "relative_start": 1.0633571147918701, "end": **********.28985, "relative_end": **********.28985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.290401, "relative_start": 1.0639081001281738, "end": **********.290401, "relative_end": **********.290401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.29092, "relative_start": 1.064427137374878, "end": **********.29092, "relative_end": **********.29092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.291876, "relative_start": 1.0653831958770752, "end": **********.291876, "relative_end": **********.291876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.292789, "relative_start": 1.066296100616455, "end": **********.292789, "relative_end": **********.292789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.295184, "relative_start": 1.0686910152435303, "end": **********.295184, "relative_end": **********.295184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.296789, "relative_start": 1.070296049118042, "end": **********.296789, "relative_end": **********.296789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.297505, "relative_start": 1.071012020111084, "end": **********.297505, "relative_end": **********.297505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.299192, "relative_start": 1.0726990699768066, "end": **********.299192, "relative_end": **********.299192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.300782, "relative_start": 1.074289083480835, "end": **********.300782, "relative_end": **********.300782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.302598, "relative_start": 1.0761051177978516, "end": **********.302598, "relative_end": **********.302598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.303499, "relative_start": 1.0770061016082764, "end": **********.303499, "relative_end": **********.303499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.304569, "relative_start": 1.0780761241912842, "end": **********.304569, "relative_end": **********.304569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.305315, "relative_start": 1.078822135925293, "end": **********.305315, "relative_end": **********.305315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.305814, "relative_start": 1.0793211460113525, "end": **********.305814, "relative_end": **********.305814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.306422, "relative_start": 1.0799291133880615, "end": **********.306422, "relative_end": **********.306422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.306996, "relative_start": 1.080503225326538, "end": **********.306996, "relative_end": **********.306996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.307988, "relative_start": 1.0814950466156006, "end": **********.307988, "relative_end": **********.307988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.308735, "relative_start": 1.0822420120239258, "end": **********.308735, "relative_end": **********.308735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.312905, "relative_start": 1.0864121913909912, "end": **********.312905, "relative_end": **********.312905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.314368, "relative_start": 1.0878751277923584, "end": **********.314368, "relative_end": **********.314368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.315375, "relative_start": 1.0888822078704834, "end": **********.315375, "relative_end": **********.315375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.316542, "relative_start": 1.0900490283966064, "end": **********.316542, "relative_end": **********.316542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.318254, "relative_start": 1.0917611122131348, "end": **********.318254, "relative_end": **********.318254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.320288, "relative_start": 1.0937950611114502, "end": **********.320288, "relative_end": **********.320288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.321337, "relative_start": 1.094844102859497, "end": **********.321337, "relative_end": **********.321337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.322154, "relative_start": 1.0956611633300781, "end": **********.322154, "relative_end": **********.322154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.322755, "relative_start": 1.0962622165679932, "end": **********.322755, "relative_end": **********.322755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.323213, "relative_start": 1.0967202186584473, "end": **********.323213, "relative_end": **********.323213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.323775, "relative_start": 1.0972821712493896, "end": **********.323775, "relative_end": **********.323775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.324332, "relative_start": 1.097839117050171, "end": **********.324332, "relative_end": **********.324332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.330051, "relative_start": 1.10355806350708, "end": **********.330051, "relative_end": **********.330051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.331946, "relative_start": 1.1054530143737793, "end": **********.331946, "relative_end": **********.331946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.334925, "relative_start": 1.1084320545196533, "end": **********.334925, "relative_end": **********.334925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.336359, "relative_start": 1.1098661422729492, "end": **********.336359, "relative_end": **********.336359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.337904, "relative_start": 1.1114110946655273, "end": **********.337904, "relative_end": **********.337904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.339542, "relative_start": 1.113049030303955, "end": **********.339542, "relative_end": **********.339542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.340699, "relative_start": 1.114206075668335, "end": **********.340699, "relative_end": **********.340699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.342489, "relative_start": 1.1159961223602295, "end": **********.342489, "relative_end": **********.342489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.344658, "relative_start": 1.1181650161743164, "end": **********.344658, "relative_end": **********.344658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.346087, "relative_start": 1.1195940971374512, "end": **********.346087, "relative_end": **********.346087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.346869, "relative_start": 1.1203761100769043, "end": **********.346869, "relative_end": **********.346869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.347417, "relative_start": 1.1209242343902588, "end": **********.347417, "relative_end": **********.347417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.348163, "relative_start": 1.1216700077056885, "end": **********.348163, "relative_end": **********.348163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.348807, "relative_start": 1.122314214706421, "end": **********.348807, "relative_end": **********.348807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.350146, "relative_start": 1.1236531734466553, "end": **********.350146, "relative_end": **********.350146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.351532, "relative_start": 1.1250391006469727, "end": **********.351532, "relative_end": **********.351532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.353374, "relative_start": 1.1268811225891113, "end": **********.353374, "relative_end": **********.353374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.354284, "relative_start": 1.127791166305542, "end": **********.354284, "relative_end": **********.354284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.354786, "relative_start": 1.1282930374145508, "end": **********.354786, "relative_end": **********.354786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.355386, "relative_start": 1.1288931369781494, "end": **********.355386, "relative_end": **********.355386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.356582, "relative_start": 1.1300890445709229, "end": **********.356582, "relative_end": **********.356582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.357708, "relative_start": 1.1312150955200195, "end": **********.357708, "relative_end": **********.357708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.358712, "relative_start": 1.1322190761566162, "end": **********.358712, "relative_end": **********.358712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.360129, "relative_start": 1.133636236190796, "end": **********.360129, "relative_end": **********.360129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.3615, "relative_start": 1.1350071430206299, "end": **********.3615, "relative_end": **********.3615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.362238, "relative_start": 1.1357450485229492, "end": **********.362238, "relative_end": **********.362238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.362928, "relative_start": 1.1364350318908691, "end": **********.362928, "relative_end": **********.362928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.363573, "relative_start": 1.137080192565918, "end": **********.363573, "relative_end": **********.363573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.364704, "relative_start": 1.1382110118865967, "end": **********.364704, "relative_end": **********.364704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.365898, "relative_start": 1.1394050121307373, "end": **********.365898, "relative_end": **********.365898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.367244, "relative_start": 1.1407511234283447, "end": **********.367244, "relative_end": **********.367244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.368339, "relative_start": 1.1418461799621582, "end": **********.368339, "relative_end": **********.368339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.369487, "relative_start": 1.1429941654205322, "end": **********.369487, "relative_end": **********.369487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.370198, "relative_start": 1.143705129623413, "end": **********.370198, "relative_end": **********.370198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.3712, "relative_start": 1.144707202911377, "end": **********.3712, "relative_end": **********.3712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.373829, "relative_start": 1.1473360061645508, "end": **********.373829, "relative_end": **********.373829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.374671, "relative_start": 1.1481781005859375, "end": **********.374671, "relative_end": **********.374671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.375451, "relative_start": 1.1489582061767578, "end": **********.375451, "relative_end": **********.375451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.377118, "relative_start": 1.150625228881836, "end": **********.377118, "relative_end": **********.377118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.378923, "relative_start": 1.1524300575256348, "end": **********.378923, "relative_end": **********.378923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.379666, "relative_start": 1.1531732082366943, "end": **********.379666, "relative_end": **********.379666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.380382, "relative_start": 1.1538891792297363, "end": **********.380382, "relative_end": **********.380382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.381851, "relative_start": 1.155358076095581, "end": **********.381851, "relative_end": **********.381851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.382741, "relative_start": 1.1562480926513672, "end": **********.382741, "relative_end": **********.382741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.384438, "relative_start": 1.157945156097412, "end": **********.384438, "relative_end": **********.384438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.385907, "relative_start": 1.1594140529632568, "end": **********.385907, "relative_end": **********.385907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.387725, "relative_start": 1.1612322330474854, "end": **********.387725, "relative_end": **********.387725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.389091, "relative_start": 1.1625981330871582, "end": **********.389091, "relative_end": **********.389091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.390305, "relative_start": 1.1638121604919434, "end": **********.390305, "relative_end": **********.390305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.391818, "relative_start": 1.1653251647949219, "end": **********.391818, "relative_end": **********.391818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.393016, "relative_start": 1.1665232181549072, "end": **********.393016, "relative_end": **********.393016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.39476, "relative_start": 1.168267011642456, "end": **********.39476, "relative_end": **********.39476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.396043, "relative_start": 1.1695501804351807, "end": **********.396043, "relative_end": **********.396043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.396876, "relative_start": 1.1703832149505615, "end": **********.396876, "relative_end": **********.396876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.397977, "relative_start": 1.1714842319488525, "end": **********.397977, "relative_end": **********.397977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.399363, "relative_start": 1.17287015914917, "end": **********.399363, "relative_end": **********.399363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.401193, "relative_start": 1.1747000217437744, "end": **********.401193, "relative_end": **********.401193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.403034, "relative_start": 1.1765410900115967, "end": **********.403034, "relative_end": **********.403034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.40455, "relative_start": 1.1780571937561035, "end": **********.40455, "relative_end": **********.40455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.40563, "relative_start": 1.1791372299194336, "end": **********.40563, "relative_end": **********.40563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.406359, "relative_start": 1.179866075515747, "end": **********.406359, "relative_end": **********.406359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.407463, "relative_start": 1.1809701919555664, "end": **********.407463, "relative_end": **********.407463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.408544, "relative_start": 1.182051181793213, "end": **********.408544, "relative_end": **********.408544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.410759, "relative_start": 1.1842660903930664, "end": **********.410759, "relative_end": **********.410759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.412265, "relative_start": 1.185772180557251, "end": **********.412265, "relative_end": **********.412265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.413225, "relative_start": 1.1867320537567139, "end": **********.413225, "relative_end": **********.413225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.413955, "relative_start": 1.1874620914459229, "end": **********.413955, "relative_end": **********.413955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.414523, "relative_start": 1.1880300045013428, "end": **********.414523, "relative_end": **********.414523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.415786, "relative_start": 1.1892931461334229, "end": **********.415786, "relative_end": **********.415786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.416699, "relative_start": 1.1902060508728027, "end": **********.416699, "relative_end": **********.416699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.419001, "relative_start": 1.1925082206726074, "end": **********.419001, "relative_end": **********.419001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.420438, "relative_start": 1.1939451694488525, "end": **********.420438, "relative_end": **********.420438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.42182, "relative_start": 1.1953270435333252, "end": **********.42182, "relative_end": **********.42182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.422493, "relative_start": 1.196000099182129, "end": **********.422493, "relative_end": **********.422493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.422967, "relative_start": 1.1964740753173828, "end": **********.422967, "relative_end": **********.422967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.424085, "relative_start": 1.19759202003479, "end": **********.424085, "relative_end": **********.424085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.425021, "relative_start": 1.1985280513763428, "end": **********.425021, "relative_end": **********.425021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.index", "start": **********.426967, "relative_start": 1.2004740238189697, "end": **********.426967, "relative_end": **********.426967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.variants.listbox", "start": **********.428797, "relative_start": 1.2023041248321533, "end": **********.428797, "relative_end": **********.428797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.button", "start": **********.430761, "relative_start": 1.204268217086792, "end": **********.430761, "relative_end": **********.430761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.selected", "start": **********.432217, "relative_start": 1.2057240009307861, "end": **********.432217, "relative_end": **********.432217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.434252, "relative_start": 1.207759141921997, "end": **********.434252, "relative_end": **********.434252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.436377, "relative_start": 1.2098841667175293, "end": **********.436377, "relative_end": **********.436377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.439345, "relative_start": 1.2128520011901855, "end": **********.439345, "relative_end": **********.439345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.440709, "relative_start": 1.2142162322998047, "end": **********.440709, "relative_end": **********.440709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-down", "start": **********.441796, "relative_start": 1.2153031826019287, "end": **********.441796, "relative_end": **********.441796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.options", "start": **********.44281, "relative_start": 1.2163171768188477, "end": **********.44281, "relative_end": **********.44281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.empty", "start": **********.444708, "relative_start": 1.2182152271270752, "end": **********.444708, "relative_end": **********.444708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.445688, "relative_start": 1.2191951274871826, "end": **********.445688, "relative_end": **********.445688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.447274, "relative_start": 1.2207810878753662, "end": **********.447274, "relative_end": **********.447274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.449547, "relative_start": 1.2230541706085205, "end": **********.449547, "relative_end": **********.449547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.exclamation-triangle", "start": **********.453552, "relative_start": 1.2270591259002686, "end": **********.453552, "relative_end": **********.453552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.673957, "relative_start": 1.4474642276763916, "end": **********.673957, "relative_end": **********.673957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.675083, "relative_start": 1.4485900402069092, "end": **********.675083, "relative_end": **********.675083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.675991, "relative_start": 1.449498176574707, "end": **********.675991, "relative_end": **********.675991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.679843, "relative_start": 1.4533500671386719, "end": **********.679843, "relative_end": **********.679843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.68107, "relative_start": 1.4545772075653076, "end": **********.68107, "relative_end": **********.68107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.682477, "relative_start": 1.455984115600586, "end": **********.682477, "relative_end": **********.682477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.683514, "relative_start": 1.4570212364196777, "end": **********.683514, "relative_end": **********.683514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.685603, "relative_start": 1.4591100215911865, "end": **********.685603, "relative_end": **********.685603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.68645, "relative_start": 1.4599571228027344, "end": **********.68645, "relative_end": **********.68645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.687543, "relative_start": 1.461050033569336, "end": **********.687543, "relative_end": **********.687543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.688078, "relative_start": 1.4615850448608398, "end": **********.688078, "relative_end": **********.688078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.689573, "relative_start": 1.4630801677703857, "end": **********.689573, "relative_end": **********.689573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.690534, "relative_start": 1.4640412330627441, "end": **********.690534, "relative_end": **********.690534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.691235, "relative_start": 1.4647421836853027, "end": **********.691235, "relative_end": **********.691235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.69191, "relative_start": 1.4654171466827393, "end": **********.69191, "relative_end": **********.69191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": **********.693553, "relative_start": 1.4670600891113281, "end": **********.693553, "relative_end": **********.693553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.697855, "relative_start": 1.4713621139526367, "end": **********.697855, "relative_end": **********.697855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.70112, "relative_start": 1.4746270179748535, "end": **********.70112, "relative_end": **********.70112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.702475, "relative_start": 1.4759821891784668, "end": **********.702475, "relative_end": **********.702475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.703981, "relative_start": 1.4774880409240723, "end": **********.703981, "relative_end": **********.703981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.705446, "relative_start": 1.4789531230926514, "end": **********.705446, "relative_end": **********.705446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.706475, "relative_start": 1.4799821376800537, "end": **********.706475, "relative_end": **********.706475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.711877, "relative_start": 1.485384225845337, "end": **********.718291, "relative_end": **********.718291, "duration": 0.006413936614990234, "duration_str": "6.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 30351712, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 267, "nb_templates": 267, "templates": [{"name": "1x livewire.planned-spending-form", "param_count": null, "params": [], "start": **********.120432, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/planned-spending-form.blade.phplivewire.planned-spending-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fplanned-spending-form.blade.php&line=1", "ajax": false, "filename": "planned-spending-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.planned-spending-form"}, {"name": "1x ********************************::heading", "param_count": null, "params": [], "start": **********.125939, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.php********************************::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::heading"}, {"name": "3x ********************************::label", "param_count": null, "params": [], "start": **********.129501, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.php********************************::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::label"}, {"name": "2x ********************************::input.index", "param_count": null, "params": [], "start": **********.131912, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.php********************************::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::input.index"}, {"name": "33x ********************************::with-field", "param_count": null, "params": [], "start": **********.137616, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.php********************************::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}, "render_count": 33, "name_original": "********************************::with-field"}, {"name": "3x ********************************::error", "param_count": null, "params": [], "start": **********.14067, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.php********************************::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::error"}, {"name": "3x ********************************::field", "param_count": null, "params": [], "start": **********.142234, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.php********************************::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::field"}, {"name": "1x components.categories", "param_count": null, "params": [], "start": **********.146249, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/categories.blade.phpcomponents.categories", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcategories.blade.php&line=1", "ajax": false, "filename": "categories.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.categories"}, {"name": "1x ********************************::select.search", "param_count": null, "params": [], "start": **********.150499, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/search.blade.php********************************::select.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.search"}, {"name": "1x ********************************::icon.magnifying-glass", "param_count": null, "params": [], "start": **********.15306, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/magnifying-glass.blade.php********************************::icon.magnifying-glass", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmagnifying-glass.blade.php&line=1", "ajax": false, "filename": "magnifying-glass.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.magnifying-glass"}, {"name": "3x ********************************::icon.x-mark", "param_count": null, "params": [], "start": **********.15592, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/x-mark.blade.php********************************::icon.x-mark", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fx-mark.blade.php&line=1", "ajax": false, "filename": "x-mark.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::icon.x-mark"}, {"name": "6x ********************************::button.index", "param_count": null, "params": [], "start": **********.156843, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.php********************************::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::button.index"}, {"name": "6x ********************************::button-or-link", "param_count": null, "params": [], "start": **********.158873, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.php********************************::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::button-or-link"}, {"name": "6x ********************************::with-tooltip", "param_count": null, "params": [], "start": **********.160032, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.php********************************::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::with-tooltip"}, {"name": "1x ********************************::icon.plus", "param_count": null, "params": [], "start": **********.162046, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/plus.blade.php********************************::icon.plus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fplus.blade.php&line=1", "ajax": false, "filename": "plus.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.plus"}, {"name": "1x ********************************::modal.trigger", "param_count": null, "params": [], "start": **********.167382, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/trigger.blade.php********************************::modal.trigger", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Ftrigger.blade.php&line=1", "ajax": false, "filename": "trigger.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::modal.trigger"}, {"name": "30x ********************************::select.option.index", "param_count": null, "params": [], "start": **********.16863, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/option/index.blade.php********************************::select.option.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foption%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 30, "name_original": "********************************::select.option.index"}, {"name": "30x ********************************::select.option.variants.custom", "param_count": null, "params": [], "start": **********.169743, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/option/variants/custom.blade.php********************************::select.option.variants.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foption%2Fvariants%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}, "render_count": 30, "name_original": "********************************::select.option.variants.custom"}, {"name": "30x ********************************::select.indicator.index", "param_count": null, "params": [], "start": **********.172475, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/indicator/index.blade.php********************************::select.indicator.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findicator%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 30, "name_original": "********************************::select.indicator.index"}, {"name": "30x ********************************::select.indicator.variants.check", "param_count": null, "params": [], "start": **********.173735, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/indicator/variants/check.blade.php********************************::select.indicator.variants.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findicator%2Fvariants%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}, "render_count": 30, "name_original": "********************************::select.indicator.variants.check"}, {"name": "33x ********************************::icon.index", "param_count": null, "params": [], "start": **********.174887, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.php********************************::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 33, "name_original": "********************************::icon.index"}, {"name": "30x ********************************::icon.check", "param_count": null, "params": [], "start": **********.177649, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/check.blade.php********************************::icon.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}, "render_count": 30, "name_original": "********************************::icon.check"}, {"name": "1x ********************************::select.index", "param_count": null, "params": [], "start": **********.426825, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/index.blade.php********************************::select.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.index"}, {"name": "1x ********************************::select.variants.listbox", "param_count": null, "params": [], "start": **********.428696, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/variants/listbox.blade.php********************************::select.variants.listbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fvariants%2Flistbox.blade.php&line=1", "ajax": false, "filename": "listbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.variants.listbox"}, {"name": "1x ********************************::select.button", "param_count": null, "params": [], "start": **********.430661, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/button.blade.php********************************::select.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.button"}, {"name": "1x ********************************::select.selected", "param_count": null, "params": [], "start": **********.432147, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/selected.blade.php********************************::select.selected", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fselected.blade.php&line=1", "ajax": false, "filename": "selected.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.selected"}, {"name": "1x ********************************::icon.chevron-down", "param_count": null, "params": [], "start": **********.441727, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-down.blade.php********************************::icon.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.chevron-down"}, {"name": "1x ********************************::select.options", "param_count": null, "params": [], "start": **********.442723, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/options.blade.php********************************::select.options", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foptions.blade.php&line=1", "ajax": false, "filename": "options.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.options"}, {"name": "1x ********************************::select.empty", "param_count": null, "params": [], "start": **********.444609, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/empty.blade.php********************************::select.empty", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fempty.blade.php&line=1", "ajax": false, "filename": "empty.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.empty"}, {"name": "1x ********************************::icon.exclamation-triangle", "param_count": null, "params": [], "start": **********.453286, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/exclamation-triangle.blade.php********************************::icon.exclamation-triangle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fexclamation-triangle.blade.php&line=1", "ajax": false, "filename": "exclamation-triangle.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.exclamation-triangle"}, {"name": "2x ********************************::modal.close", "param_count": null, "params": [], "start": **********.687474, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/close.blade.php********************************::modal.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.close"}, {"name": "1x ********************************::icon.loading", "param_count": null, "params": [], "start": **********.690463, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.php********************************::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.loading"}, {"name": "1x ********************************::modal.index", "param_count": null, "params": [], "start": **********.693432, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/index.blade.php********************************::modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::modal.index"}]}, "queries": {"count": 3, "nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02814, "accumulated_duration_str": "28.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.937129, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr' limit 1", "type": "query", "params": [], "bindings": ["kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.948413, "duration": 0.026359999999999998, "duration_str": "26.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 93.674}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.016799, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 93.674, "width_percent": 6.326}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"planned-spending-form #R4sOXZ24P7ozYZnoTJZR": "array:4 [\n  \"data\" => array:5 [\n    \"name\" => \"<PERSON><PERSON>\"\n    \"category_id\" => null\n    \"monthly_amount\" => 10.0\n    \"expense\" => null\n    \"categories\" => array:10 [\n      0 => array:4 [\n        \"id\" => 1\n        \"name\" => \"Auto & Transport 2\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 11\n            \"user_id\" => 1\n            \"name\" => \"Car Insurance\"\n            \"parent_id\" => 1\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 12\n            \"user_id\" => 1\n            \"name\" => \"Car Payment\"\n            \"parent_id\" => 1\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      1 => array:4 [\n        \"id\" => 2\n        \"name\" => \"Food\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 13\n            \"user_id\" => 1\n            \"name\" => \"Fast Food\"\n            \"parent_id\" => 2\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 14\n            \"user_id\" => 1\n            \"name\" => \"Restaurants\"\n            \"parent_id\" => 2\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      2 => array:4 [\n        \"id\" => 4\n        \"name\" => \"Health\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 17\n            \"user_id\" => 1\n            \"name\" => \"Doctor\"\n            \"parent_id\" => 4\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 18\n            \"user_id\" => 1\n            \"name\" => \"Pharmacy\"\n            \"parent_id\" => 4\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      3 => array:4 [\n        \"id\" => 3\n        \"name\" => \"Home\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 15\n            \"user_id\" => 1\n            \"name\" => \"Mortgage\"\n            \"parent_id\" => 3\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 16\n            \"user_id\" => 1\n            \"name\" => \"Rent\"\n            \"parent_id\" => 3\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      4 => array:4 [\n        \"id\" => 5\n        \"name\" => \"Personal Care\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 19\n            \"user_id\" => 1\n            \"name\" => \"Haircut\"\n            \"parent_id\" => 5\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 20\n            \"user_id\" => 1\n            \"name\" => \"Laundry\"\n            \"parent_id\" => 5\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      5 => array:4 [\n        \"id\" => 6\n        \"name\" => \"Personal Income\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 22\n            \"user_id\" => 1\n            \"name\" => \"Bonus\"\n            \"parent_id\" => 6\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 21\n            \"user_id\" => 1\n            \"name\" => \"Paycheck\"\n            \"parent_id\" => 6\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      6 => array:4 [\n        \"id\" => 7\n        \"name\" => \"Pets\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 23\n            \"user_id\" => 1\n            \"name\" => \"Pet Food\"\n            \"parent_id\" => 7\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 24\n            \"user_id\" => 1\n            \"name\" => \"Veterinary\"\n            \"parent_id\" => 7\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      7 => array:4 [\n        \"id\" => 8\n        \"name\" => \"Shopping\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 25\n            \"user_id\" => 1\n            \"name\" => \"Clothing\"\n            \"parent_id\" => 8\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 26\n            \"user_id\" => 1\n            \"name\" => \"Gifts\"\n            \"parent_id\" => 8\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      8 => array:4 [\n        \"id\" => 9\n        \"name\" => \"Travel\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 28\n            \"user_id\" => 1\n            \"name\" => \"Airfare\"\n            \"parent_id\" => 9\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 27\n            \"user_id\" => 1\n            \"name\" => \"Hotel\"\n            \"parent_id\" => 9\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      9 => array:4 [\n        \"id\" => 10\n        \"name\" => \"Utilities\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 30\n            \"user_id\" => 1\n            \"name\" => \"Electric\"\n            \"parent_id\" => 10\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 29\n            \"user_id\" => 1\n            \"name\" => \"Gas\"\n            \"parent_id\" => 10\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n    ]\n  ]\n  \"name\" => \"planned-spending-form\"\n  \"component\" => \"App\\Livewire\\PlannedSpendingForm\"\n  \"id\" => \"R4sOXZ24P7ozYZnoTJZR\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\PlannedSpendingForm@submit<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpendingForm.php&line=63\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpendingForm.php&line=63\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/PlannedSpendingForm.php:63-86</a>", "middleware": "web", "duration": "1.51s", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-605786560 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-605786560\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1837660262 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"4336 characters\">{&quot;data&quot;:{&quot;name&quot;:&quot;&quot;,&quot;category_id&quot;:null,&quot;monthly_amount&quot;:null,&quot;expense&quot;:null,&quot;categories&quot;:[[[{&quot;id&quot;:1,&quot;name&quot;:&quot;Auto &amp; Transport 2&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:11,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Car Insurance&quot;,&quot;parent_id&quot;:1,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:12,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Car Payment&quot;,&quot;parent_id&quot;:1,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:2,&quot;name&quot;:&quot;Food&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:13,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Fast Food&quot;,&quot;parent_id&quot;:2,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:14,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Restaurants&quot;,&quot;parent_id&quot;:2,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:4,&quot;name&quot;:&quot;Health&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:17,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Doctor&quot;,&quot;parent_id&quot;:4,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:18,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Pharmacy&quot;,&quot;parent_id&quot;:4,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:3,&quot;name&quot;:&quot;Home&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:15,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Mortgage&quot;,&quot;parent_id&quot;:3,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:16,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Rent&quot;,&quot;parent_id&quot;:3,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:5,&quot;name&quot;:&quot;Personal Care&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:19,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Haircut&quot;,&quot;parent_id&quot;:5,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:20,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Laundry&quot;,&quot;parent_id&quot;:5,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:6,&quot;name&quot;:&quot;Personal Income&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:22,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Bonus&quot;,&quot;parent_id&quot;:6,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:21,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Paycheck&quot;,&quot;parent_id&quot;:6,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:7,&quot;name&quot;:&quot;Pets&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:23,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Pet Food&quot;,&quot;parent_id&quot;:7,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:24,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Veterinary&quot;,&quot;parent_id&quot;:7,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:8,&quot;name&quot;:&quot;Shopping&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:25,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Clothing&quot;,&quot;parent_id&quot;:8,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:26,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Gifts&quot;,&quot;parent_id&quot;:8,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:9,&quot;name&quot;:&quot;Travel&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:28,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Airfare&quot;,&quot;parent_id&quot;:9,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:27,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Hotel&quot;,&quot;parent_id&quot;:9,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:10,&quot;name&quot;:&quot;Utilities&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:30,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Electric&quot;,&quot;parent_id&quot;:10,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:29,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Gas&quot;,&quot;parent_id&quot;:10,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;R4sOXZ24P7ozYZnoTJZR&quot;,&quot;name&quot;:&quot;planned-spending-form&quot;,&quot;path&quot;:&quot;planned-spending&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-2445847864-0&quot;:[&quot;div&quot;,&quot;OwiHvzxmurMUZEtXl2G2&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;bef1d1e9079d49a42b221544584ea95a8fc938d2d93dee2d5458394e21f06748&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Tanvir Hossen Bappy</span>\"\n        \"<span class=sf-dump-key>monthly_amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"6 characters\">submit</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1837660262\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-211699951 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">5213</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/planned-spending</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"751 characters\">PHPSESSID=aa0mh29tpvppc77i3l7peen7g0; XSRF-TOKEN=eyJpdiI6InpucUk2VCttWjBHUUJqQ21qVDdiaGc9PSIsInZhbHVlIjoib0VMSkVGVFBHT21oQ0Mzck9WZFhZS0tvVmVaWGdZNFVGN2FGbkgvTFd0em53cHE1K2ZQblBRNnBIQUh4UThrQXJDcVVaQWtTeEs5amdLNkVFK1lTOXBSRE50QUdqb3ZqVlpQeXVWK2JRZ013b0pPVmcyTnczWE82SUtBZEFBSUYiLCJtYWMiOiJmMDAzNGU0MGVhM2M4MGMzMzM0NjFiNmY1OTQ0ZjdmMDY2MzgzN2RlZjJlZmRlMjdlZGE4ZTMxN2JhOTc1MDkxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InNkMW9FZGJVSDJIT0lyTloxcVpxMnc9PSIsInZhbHVlIjoiandoajJRMTU2ZDhIRnB2dWtSZURHR1RZeHVBQ0ZxQ1h2QU1rcjQ3TXMxVHFGV3BqSUNxYzRsa1IvVnJLQVlVNDVLTERJN2xiNTRjMEludGRvS2ZPdVFYN1NyREc2dEVmeWk2V1Jtd3hVY1k1M1hZNGdHOVZ0eGgyZ2tIb3FPNzgiLCJtYWMiOiI3MTdlOGEwNzBiMjVlYmNjZDQ5MzYyNGI0OGE1N2UzZWQ0N2JkMmJjMmM4MDBmZDkxYzlmMzkyN2NiZjliZTUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211699951\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-438628466 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-438628466\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1611310369 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 25 May 2025 09:19:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611310369\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-452112796 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/planned-spending</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452112796\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}