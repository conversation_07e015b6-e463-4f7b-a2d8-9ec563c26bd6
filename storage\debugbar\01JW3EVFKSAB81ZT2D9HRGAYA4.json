{"__meta": {"id": "01JW3EVFKSAB81ZT2D9HRGAYA4", "datetime": "2025-05-25 10:12:13", "utime": **********.562214, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748167932.942156, "end": **********.56223, "duration": 0.6200740337371826, "duration_str": "620ms", "measures": [{"label": "Booting", "start": 1748167932.942156, "relative_start": 0, "end": **********.346651, "relative_end": **********.346651, "duration": 0.****************, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.346665, "relative_start": 0.****************, "end": **********.562232, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.382955, "relative_start": 0.****************, "end": **********.387154, "relative_end": **********.387154, "duration": 0.004199028015136719, "duration_str": "4.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.554412, "relative_start": 0.****************, "end": **********.55536, "relative_end": **********.55536, "duration": 0.0009481906890869141, "duration_str": "948μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 10, "nb_statements": 9, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.010320000000000001, "accumulated_duration_str": "10.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.406463, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr' limit 1", "type": "query", "params": [], "bindings": ["kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.415706, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 32.655}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.4459138, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 32.655, "width_percent": 5.814}, {"sql": "select * from `transactions` where `slug` = 'cole-llc' limit 1", "type": "query", "params": [], "bindings": ["cole-llc"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 119}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 72}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 31}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php", "line": 211}], "start": **********.455072, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:119", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FDrawer%2FImplicitRouteBinding.php&line=119", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "119"}, "connection": "daily", "explain": null, "start_percent": 38.469, "width_percent": 5.717}, {"sql": "select * from `accounts` where `accounts`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Policies/TransactionPolicy.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Policies\\TransactionPolicy.php", "line": 33}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 818}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 771}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 552}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 447}], "start": **********.460793, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "TransactionPolicy.php:33", "source": {"index": 21, "namespace": null, "name": "app/Policies/TransactionPolicy.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Policies\\TransactionPolicy.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FPolicies%2FTransactionPolicy.php&line=33", "ajax": false, "filename": "TransactionPolicy.php", "line": "33"}, "connection": "daily", "explain": null, "start_percent": 44.186, "width_percent": 6.686}, {"sql": "select * from `accounts` where `accounts`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.47252, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "daily", "explain": null, "start_percent": 50.872, "width_percent": 6.105}, {"sql": "select * from `transactions` where `transactions`.`id` = 39 limit 1", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.4757912, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "daily", "explain": null, "start_percent": 56.977, "width_percent": 6.105}, {"sql": "select * from `accounts` where `accounts`.`id` in (4, 3, 1, 2, 5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/EloquentCollectionSynth.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\EloquentCollectionSynth.php", "line": 70}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.478991, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EloquentCollectionSynth.php:70", "source": {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/EloquentCollectionSynth.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\EloquentCollectionSynth.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FEloquentCollectionSynth.php&line=70", "ajax": false, "filename": "EloquentCollectionSynth.php", "line": "70"}, "connection": "daily", "explain": null, "start_percent": 63.081, "width_percent": 5.523}, {"sql": "select * from `transactions` where `id` = 39 limit 1", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/ImplicitlyBoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\ImplicitlyBoundMethod.php", "line": 109}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/ImplicitlyBoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\ImplicitlyBoundMethod.php", "line": 71}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/ImplicitlyBoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\ImplicitlyBoundMethod.php", "line": 20}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.524593, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ImplicitlyBoundMethod.php:109", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/ImplicitlyBoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\ImplicitlyBoundMethod.php", "line": 109}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FImplicitlyBoundMethod.php&line=109", "ajax": false, "filename": "ImplicitlyBoundMethod.php", "line": "109"}, "connection": "daily", "explain": null, "start_percent": 68.605, "width_percent": 9.399}, {"sql": "delete from `transactions` where `id` = 39", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 251}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.546053, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "TransactionForm.php:251", "source": {"index": 14, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 251}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=251", "ajax": false, "filename": "TransactionForm.php", "line": "251"}, "connection": "daily", "explain": null, "start_percent": 78.004, "width_percent": 21.996}]}, "models": {"data": {"App\\Models\\Account": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FAccount.php&line=1", "ajax": false, "filename": "Account.php", "line": "?"}}, "App\\Models\\Transaction": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FTransaction.php&line=1", "ajax": false, "filename": "Transaction.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 11, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => update,\n  target => App\\Models\\Transaction(id=39),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Transaction)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Transaction(id=39)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">App\\Models\\Transaction(id=39)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"37 characters\">[0 =&gt; Object(App\\Models\\Transaction)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.467231, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\TransactionForm@delete<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=249\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=249\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/TransactionForm.php:249-261</a>", "middleware": "web", "duration": "622ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1850349229 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1850349229\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-956861565 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"5839 characters\">{&quot;data&quot;:{&quot;account&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Account&quot;,&quot;key&quot;:4,&quot;s&quot;:&quot;mdl&quot;}],&quot;transaction&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Transaction&quot;,&quot;key&quot;:39,&quot;s&quot;:&quot;mdl&quot;}],&quot;accounts&quot;:[null,{&quot;keys&quot;:[4,3,1,2,5],&quot;class&quot;:&quot;Illuminate\\\\Database\\\\Eloquent\\\\Collection&quot;,&quot;modelClass&quot;:&quot;App\\\\Models\\\\Account&quot;,&quot;s&quot;:&quot;elcln&quot;}],&quot;categories&quot;:[[[{&quot;id&quot;:1,&quot;name&quot;:&quot;Auto &amp; Transport 2&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:11,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Car Insurance&quot;,&quot;parent_id&quot;:1,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:12,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Car Payment&quot;,&quot;parent_id&quot;:1,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:2,&quot;name&quot;:&quot;Food&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:13,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Fast Food&quot;,&quot;parent_id&quot;:2,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:14,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Restaurants&quot;,&quot;parent_id&quot;:2,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:4,&quot;name&quot;:&quot;Health&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:17,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Doctor&quot;,&quot;parent_id&quot;:4,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:18,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Pharmacy&quot;,&quot;parent_id&quot;:4,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:3,&quot;name&quot;:&quot;Home&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:15,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Mortgage&quot;,&quot;parent_id&quot;:3,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:16,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Rent&quot;,&quot;parent_id&quot;:3,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:5,&quot;name&quot;:&quot;Personal Care&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:19,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Haircut&quot;,&quot;parent_id&quot;:5,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:20,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Laundry&quot;,&quot;parent_id&quot;:5,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:6,&quot;name&quot;:&quot;Personal Income&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:22,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Bonus&quot;,&quot;parent_id&quot;:6,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:21,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Paycheck&quot;,&quot;parent_id&quot;:6,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:7,&quot;name&quot;:&quot;Pets&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:23,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Pet Food&quot;,&quot;parent_id&quot;:7,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:24,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Veterinary&quot;,&quot;parent_id&quot;:7,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:8,&quot;name&quot;:&quot;Shopping&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:25,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Clothing&quot;,&quot;parent_id&quot;:8,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:26,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Gifts&quot;,&quot;parent_id&quot;:8,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:9,&quot;name&quot;:&quot;Travel&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:28,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Airfare&quot;,&quot;parent_id&quot;:9,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:27,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Hotel&quot;,&quot;parent_id&quot;:9,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:10,&quot;name&quot;:&quot;Utilities&quot;,&quot;parent_id&quot;:null,&quot;children&quot;:[[[{&quot;id&quot;:30,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Electric&quot;,&quot;parent_id&quot;:10,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:29,&quot;user_id&quot;:1,&quot;name&quot;:&quot;Gas&quot;,&quot;parent_id&quot;:10,&quot;created_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-05-25T09:03:42.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;account_id&quot;:4,&quot;transfer_to&quot;:null,&quot;payee&quot;:&quot;Cole LLC&quot;,&quot;transaction_types&quot;:[[[&quot;credit&quot;,{&quot;class&quot;:&quot;App\\\\Enums\\\\TransactionType&quot;,&quot;s&quot;:&quot;enm&quot;}],[&quot;debit&quot;,{&quot;class&quot;:&quot;App\\\\Enums\\\\TransactionType&quot;,&quot;s&quot;:&quot;enm&quot;}],[&quot;deposit&quot;,{&quot;class&quot;:&quot;App\\\\Enums\\\\TransactionType&quot;,&quot;s&quot;:&quot;enm&quot;}],[&quot;transfer&quot;,{&quot;class&quot;:&quot;App\\\\Enums\\\\TransactionType&quot;,&quot;s&quot;:&quot;enm&quot;}],[&quot;withdrawal&quot;,{&quot;class&quot;:&quot;App\\\\Enums\\\\TransactionType&quot;,&quot;s&quot;:&quot;enm&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;type&quot;:[&quot;credit&quot;,{&quot;class&quot;:&quot;App\\\\Enums\\\\TransactionType&quot;,&quot;s&quot;:&quot;enm&quot;}],&quot;amount&quot;:32.6,&quot;category_id&quot;:13,&quot;date&quot;:[&quot;2025-05-24T00:00:00+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;user_tags&quot;:[[&quot;Bills&quot;,&quot;Entertainment&quot;,&quot;Groceries&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;tags&quot;:[[&quot;Groceries&quot;,&quot;Entertainment&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;notes&quot;:&quot;Ab adipisci ut aperiam reiciendis iure. Consequatur quibusdam est quia. Voluptas qui sunt molestiae nobis autem. Enim et voluptate vitae beatae quo. Odit laboriosam saepe cumque cum harum consequatur. Facere voluptatibus incidunt numquam veniam similique cumque ipsum.&quot;,&quot;attachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;status&quot;:false,&quot;is_recurring&quot;:false,&quot;frequency&quot;:[&quot;month&quot;,{&quot;class&quot;:&quot;App\\\\Enums\\\\RecurringFrequency&quot;,&quot;s&quot;:&quot;enm&quot;}],&quot;recurring_end&quot;:[&quot;2025-06-24T00:00:00+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;NmKqptpiBC46pbH3royI&quot;,&quot;name&quot;:&quot;transaction-form&quot;,&quot;path&quot;:&quot;transaction-form\\/cole-llc&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-2445847864-0&quot;:[&quot;div&quot;,&quot;AUbUgA7luKrSuWqVTGmo&quot;],&quot;lw-695692109-0&quot;:[&quot;div&quot;,&quot;xQjTztE5DoK3SoHt9mdK&quot;],&quot;lw-2046982099-0&quot;:[&quot;div&quot;,&quot;1xhGJkMGUpDhZJ6xfpTT&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;320c79e92936d1ad4b466ce34e33ebc276d0e1ada9b452569f522779c85433ef&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>tags.0</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Entertainment</span>\"\n        \"<span class=sf-dump-key>tags.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>39</span>\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956861565\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-310572837 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6951</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/transaction-form/cole-llc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"751 characters\">PHPSESSID=aa0mh29tpvppc77i3l7peen7g0; XSRF-TOKEN=eyJpdiI6IkNKL0RHZHQwVU9PbDJ0ckZqU3hFekE9PSIsInZhbHVlIjoiWU1iL0o5cEVOeEt2dVBFKzBPUlhPdEZFbU1TcjVBR3lkSThrNURPajJWZWREdVJaOXBtbTdWY0dFYjNPSUhMRVlZQ3RtcTRrbG14MElpMCs4VmNic0ZsN1lkbmlISmJkN2o4NXF4cUxzTU1qcG1oOEErZHZuSFRCWmIrSE9hRCsiLCJtYWMiOiI3MTJjY2M3MDE2M2VlMDU2ZTRkOWIzZGYzNWYzZjkzNmUwMDcyZDQxOTliZDZlNDdkMjI4MjRiNTQ4N2IxYmM3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imk5L2g5NjNMMDh1WnRXNHd5dXNGNGc9PSIsInZhbHVlIjoid1BHL0w1a1BLazhLL2VBc3hrdE1ZemdHZklJSVJxYXpSWDNmYjh5dk9yS1cvUGpPUnJQcjlFcTYyNTVrQ1M3NHhZWXNSSVUvblA4VWlnTmErWW4wblBlUHcvcld4K3VXWEcyV0VwaDRIMWhIMlF3ajZvUDJJUjJQZXRWUWJzQXkiLCJtYWMiOiJmNTdhMmJmYTAyNjIzZGI1MmE0NDZjNGNlYzBhYjk1OTUzYWE0NjVjNTVhZDMyOTA3ZTI0MDUwOWI0MjYyYzE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-310572837\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1533003123 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533003123\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-539943687 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 25 May 2025 10:12:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-539943687\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2048722164 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/transaction-form/cole-llc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2048722164\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}