<div>
    <flux:modal name="{{ $this->getId() }}">
        <div x-cloak wire:loading.remove class="space-y-6">
            <flux:heading size="lg" class="font-semibold -mt-1.5!">
                Manage User Roles
            </flux:heading>

            @if($user)
                <div class="space-y-4">
                    <div>
                        <flux:label>User</flux:label>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            {{ $user['name'] }} ({{ $user['email'] }})
                        </div>
                    </div>

                    <flux:field>
                        <flux:label>Roles</flux:label>

                        <div class="space-y-2 max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg p-3">
                            @foreach($availableRoles as $role)
                                <label class="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        wire:model="roles"
                                        value="{{ $role->id }}"
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                    >
                                    <span class="text-sm">{{ $role->name }}</span>
                                </label>
                            @endforeach
                        </div>

                        <flux:error name="roles" />
                    </flux:field>
                </div>
            @endif

            <div class="flex gap-2">
                <flux:spacer />

                <flux:modal.close>
                        <flux:button variant="ghost" size="sm">
                            Cancel
                        </flux:button>
                    </flux:modal.close>

                <flux:button type="button" wire:click='submit' variant="primary" size="sm">
                    Save
                </flux:button>
            </div>
        </div>

        <div x-cloak wire:loading.flex class="flex items-center justify-center w-full h-[400px]">
            <flux:icon.loading />
        </div>
    </flux:modal>
</div>
