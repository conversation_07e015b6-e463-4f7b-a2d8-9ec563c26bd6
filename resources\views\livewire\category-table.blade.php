<div class="space-y-4 mx-auto max-w-7xl">
    <div class="flex items-center justify-between">
        <flux:heading size="xl">
            Categories
        </flux:heading>

        <div>
            <flux:modal.trigger name="category-form">
                <flux:button icon="plus" variant="primary" size="sm">
                    Add
                </flux:button>
            </flux:modal.trigger>
        </div>
    </div>

    <x-card>
        <x-slot:content>
            <div class="p-3">
                <flux:input icon="magnifying-glass" placeholder="Search categories..." wire:model.live.debounce.300ms='search' clearable />
            </div>

            @if ($categories->count() > 0)
                <x-table :paginate="$categories" class="border-t border-zinc-200 dark:border-white/20">
                    <x-table.columns class="[&>tr>th]:px-3! bg-zinc-50 dark:bg-zinc-800">
                        <x-table.column>
                            Name
                        </x-table.column>

                        <x-table.column>
                            Parent
                        </x-table.column>

                        <x-table.column class="[&>div]:justify-end!">
                            Actions
                        </x-table.column>
                    </x-table.columns>

                    <x-table.rows class="dark:bg-zinc-900">
                        @foreach ($categories as $category)
                            <x-table.row :key="$category->id" class="[&>td]:px-3! [&>td]:py-2!">
                                <x-table.cell class="whitespace-nowrap" variant="strong">
                                    {{ $category->name }}
                                </x-table.cell>

                                <x-table.cell class="whitespace-nowrap" variant="strong">
                                    {{ $category->parent?->name }}
                                </x-table.cell>

                                <x-table.cell class="[&>div]:justify-end!">
                                    <div class="flex items-center ">
                                        <div>
                                            <flux:modal.trigger name="category-form">
                                                <flux:button icon="pencil-square" variant="ghost" size="sm"
                                                    class="text-indigo-500!" x-on:click="$dispatch('load-category', { category: {{ $category }} })" />
                                            </flux:modal.trigger>
                                        </div>

                                        <div>
                                            <flux:modal.trigger name="delete-category-{{ $category->id }}">
                                                <flux:button icon="trash" variant="ghost" size="sm"
                                                    class="text-red-500!" />
                                            </flux:modal.trigger>

                                            <flux:modal name="delete-category-{{ $category->id }}" class="min-w-[22rem]">
                                                <form wire:submit="delete({{ $category->id }})" class="space-y-6">
                                                    <div class="space-y-4!">
                                                        <flux:heading size="lg" class="font-semibold -mt-1.5!">
                                                            Delete Category?
                                                        </flux:heading>

                                                        <flux:subheading>
                                                            Are you sure you want to delete the

                                                            <span class="font-semibold text-red-500">
                                                                '{{ $category->name }}'
                                                            </span>

                                                            category?
                                                        </flux:subheading>
                                                    </div>

                                                    <div class="flex gap-2">
                                                        <flux:spacer />

                                                        <flux:modal.close>
                                                            <flux:button variant="ghost" size="sm">
                                                                Cancel
                                                            </flux:button>
                                                        </flux:modal.close>

                                                        <flux:button type="submit" variant="danger" size="sm">
                                                            Confirm
                                                        </flux:button>
                                                    </div>
                                                </form>
                                            </flux:modal>
                                        </div>
                                    </div>
                                </x-table.cell>
                            </x-table.row>
                        @endforeach
                    </x-table.rows>
                </x-table>
            @else
                <flux:heading class="italic! font-medium text-center pb-3">
                    No categories found...
                </flux:heading>
            @endif
        </x-slot:content>
    </x-card>

    <livewire:category-form />
</div>
