@props(['categories', 'disabled' => null])

<flux:field>
    <flux:label @class([
        'opacity-50' => $disabled
    ])>
        Category
    </flux:label>

    <div class="relative">
        <x-select placeholder="Select a category" clearable wire:model='category_id'>
            @foreach ($categories as $category)
                <x-option value="{{ $category['id'] }}" class="font-semibold">
                    {{ $category['name'] }}
                </x-option>

                @foreach($category['children'] as $child)
                    <x-option value="{{ $child['id'] }}">
                        &nbsp;&nbsp;&nbsp;&nbsp;{{ $child['name'] }}
                    </x-option>
                @endforeach
            @endforeach
        </x-select>

        <!-- Add category button -->
        <div class="absolute top-2 right-10">
            <flux:modal.trigger name="category-form">
                <flux:button square variant="subtle" size="sm" aria-label="Category form">
                    <flux:icon.plus variant="micro" />
                </flux:button>
            </flux:modal.trigger>
        </div>
    </div>

    <flux:error name="category_id" />

    <div class="absolute">
        <livewire:category-form />
    </div>
</flux:field>