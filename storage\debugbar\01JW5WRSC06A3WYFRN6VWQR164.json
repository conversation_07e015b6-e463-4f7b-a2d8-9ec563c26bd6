{"__meta": {"id": "01JW5WRSC06A3WYFRN6VWQR164", "datetime": "2025-05-26 08:53:54", "utime": **********.178033, "method": "GET", "uri": "/transaction-form", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.012256, "end": **********.178052, "duration": 13.165796041488647, "duration_str": "13.17s", "measures": [{"label": "Booting", "start": **********.012256, "relative_start": 0, "end": **********.450732, "relative_end": **********.450732, "duration": 0.****************, "duration_str": "438ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.450745, "relative_start": 0.****************, "end": **********.178054, "relative_end": 2.1457672119140625e-06, "duration": 12.***************, "duration_str": "12.73s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.500353, "relative_start": 0.****************, "end": **********.504537, "relative_end": **********.504537, "duration": 0.*****************, "duration_str": "4.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.transaction-form", "start": **********.679833, "relative_start": 0.****************, "end": **********.679833, "relative_end": **********.679833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.135887, "relative_start": 6.***************, "end": **********.135887, "relative_end": **********.135887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.140148, "relative_start": 6.127892017364502, "end": **********.140148, "relative_end": **********.140148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.1417, "relative_start": 6.129444122314453, "end": **********.1417, "relative_end": **********.1417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.142449, "relative_start": 6.130192995071411, "end": **********.142449, "relative_end": **********.142449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.142916, "relative_start": 6.130660057067871, "end": **********.142916, "relative_end": **********.142916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.143348, "relative_start": 6.131092071533203, "end": **********.143348, "relative_end": **********.143348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.143788, "relative_start": 6.131532192230225, "end": **********.143788, "relative_end": **********.143788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.144295, "relative_start": 6.1320390701293945, "end": **********.144295, "relative_end": **********.144295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.145473, "relative_start": 6.133217096328735, "end": **********.145473, "relative_end": **********.145473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.146261, "relative_start": 6.134005069732666, "end": **********.146261, "relative_end": **********.146261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.147266, "relative_start": 6.135010004043579, "end": **********.147266, "relative_end": **********.147266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.148279, "relative_start": 6.136023044586182, "end": **********.148279, "relative_end": **********.148279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.387553, "relative_start": 6.3752970695495605, "end": **********.387553, "relative_end": **********.387553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.660872, "relative_start": 6.648616075515747, "end": **********.660872, "relative_end": **********.660872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.808733, "relative_start": 6.7964770793914795, "end": **********.808733, "relative_end": **********.808733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.939147, "relative_start": 6.926891088485718, "end": **********.939147, "relative_end": **********.939147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.940793, "relative_start": 6.928537130355835, "end": **********.940793, "relative_end": **********.940793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.94191, "relative_start": 6.929654121398926, "end": **********.94191, "relative_end": **********.94191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.943487, "relative_start": 6.9312310218811035, "end": **********.943487, "relative_end": **********.943487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.944459, "relative_start": 6.932203054428101, "end": **********.944459, "relative_end": **********.944459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.945417, "relative_start": 6.933161020278931, "end": **********.945417, "relative_end": **********.945417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.94628, "relative_start": 6.934024095535278, "end": **********.94628, "relative_end": **********.94628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.946788, "relative_start": 6.934532165527344, "end": **********.946788, "relative_end": **********.946788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.947351, "relative_start": 6.9350950717926025, "end": **********.947351, "relative_end": **********.947351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.948055, "relative_start": 6.9357991218566895, "end": **********.948055, "relative_end": **********.948055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.949316, "relative_start": 6.937060117721558, "end": **********.949316, "relative_end": **********.949316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.950234, "relative_start": 6.937978029251099, "end": **********.950234, "relative_end": **********.950234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.951077, "relative_start": 6.938821077346802, "end": **********.951077, "relative_end": **********.951077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.951642, "relative_start": 6.9393861293792725, "end": **********.951642, "relative_end": **********.951642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.952037, "relative_start": 6.939781188964844, "end": **********.952037, "relative_end": **********.952037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.95254, "relative_start": 6.940284013748169, "end": **********.95254, "relative_end": **********.95254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.953378, "relative_start": 6.941122055053711, "end": **********.953378, "relative_end": **********.953378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.955331, "relative_start": 6.943075180053711, "end": **********.955331, "relative_end": **********.955331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.956276, "relative_start": 6.94402003288269, "end": **********.956276, "relative_end": **********.956276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.957002, "relative_start": 6.944746017456055, "end": **********.957002, "relative_end": **********.957002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.95756, "relative_start": 6.9453041553497314, "end": **********.95756, "relative_end": **********.95756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.95795, "relative_start": 6.945694208145142, "end": **********.95795, "relative_end": **********.95795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.958442, "relative_start": 6.946186065673828, "end": **********.958442, "relative_end": **********.958442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.958955, "relative_start": 6.946699142456055, "end": **********.958955, "relative_end": **********.958955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.959955, "relative_start": 6.947699069976807, "end": **********.959955, "relative_end": **********.959955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.960675, "relative_start": 6.948419094085693, "end": **********.960675, "relative_end": **********.960675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.961394, "relative_start": 6.949138164520264, "end": **********.961394, "relative_end": **********.961394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.961953, "relative_start": 6.949697017669678, "end": **********.961953, "relative_end": **********.961953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.96238, "relative_start": 6.950124025344849, "end": **********.96238, "relative_end": **********.96238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.962907, "relative_start": 6.950651168823242, "end": **********.962907, "relative_end": **********.962907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.963443, "relative_start": 6.9511871337890625, "end": **********.963443, "relative_end": **********.963443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.index", "start": **********.964454, "relative_start": 6.952198028564453, "end": **********.964454, "relative_end": **********.964454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.variants.listbox", "start": 1748249628.193588, "relative_start": 7.181332111358643, "end": 1748249628.193588, "relative_end": 1748249628.193588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.button", "start": 1748249628.460133, "relative_start": 7.4478771686553955, "end": 1748249628.460133, "relative_end": 1748249628.460133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.selected", "start": 1748249628.979696, "relative_start": 7.967440128326416, "end": 1748249628.979696, "relative_end": 1748249628.979696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": 1748249629.049349, "relative_start": 8.037093162536621, "end": 1748249629.049349, "relative_end": 1748249629.049349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249629.050588, "relative_start": 8.038331985473633, "end": 1748249629.050588, "relative_end": 1748249629.050588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249629.056001, "relative_start": 8.043745040893555, "end": 1748249629.056001, "relative_end": 1748249629.056001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249629.057077, "relative_start": 8.04482102394104, "end": 1748249629.057077, "relative_end": 1748249629.057077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-down", "start": 1748249629.05842, "relative_start": 8.04616403579712, "end": 1748249629.05842, "relative_end": 1748249629.05842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.options", "start": 1748249629.05936, "relative_start": 8.047104120254517, "end": 1748249629.05936, "relative_end": 1748249629.05936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": 1748249629.300827, "relative_start": 8.288571119308472, "end": 1748249629.300827, "relative_end": 1748249629.300827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249629.302281, "relative_start": 8.290024995803833, "end": 1748249629.302281, "relative_end": 1748249629.302281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249629.307169, "relative_start": 8.294913053512573, "end": 1748249629.307169, "relative_end": 1748249629.307169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249629.308155, "relative_start": 8.295899152755737, "end": 1748249629.308155, "relative_end": 1748249629.308155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.309706, "relative_start": 8.297450065612793, "end": 1748249629.309706, "relative_end": 1748249629.309706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.310225, "relative_start": 8.297969102859497, "end": 1748249629.310225, "relative_end": 1748249629.310225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.31067, "relative_start": 8.2984139919281, "end": 1748249629.31067, "relative_end": 1748249629.31067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.311112, "relative_start": 8.298856019973755, "end": 1748249629.311112, "relative_end": 1748249629.311112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.311544, "relative_start": 8.299288034439087, "end": 1748249629.311544, "relative_end": 1748249629.311544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": 1748249629.311958, "relative_start": 8.299702167510986, "end": 1748249629.311958, "relative_end": 1748249629.311958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249629.312562, "relative_start": 8.30030608177185, "end": 1748249629.312562, "relative_end": 1748249629.312562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249629.313147, "relative_start": 8.300891160964966, "end": 1748249629.313147, "relative_end": 1748249629.313147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.categories", "start": 1748249629.313689, "relative_start": 8.301433086395264, "end": 1748249629.313689, "relative_end": 1748249629.313689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249629.314932, "relative_start": 8.3026762008667, "end": 1748249629.314932, "relative_end": 1748249629.314932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.315415, "relative_start": 8.30315899848938, "end": 1748249629.315415, "relative_end": 1748249629.315415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.315821, "relative_start": 8.30356502532959, "end": 1748249629.315821, "relative_end": 1748249629.315821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.31619, "relative_start": 8.303934097290039, "end": 1748249629.31619, "relative_end": 1748249629.31619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.316618, "relative_start": 8.304362058639526, "end": 1748249629.316618, "relative_end": 1748249629.316618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.317176, "relative_start": 8.304920196533203, "end": 1748249629.317176, "relative_end": 1748249629.317176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.31761, "relative_start": 8.305354118347168, "end": 1748249629.31761, "relative_end": 1748249629.31761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.318029, "relative_start": 8.30577301979065, "end": 1748249629.318029, "relative_end": 1748249629.318029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.318394, "relative_start": 8.306138038635254, "end": 1748249629.318394, "relative_end": 1748249629.318394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.318756, "relative_start": 8.30650019645691, "end": 1748249629.318756, "relative_end": 1748249629.318756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.319115, "relative_start": 8.306859016418457, "end": 1748249629.319115, "relative_end": 1748249629.319115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.320085, "relative_start": 8.307829141616821, "end": 1748249629.320085, "relative_end": 1748249629.320085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.32093, "relative_start": 8.308674097061157, "end": 1748249629.32093, "relative_end": 1748249629.32093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.321703, "relative_start": 8.309447050094604, "end": 1748249629.321703, "relative_end": 1748249629.321703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.322163, "relative_start": 8.30990719795227, "end": 1748249629.322163, "relative_end": 1748249629.322163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.322597, "relative_start": 8.310341119766235, "end": 1748249629.322597, "relative_end": 1748249629.322597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.322997, "relative_start": 8.310741186141968, "end": 1748249629.322997, "relative_end": 1748249629.322997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.323372, "relative_start": 8.311115980148315, "end": 1748249629.323372, "relative_end": 1748249629.323372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.32376, "relative_start": 8.311504125595093, "end": 1748249629.32376, "relative_end": 1748249629.32376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.324147, "relative_start": 8.311891078948975, "end": 1748249629.324147, "relative_end": 1748249629.324147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.324522, "relative_start": 8.312266111373901, "end": 1748249629.324522, "relative_end": 1748249629.324522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.324897, "relative_start": 8.312641143798828, "end": 1748249629.324897, "relative_end": 1748249629.324897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.32527, "relative_start": 8.313014030456543, "end": 1748249629.32527, "relative_end": 1748249629.32527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.325671, "relative_start": 8.313415050506592, "end": 1748249629.325671, "relative_end": 1748249629.325671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.326064, "relative_start": 8.31380820274353, "end": 1748249629.326064, "relative_end": 1748249629.326064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.326449, "relative_start": 8.3141930103302, "end": 1748249629.326449, "relative_end": 1748249629.326449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.326833, "relative_start": 8.314577102661133, "end": 1748249629.326833, "relative_end": 1748249629.326833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.327227, "relative_start": 8.314971208572388, "end": 1748249629.327227, "relative_end": 1748249629.327227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.327613, "relative_start": 8.315357208251953, "end": 1748249629.327613, "relative_end": 1748249629.327613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.327996, "relative_start": 8.31574010848999, "end": 1748249629.327996, "relative_end": 1748249629.327996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.328378, "relative_start": 8.316122055053711, "end": 1748249629.328378, "relative_end": 1748249629.328378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": 1748249629.328757, "relative_start": 8.316501140594482, "end": 1748249629.328757, "relative_end": 1748249629.328757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.plus", "start": 1748249629.329823, "relative_start": 8.317567110061646, "end": 1748249629.329823, "relative_end": 1748249629.329823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249629.330451, "relative_start": 8.318195104598999, "end": 1748249629.330451, "relative_end": 1748249629.330451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249629.331803, "relative_start": 8.319547176361084, "end": 1748249629.331803, "relative_end": 1748249629.331803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249629.332546, "relative_start": 8.320290088653564, "end": 1748249629.332546, "relative_end": 1748249629.332546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.trigger", "start": 1748249629.333248, "relative_start": 8.32099199295044, "end": 1748249629.333248, "relative_end": 1748249629.333248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249629.333878, "relative_start": 8.321622133255005, "end": 1748249629.333878, "relative_end": 1748249629.333878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.category-form", "start": 1748249629.337579, "relative_start": 8.325323104858398, "end": 1748249629.337579, "relative_end": 1748249629.337579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": 1748249629.339506, "relative_start": 8.327250003814697, "end": 1748249629.339506, "relative_end": 1748249629.339506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249629.340105, "relative_start": 8.32784914970398, "end": 1748249629.340105, "relative_end": 1748249629.340105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": 1748249629.340941, "relative_start": 8.32868504524231, "end": 1748249629.340941, "relative_end": 1748249629.340941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": 1748249629.343223, "relative_start": 8.33096718788147, "end": 1748249629.343223, "relative_end": 1748249629.343223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249629.344124, "relative_start": 8.331868171691895, "end": 1748249629.344124, "relative_end": 1748249629.344124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249629.344701, "relative_start": 8.33244514465332, "end": 1748249629.344701, "relative_end": 1748249629.344701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249629.345216, "relative_start": 8.33296012878418, "end": 1748249629.345216, "relative_end": 1748249629.345216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.350001, "relative_start": 8.337745189666748, "end": 1748249629.350001, "relative_end": 1748249629.350001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.350496, "relative_start": 8.338240146636963, "end": 1748249629.350496, "relative_end": 1748249629.350496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.35091, "relative_start": 8.338654041290283, "end": 1748249629.35091, "relative_end": 1748249629.35091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.351312, "relative_start": 8.339056015014648, "end": 1748249629.351312, "relative_end": 1748249629.351312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.351715, "relative_start": 8.33945918083191, "end": 1748249629.351715, "relative_end": 1748249629.351715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.352108, "relative_start": 8.339852094650269, "end": 1748249629.352108, "relative_end": 1748249629.352108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.352473, "relative_start": 8.340217113494873, "end": 1748249629.352473, "relative_end": 1748249629.352473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.353539, "relative_start": 8.341283082962036, "end": 1748249629.353539, "relative_end": 1748249629.353539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.354288, "relative_start": 8.342032194137573, "end": 1748249629.354288, "relative_end": 1748249629.354288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249629.355153, "relative_start": 8.342897176742554, "end": 1748249629.355153, "relative_end": 1748249629.355153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": 1748249629.355617, "relative_start": 8.343361139297485, "end": 1748249629.355617, "relative_end": 1748249629.355617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249629.356164, "relative_start": 8.343908071517944, "end": 1748249629.356164, "relative_end": 1748249629.356164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249629.356689, "relative_start": 8.344433069229126, "end": 1748249629.356689, "relative_end": 1748249629.356689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": 1748249629.357487, "relative_start": 8.345231056213379, "end": 1748249629.357487, "relative_end": 1748249629.357487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249629.358054, "relative_start": 8.345798015594482, "end": 1748249629.358054, "relative_end": 1748249629.358054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249629.359198, "relative_start": 8.34694218635559, "end": 1748249629.359198, "relative_end": 1748249629.359198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249629.359736, "relative_start": 8.347480058670044, "end": 1748249629.359736, "relative_end": 1748249629.359736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": 1748249629.360294, "relative_start": 8.34803819656372, "end": 1748249629.360294, "relative_end": 1748249629.360294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249629.360763, "relative_start": 8.348507165908813, "end": 1748249629.360763, "relative_end": 1748249629.360763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": 1748249629.361984, "relative_start": 8.349728107452393, "end": 1748249629.361984, "relative_end": 1748249629.361984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": 1748249629.363074, "relative_start": 8.350818157196045, "end": 1748249629.363074, "relative_end": 1748249629.363074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249629.363781, "relative_start": 8.351525068283081, "end": 1748249629.363781, "relative_end": 1748249629.363781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249629.364325, "relative_start": 8.35206913948059, "end": 1748249629.364325, "relative_end": 1748249629.364325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": 1748249629.364808, "relative_start": 8.35255217552185, "end": 1748249629.364808, "relative_end": 1748249629.364808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": 1748249629.36529, "relative_start": 8.353034019470215, "end": 1748249629.36529, "relative_end": 1748249629.36529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249629.366601, "relative_start": 8.354345083236694, "end": 1748249629.366601, "relative_end": 1748249629.366601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": 1748249629.367811, "relative_start": 8.355555057525635, "end": 1748249629.367811, "relative_end": 1748249629.367811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": 1748249629.368296, "relative_start": 8.356040000915527, "end": 1748249629.368296, "relative_end": 1748249629.368296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249629.36877, "relative_start": 8.356513977050781, "end": 1748249629.36877, "relative_end": 1748249629.36877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249629.3693, "relative_start": 8.357043981552124, "end": 1748249629.3693, "relative_end": 1748249629.3693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": 1748249629.370741, "relative_start": 8.358484983444214, "end": 1748249629.370741, "relative_end": 1748249629.370741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249629.373483, "relative_start": 8.361227035522461, "end": 1748249629.373483, "relative_end": 1748249629.373483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.tags", "start": 1748249629.374242, "relative_start": 8.36198616027832, "end": 1748249629.374242, "relative_end": 1748249629.374242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249630.408424, "relative_start": 9.396167993545532, "end": 1748249630.408424, "relative_end": 1748249630.408424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249630.409327, "relative_start": 9.397071123123169, "end": 1748249630.409327, "relative_end": 1748249630.409327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249630.410156, "relative_start": 9.397900104522705, "end": 1748249630.410156, "relative_end": 1748249630.410156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249630.411845, "relative_start": 9.39958906173706, "end": 1748249630.411845, "relative_end": 1748249630.411845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": 1748249630.412637, "relative_start": 9.400381088256836, "end": 1748249630.412637, "relative_end": 1748249630.412637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.plus", "start": 1748249630.415246, "relative_start": 9.402990102767944, "end": 1748249630.415246, "relative_end": 1748249630.415246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249630.416378, "relative_start": 9.404122114181519, "end": 1748249630.416378, "relative_end": 1748249630.416378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249630.418285, "relative_start": 9.406028985977173, "end": 1748249630.418285, "relative_end": 1748249630.418285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249630.419845, "relative_start": 9.407589197158813, "end": 1748249630.419845, "relative_end": 1748249630.419845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.trigger", "start": 1748249630.421142, "relative_start": 9.408886194229126, "end": 1748249630.421142, "relative_end": 1748249630.421142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249630.422124, "relative_start": 9.409868001937866, "end": 1748249630.422124, "relative_end": 1748249630.422124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.tag-form", "start": 1748249630.425109, "relative_start": 9.412853002548218, "end": 1748249630.425109, "relative_end": 1748249630.425109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": 1748249630.427029, "relative_start": 9.414772987365723, "end": 1748249630.427029, "relative_end": 1748249630.427029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249630.428128, "relative_start": 9.41587209701538, "end": 1748249630.428128, "relative_end": 1748249630.428128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": 1748249630.428984, "relative_start": 9.416728019714355, "end": 1748249630.428984, "relative_end": 1748249630.428984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": 1748249630.431211, "relative_start": 9.418955087661743, "end": 1748249630.431211, "relative_end": 1748249630.431211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249630.432363, "relative_start": 9.420107126235962, "end": 1748249630.432363, "relative_end": 1748249630.432363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249630.433133, "relative_start": 9.42087697982788, "end": 1748249630.433133, "relative_end": 1748249630.433133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": 1748249630.433594, "relative_start": 9.421338081359863, "end": 1748249630.433594, "relative_end": 1748249630.433594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249630.433931, "relative_start": 9.421675205230713, "end": 1748249630.433931, "relative_end": 1748249630.433931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249630.435079, "relative_start": 9.422823190689087, "end": 1748249630.435079, "relative_end": 1748249630.435079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249630.436004, "relative_start": 9.423748016357422, "end": 1748249630.436004, "relative_end": 1748249630.436004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": 1748249630.437084, "relative_start": 9.424828052520752, "end": 1748249630.437084, "relative_end": 1748249630.437084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249630.437598, "relative_start": 9.425342082977295, "end": 1748249630.437598, "relative_end": 1748249630.437598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": 1748249630.439748, "relative_start": 9.427492141723633, "end": 1748249630.439748, "relative_end": 1748249630.439748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": 1748249630.440683, "relative_start": 9.42842698097229, "end": 1748249630.440683, "relative_end": 1748249630.440683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249630.441333, "relative_start": 9.4290771484375, "end": 1748249630.441333, "relative_end": 1748249630.441333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249630.442042, "relative_start": 9.429786205291748, "end": 1748249630.442042, "relative_end": 1748249630.442042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": 1748249630.442789, "relative_start": 9.430533170700073, "end": 1748249630.442789, "relative_end": 1748249630.442789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249630.444051, "relative_start": 9.431795120239258, "end": 1748249630.444051, "relative_end": 1748249630.444051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": 1748249630.445401, "relative_start": 9.43314504623413, "end": 1748249630.445401, "relative_end": 1748249630.445401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": 1748249630.445898, "relative_start": 9.433642148971558, "end": 1748249630.445898, "relative_end": 1748249630.445898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249630.446344, "relative_start": 9.434087991714478, "end": 1748249630.446344, "relative_end": 1748249630.446344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249630.447026, "relative_start": 9.434770107269287, "end": 1748249630.447026, "relative_end": 1748249630.447026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": 1748249630.447615, "relative_start": 9.435359001159668, "end": 1748249630.447615, "relative_end": 1748249630.447615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::badge.close", "start": 1748249630.449347, "relative_start": 9.43709111213684, "end": 1748249630.449347, "relative_end": 1748249630.449347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": 1748249630.644973, "relative_start": 9.63271713256836, "end": 1748249630.644973, "relative_end": 1748249630.644973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": 1748249630.645645, "relative_start": 9.633388996124268, "end": 1748249630.645645, "relative_end": 1748249630.645645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::badge.index", "start": 1748249630.646264, "relative_start": 9.634008169174194, "end": 1748249630.646264, "relative_end": 1748249630.646264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-div", "start": 1748249630.652245, "relative_start": 9.639989137649536, "end": 1748249630.652245, "relative_end": 1748249630.652245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249630.654329, "relative_start": 9.642073154449463, "end": 1748249630.654329, "relative_end": 1748249630.654329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249630.655613, "relative_start": 9.643357038497925, "end": 1748249630.655613, "relative_end": 1748249630.655613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": 1748249630.656611, "relative_start": 9.644355058670044, "end": 1748249630.656611, "relative_end": 1748249630.656611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": 1748249630.659278, "relative_start": 9.647022008895874, "end": 1748249630.659278, "relative_end": 1748249630.659278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249630.660155, "relative_start": 9.647899150848389, "end": 1748249630.660155, "relative_end": 1748249630.660155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249630.660676, "relative_start": 9.648420095443726, "end": 1748249630.660676, "relative_end": 1748249630.660676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249630.661153, "relative_start": 9.648897171020508, "end": 1748249630.661153, "relative_end": 1748249630.661153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": 1748249630.661584, "relative_start": 9.649327993392944, "end": 1748249630.661584, "relative_end": 1748249630.661584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": 1748249630.663162, "relative_start": 9.650906085968018, "end": 1748249630.663162, "relative_end": 1748249630.663162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.currency-dollar", "start": 1748249630.664043, "relative_start": 9.651787042617798, "end": 1748249630.664043, "relative_end": 1748249630.664043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": 1748249630.664816, "relative_start": 9.652559995651245, "end": 1748249630.664816, "relative_end": 1748249630.664816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249630.666105, "relative_start": 9.653849124908447, "end": 1748249630.666105, "relative_end": 1748249630.666105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249630.666772, "relative_start": 9.654515981674194, "end": 1748249630.666772, "relative_end": 1748249630.666772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249630.667283, "relative_start": 9.655027151107788, "end": 1748249630.667283, "relative_end": 1748249630.667283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::date-picker.index", "start": 1748249630.66797, "relative_start": 9.65571403503418, "end": 1748249630.66797, "relative_end": 1748249630.66797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::date-picker.button", "start": 1748249631.777181, "relative_start": 10.764925003051758, "end": 1748249631.777181, "relative_end": 1748249631.777181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.calendar", "start": 1748249632.259073, "relative_start": 11.246817111968994, "end": 1748249632.259073, "relative_end": 1748249632.259073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::date-picker.selected", "start": 1748249632.332451, "relative_start": 11.320195198059082, "end": 1748249632.332451, "relative_end": 1748249632.332451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": 1748249632.375326, "relative_start": 11.363070011138916, "end": 1748249632.375326, "relative_end": 1748249632.375326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249632.375965, "relative_start": 11.363709211349487, "end": 1748249632.375965, "relative_end": 1748249632.375965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249632.378499, "relative_start": 11.366243124008179, "end": 1748249632.378499, "relative_end": 1748249632.378499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249632.381929, "relative_start": 11.369673013687134, "end": 1748249632.381929, "relative_end": 1748249632.381929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249632.382634, "relative_start": 11.370378017425537, "end": 1748249632.382634, "relative_end": 1748249632.382634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249632.384018, "relative_start": 11.371762037277222, "end": 1748249632.384018, "relative_end": 1748249632.384018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249632.384635, "relative_start": 11.372379064559937, "end": 1748249632.384635, "relative_end": 1748249632.384635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249632.386203, "relative_start": 11.373947143554688, "end": 1748249632.386203, "relative_end": 1748249632.386203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249632.38863, "relative_start": 11.376374006271362, "end": 1748249632.38863, "relative_end": 1748249632.38863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249632.389272, "relative_start": 11.377016067504883, "end": 1748249632.389272, "relative_end": 1748249632.389272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": 1748249632.389826, "relative_start": 11.377570152282715, "end": 1748249632.389826, "relative_end": 1748249632.389826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249632.390692, "relative_start": 11.378436088562012, "end": 1748249632.390692, "relative_end": 1748249632.390692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249632.391293, "relative_start": 11.379037141799927, "end": 1748249632.391293, "relative_end": 1748249632.391293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249632.391827, "relative_start": 11.379571199417114, "end": 1748249632.391827, "relative_end": 1748249632.391827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::switch", "start": 1748249632.392738, "relative_start": 11.380482196807861, "end": 1748249632.392738, "relative_end": 1748249632.392738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-reversed-inline-field", "start": 1748249632.664072, "relative_start": 11.651816129684448, "end": 1748249632.664072, "relative_end": 1748249632.664072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249633.0944, "relative_start": 12.082144021987915, "end": 1748249633.0944, "relative_end": 1748249633.0944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249633.095176, "relative_start": 12.08292007446289, "end": 1748249633.095176, "relative_end": 1748249633.095176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249633.095713, "relative_start": 12.083456993103027, "end": 1748249633.095713, "relative_end": 1748249633.095713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::switch", "start": 1748249633.097409, "relative_start": 12.085153102874756, "end": 1748249633.097409, "relative_end": 1748249633.097409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-reversed-inline-field", "start": 1748249633.100551, "relative_start": 12.088294982910156, "end": 1748249633.100551, "relative_end": 1748249633.100551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249633.101323, "relative_start": 12.089066982269287, "end": 1748249633.101323, "relative_end": 1748249633.101323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249633.102932, "relative_start": 12.090676069259644, "end": 1748249633.102932, "relative_end": 1748249633.102932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249633.10408, "relative_start": 12.091824054718018, "end": 1748249633.10408, "relative_end": 1748249633.10408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::textarea", "start": 1748249633.105419, "relative_start": 12.093163013458252, "end": 1748249633.105419, "relative_end": 1748249633.105419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": 1748249633.264266, "relative_start": 12.252010107040405, "end": 1748249633.264266, "relative_end": 1748249633.264266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249633.265305, "relative_start": 12.25304913520813, "end": 1748249633.265305, "relative_end": 1748249633.265305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249633.266587, "relative_start": 12.254331111907959, "end": 1748249633.266587, "relative_end": 1748249633.266587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": 1748249633.271564, "relative_start": 12.259308099746704, "end": 1748249633.271564, "relative_end": 1748249633.271564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1748249633.272979, "relative_start": 12.260723114013672, "end": 1748249633.272979, "relative_end": 1748249633.272979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249633.273662, "relative_start": 12.261406183242798, "end": 1748249633.273662, "relative_end": 1748249633.273662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249633.274484, "relative_start": 12.262228012084961, "end": 1748249633.274484, "relative_end": 1748249633.274484, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249633.275018, "relative_start": 12.262762069702148, "end": 1748249633.275018, "relative_end": 1748249633.275018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": 1748249633.275653, "relative_start": 12.263396978378296, "end": 1748249633.275653, "relative_end": 1748249633.275653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249633.276321, "relative_start": 12.264065027236938, "end": 1748249633.276321, "relative_end": 1748249633.276321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249633.276926, "relative_start": 12.264670133590698, "end": 1748249633.276926, "relative_end": 1748249633.276926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249633.277423, "relative_start": 12.265166997909546, "end": 1748249633.277423, "relative_end": 1748249633.277423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::date-picker.index", "start": 1748249633.27811, "relative_start": 12.265854120254517, "end": 1748249633.27811, "relative_end": 1748249633.27811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::date-picker.button", "start": 1748249633.27956, "relative_start": 12.267304182052612, "end": 1748249633.27956, "relative_end": 1748249633.27956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.calendar", "start": 1748249633.280378, "relative_start": 12.26812219619751, "end": 1748249633.280378, "relative_end": 1748249633.280378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::date-picker.selected", "start": 1748249633.280875, "relative_start": 12.268619060516357, "end": 1748249633.280875, "relative_end": 1748249633.280875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": 1748249633.281321, "relative_start": 12.269065141677856, "end": 1748249633.281321, "relative_end": 1748249633.281321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249633.281799, "relative_start": 12.269543170928955, "end": 1748249633.281799, "relative_end": 1748249633.281799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249633.283075, "relative_start": 12.270819187164307, "end": 1748249633.283075, "relative_end": 1748249633.283075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249633.283626, "relative_start": 12.27137017250061, "end": 1748249633.283626, "relative_end": 1748249633.283626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249633.284215, "relative_start": 12.271959066390991, "end": 1748249633.284215, "relative_end": 1748249633.284215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249633.286191, "relative_start": 12.273935079574585, "end": 1748249633.286191, "relative_end": 1748249633.286191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249633.287258, "relative_start": 12.275002002716064, "end": 1748249633.287258, "relative_end": 1748249633.287258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249633.28801, "relative_start": 12.27575397491455, "end": 1748249633.28801, "relative_end": 1748249633.28801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249633.289296, "relative_start": 12.277040004730225, "end": 1748249633.289296, "relative_end": 1748249633.289296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249633.289896, "relative_start": 12.277640104293823, "end": 1748249633.289896, "relative_end": 1748249633.289896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": 1748249633.290441, "relative_start": 12.27818512916565, "end": 1748249633.290441, "relative_end": 1748249633.290441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": 1748249633.291295, "relative_start": 12.279039144515991, "end": 1748249633.291295, "relative_end": 1748249633.291295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": 1748249633.291894, "relative_start": 12.279638051986694, "end": 1748249633.291894, "relative_end": 1748249633.291894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": 1748249633.292402, "relative_start": 12.28014612197876, "end": 1748249633.292402, "relative_end": 1748249633.292402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": 1748249633.292985, "relative_start": 12.280729055404663, "end": 1748249633.292985, "relative_end": 1748249633.292985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1748249633.293519, "relative_start": 12.28126311302185, "end": 1748249633.293519, "relative_end": 1748249633.293519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.file-uploader", "start": 1748249633.322657, "relative_start": 12.310401201248169, "end": 1748249633.322657, "relative_end": 1748249633.322657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": 1748249633.895932, "relative_start": 12.883676052093506, "end": 1748249633.895932, "relative_end": 1748249633.895932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.arrow-up-to-line", "start": 1748249633.896854, "relative_start": 12.884598016738892, "end": 1748249633.896854, "relative_end": 1748249633.896854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": 1748249633.948324, "relative_start": 12.936068058013916, "end": 1748249633.948324, "relative_end": 1748249633.948324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": 1748249633.961334, "relative_start": 12.94907808303833, "end": 1748249633.961334, "relative_end": 1748249633.961334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": 1748249633.962083, "relative_start": 12.949827194213867, "end": 1748249633.962083, "relative_end": 1748249633.962083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1748249633.962658, "relative_start": 12.950402021408081, "end": 1748249633.962658, "relative_end": 1748249633.962658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249633.964444, "relative_start": 12.95218801498413, "end": 1748249633.964444, "relative_end": 1748249633.964444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249633.966661, "relative_start": 12.954405069351196, "end": 1748249633.966661, "relative_end": 1748249633.966661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249633.967643, "relative_start": 12.955387115478516, "end": 1748249633.967643, "relative_end": 1748249633.967643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": 1748249633.96944, "relative_start": 12.957184076309204, "end": 1748249633.96944, "relative_end": 1748249633.96944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": 1748249633.972258, "relative_start": 12.960002183914185, "end": 1748249633.972258, "relative_end": 1748249633.972258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": 1748249633.973222, "relative_start": 12.960966110229492, "end": 1748249633.973222, "relative_end": 1748249633.973222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": 1748249633.974036, "relative_start": 12.961780071258545, "end": 1748249633.974036, "relative_end": 1748249633.974036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": 1748249633.97498, "relative_start": 12.962724208831787, "end": 1748249633.97498, "relative_end": 1748249633.97498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": 1748249633.990758, "relative_start": 12.978502035140991, "end": 1748249633.990758, "relative_end": 1748249633.990758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app", "start": 1748249633.994673, "relative_start": 12.982417106628418, "end": 1748249633.994673, "relative_end": 1748249633.994673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::main", "start": 1748249633.996149, "relative_start": 12.983893156051636, "end": 1748249633.996149, "relative_end": 1748249633.996149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app.sidebar", "start": 1748249633.99713, "relative_start": 12.98487401008606, "end": 1748249633.99713, "relative_end": 1748249633.99713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.head", "start": **********.003151, "relative_start": 12.99089503288269, "end": **********.003151, "relative_end": **********.003151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.toggle", "start": **********.006303, "relative_start": 12.994047164916992, "end": **********.006303, "relative_end": **********.006303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.007542, "relative_start": 12.995285987854004, "end": **********.007542, "relative_end": **********.007542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.009121, "relative_start": 12.996865034103394, "end": **********.009121, "relative_end": **********.009121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.009812, "relative_start": 12.997556209564209, "end": **********.009812, "relative_end": **********.009812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.010389, "relative_start": 12.998133182525635, "end": **********.010389, "relative_end": **********.010389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.011037, "relative_start": 12.998781204223633, "end": **********.011037, "relative_end": **********.011037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.app-logo", "start": **********.011972, "relative_start": 12.99971604347229, "end": **********.011972, "relative_end": **********.011972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.013731, "relative_start": 13.001475095748901, "end": **********.013731, "relative_end": **********.013731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.015731, "relative_start": 13.003475189208984, "end": **********.015731, "relative_end": **********.015731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.layout-dashboard", "start": **********.016765, "relative_start": 13.004509210586548, "end": **********.016765, "relative_end": **********.016765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.017607, "relative_start": 13.005351066589355, "end": **********.017607, "relative_end": **********.017607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.020099, "relative_start": 13.007843017578125, "end": **********.020099, "relative_end": **********.020099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.021818, "relative_start": 13.009562015533447, "end": **********.021818, "relative_end": **********.021818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.user", "start": **********.022906, "relative_start": 13.010650157928467, "end": **********.022906, "relative_end": **********.022906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.023745, "relative_start": 13.011489152908325, "end": **********.023745, "relative_end": **********.023745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.024801, "relative_start": 13.012545108795166, "end": **********.024801, "relative_end": **********.024801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.026012, "relative_start": 13.013756036758423, "end": **********.026012, "relative_end": **********.026012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.currency-dollar", "start": **********.026705, "relative_start": 13.014449119567871, "end": **********.026705, "relative_end": **********.026705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.027368, "relative_start": 13.015112161636353, "end": **********.027368, "relative_end": **********.027368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.028373, "relative_start": 13.016117095947266, "end": **********.028373, "relative_end": **********.028373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.02954, "relative_start": 13.017284154891968, "end": **********.02954, "relative_end": **********.02954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.scroll-text", "start": **********.030445, "relative_start": 13.018189191818237, "end": **********.030445, "relative_end": **********.030445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.031495, "relative_start": 13.0192391872406, "end": **********.031495, "relative_end": **********.031495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.032573, "relative_start": 13.020317077636719, "end": **********.032573, "relative_end": **********.032573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.033733, "relative_start": 13.021476984024048, "end": **********.033733, "relative_end": **********.033733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.queue-list", "start": **********.03515, "relative_start": 13.022894144058228, "end": **********.03515, "relative_end": **********.03515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.036704, "relative_start": 13.024448156356812, "end": **********.036704, "relative_end": **********.036704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.038236, "relative_start": 13.025979995727539, "end": **********.038236, "relative_end": **********.038236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.040024, "relative_start": 13.0277681350708, "end": **********.040024, "relative_end": **********.040024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.tags", "start": **********.041346, "relative_start": 13.029090166091919, "end": **********.041346, "relative_end": **********.041346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.042644, "relative_start": 13.030388116836548, "end": **********.042644, "relative_end": **********.042644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.group", "start": **********.043969, "relative_start": 13.031713008880615, "end": **********.043969, "relative_end": **********.043969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.045464, "relative_start": 13.033208131790161, "end": **********.045464, "relative_end": **********.045464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.047157, "relative_start": 13.034901142120361, "end": **********.047157, "relative_end": **********.047157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.shield-check", "start": **********.048639, "relative_start": 13.036383152008057, "end": **********.048639, "relative_end": **********.048639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.049659, "relative_start": 13.037403106689453, "end": **********.049659, "relative_end": **********.049659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.050902, "relative_start": 13.03864598274231, "end": **********.050902, "relative_end": **********.050902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.05296, "relative_start": 13.040704011917114, "end": **********.05296, "relative_end": **********.05296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.key", "start": **********.054712, "relative_start": 13.042456150054932, "end": **********.054712, "relative_end": **********.054712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.055537, "relative_start": 13.043281078338623, "end": **********.055537, "relative_end": **********.055537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.056532, "relative_start": 13.044275999069214, "end": **********.056532, "relative_end": **********.056532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.057673, "relative_start": 13.045417070388794, "end": **********.057673, "relative_end": **********.057673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.users", "start": **********.058767, "relative_start": 13.046511173248291, "end": **********.058767, "relative_end": **********.058767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.059595, "relative_start": 13.04733920097351, "end": **********.059595, "relative_end": **********.059595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.group", "start": **********.06029, "relative_start": 13.048034191131592, "end": **********.06029, "relative_end": **********.06029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-down", "start": **********.061021, "relative_start": 13.048765182495117, "end": **********.061021, "relative_end": **********.061021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-right", "start": **********.062327, "relative_start": 13.050071001052856, "end": **********.062327, "relative_end": **********.062327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.index", "start": **********.063314, "relative_start": 13.051058053970337, "end": **********.063314, "relative_end": **********.063314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.064073, "relative_start": 13.051817178726196, "end": **********.064073, "relative_end": **********.064073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.064773, "relative_start": 13.052517175674438, "end": **********.064773, "relative_end": **********.064773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.06595, "relative_start": 13.053694009780884, "end": **********.06595, "relative_end": **********.06595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.folder-git-2", "start": **********.06684, "relative_start": 13.05458402633667, "end": **********.06684, "relative_end": **********.06684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.067713, "relative_start": 13.05545711517334, "end": **********.067713, "relative_end": **********.067713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.index", "start": **********.069216, "relative_start": 13.056960105895996, "end": **********.069216, "relative_end": **********.069216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::profile", "start": **********.071428, "relative_start": 13.0591721534729, "end": **********.071428, "relative_end": **********.071428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::avatar.index", "start": **********.073001, "relative_start": 13.060745000839233, "end": **********.073001, "relative_end": **********.073001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.074683, "relative_start": 13.062427043914795, "end": **********.074683, "relative_end": **********.074683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.075386, "relative_start": 13.063130140304565, "end": **********.075386, "relative_end": **********.075386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.076103, "relative_start": 13.063847064971924, "end": **********.076103, "relative_end": **********.076103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevrons-up-down", "start": **********.076982, "relative_start": 13.064726114273071, "end": **********.076982, "relative_end": **********.076982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.078856, "relative_start": 13.06660008430481, "end": **********.078856, "relative_end": **********.078856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.079789, "relative_start": 13.067533016204834, "end": **********.079789, "relative_end": **********.079789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.080854, "relative_start": 13.06859803199768, "end": **********.080854, "relative_end": **********.080854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.082399, "relative_start": 13.070142984390259, "end": **********.082399, "relative_end": **********.082399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.083716, "relative_start": 13.071460008621216, "end": **********.083716, "relative_end": **********.083716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.085011, "relative_start": 13.072755098342896, "end": **********.085011, "relative_end": **********.085011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.sun", "start": **********.087065, "relative_start": 13.074809074401855, "end": **********.087065, "relative_end": **********.087065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.088038, "relative_start": 13.075782060623169, "end": **********.088038, "relative_end": **********.088038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.08872, "relative_start": 13.076464176177979, "end": **********.08872, "relative_end": **********.08872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.089627, "relative_start": 13.07737112045288, "end": **********.089627, "relative_end": **********.089627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.moon", "start": **********.090695, "relative_start": 13.078438997268677, "end": **********.090695, "relative_end": **********.090695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.09151, "relative_start": 13.079254150390625, "end": **********.09151, "relative_end": **********.09151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.09219, "relative_start": 13.079934120178223, "end": **********.09219, "relative_end": **********.09219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.093043, "relative_start": 13.080787181854248, "end": **********.093043, "relative_end": **********.093043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.computer-desktop", "start": **********.094618, "relative_start": 13.082362174987793, "end": **********.094618, "relative_end": **********.094618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.index", "start": **********.095962, "relative_start": 13.083706140518188, "end": **********.095962, "relative_end": **********.095962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.variants.segmented", "start": **********.097737, "relative_start": 13.0854811668396, "end": **********.097737, "relative_end": **********.097737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.099043, "relative_start": 13.086786985397339, "end": **********.099043, "relative_end": **********.099043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.100625, "relative_start": 13.088369131088257, "end": **********.100625, "relative_end": **********.100625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.101307, "relative_start": 13.089051008224487, "end": **********.101307, "relative_end": **********.101307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.10384, "relative_start": 13.091584205627441, "end": **********.10384, "relative_end": **********.10384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.10541, "relative_start": 13.093154191970825, "end": **********.10541, "relative_end": **********.10541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.cog", "start": **********.106524, "relative_start": 13.094268083572388, "end": **********.106524, "relative_end": **********.106524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.107408, "relative_start": 13.095152139663696, "end": **********.107408, "relative_end": **********.107408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.10821, "relative_start": 13.095954179763794, "end": **********.10821, "relative_end": **********.10821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.108673, "relative_start": 13.09641718864441, "end": **********.108673, "relative_end": **********.108673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.109369, "relative_start": 13.097113132476807, "end": **********.109369, "relative_end": **********.109369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.110465, "relative_start": 13.098209142684937, "end": **********.110465, "relative_end": **********.110465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.111578, "relative_start": 13.099322080612183, "end": **********.111578, "relative_end": **********.111578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.arrow-right-start-on-rectangle", "start": **********.112678, "relative_start": 13.100422143936157, "end": **********.112678, "relative_end": **********.112678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.11344, "relative_start": 13.101184129714966, "end": **********.11344, "relative_end": **********.11344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.index", "start": **********.114247, "relative_start": 13.101991176605225, "end": **********.114247, "relative_end": **********.114247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown", "start": **********.114972, "relative_start": 13.102716207504272, "end": **********.114972, "relative_end": **********.114972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.index", "start": **********.115751, "relative_start": 13.103495121002197, "end": **********.115751, "relative_end": **********.115751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.backdrop", "start": **********.117253, "relative_start": 13.104997158050537, "end": **********.117253, "relative_end": **********.117253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.toggle", "start": **********.119238, "relative_start": 13.106981992721558, "end": **********.119238, "relative_end": **********.119238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.120318, "relative_start": 13.108062028884888, "end": **********.120318, "relative_end": **********.120318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.12277, "relative_start": 13.110514163970947, "end": **********.12277, "relative_end": **********.12277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.panel-left", "start": **********.123885, "relative_start": 13.111629009246826, "end": **********.123885, "relative_end": **********.123885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.124848, "relative_start": 13.112591981887817, "end": **********.124848, "relative_end": **********.124848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.125991, "relative_start": 13.11373519897461, "end": **********.125991, "relative_end": **********.125991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.126991, "relative_start": 13.114735126495361, "end": **********.126991, "relative_end": **********.126991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::profile", "start": **********.127713, "relative_start": 13.11545705795288, "end": **********.127713, "relative_end": **********.127713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::avatar.index", "start": **********.128945, "relative_start": 13.116689205169678, "end": **********.128945, "relative_end": **********.128945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.130959, "relative_start": 13.118703126907349, "end": **********.130959, "relative_end": **********.130959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.131835, "relative_start": 13.119579076766968, "end": **********.131835, "relative_end": **********.131835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.132767, "relative_start": 13.120511054992676, "end": **********.132767, "relative_end": **********.132767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevrons-up-down", "start": **********.133611, "relative_start": 13.121355056762695, "end": **********.133611, "relative_end": **********.133611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.134695, "relative_start": 13.12243914604187, "end": **********.134695, "relative_end": **********.134695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.136182, "relative_start": 13.123926162719727, "end": **********.136182, "relative_end": **********.136182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.137105, "relative_start": 13.124849081039429, "end": **********.137105, "relative_end": **********.137105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.138109, "relative_start": 13.125853061676025, "end": **********.138109, "relative_end": **********.138109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.139083, "relative_start": 13.126827001571655, "end": **********.139083, "relative_end": **********.139083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.139977, "relative_start": 13.127721071243286, "end": **********.139977, "relative_end": **********.139977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.sun", "start": **********.140863, "relative_start": 13.128607034683228, "end": **********.140863, "relative_end": **********.140863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.141595, "relative_start": 13.12933897972107, "end": **********.141595, "relative_end": **********.141595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.142327, "relative_start": 13.13007116317749, "end": **********.142327, "relative_end": **********.142327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.143229, "relative_start": 13.130973100662231, "end": **********.143229, "relative_end": **********.143229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.moon", "start": **********.143876, "relative_start": 13.131620168685913, "end": **********.143876, "relative_end": **********.143876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.144554, "relative_start": 13.132297992706299, "end": **********.144554, "relative_end": **********.144554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.145271, "relative_start": 13.133015155792236, "end": **********.145271, "relative_end": **********.145271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.146137, "relative_start": 13.133881092071533, "end": **********.146137, "relative_end": **********.146137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.computer-desktop", "start": **********.146779, "relative_start": 13.134523153305054, "end": **********.146779, "relative_end": **********.146779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.index", "start": **********.147369, "relative_start": 13.135113000869751, "end": **********.147369, "relative_end": **********.147369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.variants.segmented", "start": **********.147966, "relative_start": 13.135710000991821, "end": **********.147966, "relative_end": **********.147966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.148678, "relative_start": 13.136422157287598, "end": **********.148678, "relative_end": **********.148678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.149731, "relative_start": 13.13747501373291, "end": **********.149731, "relative_end": **********.149731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.15025, "relative_start": 13.137994050979614, "end": **********.15025, "relative_end": **********.15025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.150924, "relative_start": 13.138668060302734, "end": **********.150924, "relative_end": **********.150924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.153027, "relative_start": 13.14077115058899, "end": **********.153027, "relative_end": **********.153027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.cog", "start": **********.154157, "relative_start": 13.141901016235352, "end": **********.154157, "relative_end": **********.154157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.154909, "relative_start": 13.142652988433838, "end": **********.154909, "relative_end": **********.154909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.155991, "relative_start": 13.14373517036438, "end": **********.155991, "relative_end": **********.155991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.156605, "relative_start": 13.144349098205566, "end": **********.156605, "relative_end": **********.156605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.15726, "relative_start": 13.145004034042358, "end": **********.15726, "relative_end": **********.15726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.158186, "relative_start": 13.145930051803589, "end": **********.158186, "relative_end": **********.158186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.159605, "relative_start": 13.147349119186401, "end": **********.159605, "relative_end": **********.159605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.arrow-right-start-on-rectangle", "start": **********.160408, "relative_start": 13.148152112960815, "end": **********.160408, "relative_end": **********.160408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.161131, "relative_start": 13.148874998092651, "end": **********.161131, "relative_end": **********.161131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.index", "start": **********.161998, "relative_start": 13.149742126464844, "end": **********.161998, "relative_end": **********.161998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown", "start": **********.162566, "relative_start": 13.150310039520264, "end": **********.162566, "relative_end": **********.162566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::header", "start": **********.163368, "relative_start": 13.151112079620361, "end": **********.163368, "relative_end": **********.163368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::toast.index", "start": **********.164743, "relative_start": 13.15248703956604, "end": **********.164743, "relative_end": **********.164743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.173933, "relative_start": 13.161677122116089, "end": **********.174363, "relative_end": **********.174363, "duration": 0.0004298686981201172, "duration_str": "430μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 32299464, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 427, "nb_templates": 427, "templates": [{"name": "1x livewire.transaction-form", "param_count": null, "params": [], "start": **********.679771, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/transaction-form.blade.phplivewire.transaction-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Ftransaction-form.blade.php&line=1", "ajax": false, "filename": "transaction-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.transaction-form"}, {"name": "5x ********************************::heading", "param_count": null, "params": [], "start": **********.135777, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.php********************************::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::heading"}, {"name": "17x ********************************::label", "param_count": null, "params": [], "start": **********.140053, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.php********************************::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 17, "name_original": "********************************::label"}, {"name": "55x components.option", "param_count": null, "params": [], "start": **********.141642, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/option.blade.phpcomponents.option", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Foption.blade.php&line=1", "ajax": false, "filename": "option.blade.php", "line": "?"}, "render_count": 55, "name_original": "components.option"}, {"name": "6x components.select", "param_count": null, "params": [], "start": **********.144233, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/select.blade.phpcomponents.select", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 6, "name_original": "components.select"}, {"name": "16x ********************************::error", "param_count": null, "params": [], "start": **********.145414, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.php********************************::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 16, "name_original": "********************************::error"}, {"name": "16x ********************************::field", "param_count": null, "params": [], "start": **********.146202, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.php********************************::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 16, "name_original": "********************************::field"}, {"name": "5x ********************************::select.option.index", "param_count": null, "params": [], "start": **********.148223, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/option/index.blade.php********************************::select.option.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foption%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::select.option.index"}, {"name": "5x ********************************::select.option.variants.custom", "param_count": null, "params": [], "start": **********.387388, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/option/variants/custom.blade.php********************************::select.option.variants.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foption%2Fvariants%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::select.option.variants.custom"}, {"name": "5x ********************************::select.indicator.index", "param_count": null, "params": [], "start": **********.660795, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/indicator/index.blade.php********************************::select.indicator.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findicator%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::select.indicator.index"}, {"name": "5x ********************************::select.indicator.variants.check", "param_count": null, "params": [], "start": **********.808652, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/indicator/variants/check.blade.php********************************::select.indicator.variants.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findicator%2Fvariants%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::select.indicator.variants.check"}, {"name": "36x ********************************::icon.index", "param_count": null, "params": [], "start": **********.939057, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.php********************************::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 36, "name_original": "********************************::icon.index"}, {"name": "5x ********************************::icon.check", "param_count": null, "params": [], "start": **********.940711, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/check.blade.php********************************::icon.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::icon.check"}, {"name": "15x ********************************::with-field", "param_count": null, "params": [], "start": **********.941803, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.php********************************::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}, "render_count": 15, "name_original": "********************************::with-field"}, {"name": "1x ********************************::select.index", "param_count": null, "params": [], "start": **********.964388, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/index.blade.php********************************::select.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.index"}, {"name": "1x ********************************::select.variants.listbox", "param_count": null, "params": [], "start": 1748249628.19353, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/variants/listbox.blade.php********************************::select.variants.listbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fvariants%2Flistbox.blade.php&line=1", "ajax": false, "filename": "listbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.variants.listbox"}, {"name": "1x ********************************::select.button", "param_count": null, "params": [], "start": 1748249628.460076, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/button.blade.php********************************::select.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.button"}, {"name": "1x ********************************::select.selected", "param_count": null, "params": [], "start": 1748249628.979642, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/selected.blade.php********************************::select.selected", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fselected.blade.php&line=1", "ajax": false, "filename": "selected.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.selected"}, {"name": "7x ********************************::icon.x-mark", "param_count": null, "params": [], "start": 1748249629.049245, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/x-mark.blade.php********************************::icon.x-mark", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fx-mark.blade.php&line=1", "ajax": false, "filename": "x-mark.blade.php", "line": "?"}, "render_count": 7, "name_original": "********************************::icon.x-mark"}, {"name": "19x ********************************::button.index", "param_count": null, "params": [], "start": 1748249629.050492, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.php********************************::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 19, "name_original": "********************************::button.index"}, {"name": "35x ********************************::button-or-link", "param_count": null, "params": [], "start": 1748249629.055916, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.php********************************::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}, "render_count": 35, "name_original": "********************************::button-or-link"}, {"name": "21x ********************************::with-tooltip", "param_count": null, "params": [], "start": 1748249629.057006, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.php********************************::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}, "render_count": 21, "name_original": "********************************::with-tooltip"}, {"name": "2x ********************************::icon.chevron-down", "param_count": null, "params": [], "start": 1748249629.058356, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-down.blade.php********************************::icon.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.chevron-down"}, {"name": "1x ********************************::select.options", "param_count": null, "params": [], "start": 1748249629.059303, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/options.blade.php********************************::select.options", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foptions.blade.php&line=1", "ajax": false, "filename": "options.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.options"}, {"name": "1x components.categories", "param_count": null, "params": [], "start": 1748249629.313629, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/categories.blade.phpcomponents.categories", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcategories.blade.php&line=1", "ajax": false, "filename": "categories.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.categories"}, {"name": "2x ********************************::icon.plus", "param_count": null, "params": [], "start": 1748249629.329766, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/plus.blade.php********************************::icon.plus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fplus.blade.php&line=1", "ajax": false, "filename": "plus.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.plus"}, {"name": "2x ********************************::modal.trigger", "param_count": null, "params": [], "start": 1748249629.33319, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/trigger.blade.php********************************::modal.trigger", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Ftrigger.blade.php&line=1", "ajax": false, "filename": "trigger.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.trigger"}, {"name": "1x livewire.category-form", "param_count": null, "params": [], "start": 1748249629.337483, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/category-form.blade.phplivewire.category-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fcategory-form.blade.php&line=1", "ajax": false, "filename": "category-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.category-form"}, {"name": "4x ********************************::input.index", "param_count": null, "params": [], "start": 1748249629.340882, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.php********************************::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::input.index"}, {"name": "4x ********************************::spacer", "param_count": null, "params": [], "start": 1748249629.357433, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/spacer.blade.php********************************::spacer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fspacer.blade.php&line=1", "ajax": false, "filename": "spacer.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::spacer"}, {"name": "4x ********************************::modal.close", "param_count": null, "params": [], "start": 1748249629.360242, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/close.blade.php********************************::modal.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::modal.close"}, {"name": "5x ********************************::icon.loading", "param_count": null, "params": [], "start": 1748249629.363017, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.php********************************::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::icon.loading"}, {"name": "2x ********************************::modal.index", "param_count": null, "params": [], "start": 1748249629.365239, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/index.blade.php********************************::modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.index"}, {"name": "1x components.tags", "param_count": null, "params": [], "start": 1748249629.374186, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/tags.blade.phpcomponents.tags", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftags.blade.php&line=1", "ajax": false, "filename": "tags.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.tags"}, {"name": "1x livewire.tag-form", "param_count": null, "params": [], "start": 1748249630.425048, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/tag-form.blade.phplivewire.tag-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Ftag-form.blade.php&line=1", "ajax": false, "filename": "tag-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.tag-form"}, {"name": "1x ********************************::badge.close", "param_count": null, "params": [], "start": 1748249630.449293, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/badge/close.blade.php********************************::badge.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbadge%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::badge.close"}, {"name": "1x ********************************::badge.index", "param_count": null, "params": [], "start": 1748249630.646207, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/badge/index.blade.php********************************::badge.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbadge%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::badge.index"}, {"name": "1x ********************************::button-or-div", "param_count": null, "params": [], "start": 1748249630.652153, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-div.blade.php********************************::button-or-div", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-div.blade.php&line=1", "ajax": false, "filename": "button-or-div.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::button-or-div"}, {"name": "2x ********************************::icon.currency-dollar", "param_count": null, "params": [], "start": 1748249630.663985, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/currency-dollar.blade.php********************************::icon.currency-dollar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcurrency-dollar.blade.php&line=1", "ajax": false, "filename": "currency-dollar.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.currency-dollar"}, {"name": "2x ********************************::date-picker.index", "param_count": null, "params": [], "start": 1748249630.667914, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/date-picker/index.blade.php********************************::date-picker.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fdate-picker%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::date-picker.index"}, {"name": "2x ********************************::date-picker.button", "param_count": null, "params": [], "start": 1748249631.777083, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/date-picker/button.blade.php********************************::date-picker.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fdate-picker%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::date-picker.button"}, {"name": "2x ********************************::icon.calendar", "param_count": null, "params": [], "start": 1748249632.259013, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/calendar.blade.php********************************::icon.calendar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcalendar.blade.php&line=1", "ajax": false, "filename": "calendar.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.calendar"}, {"name": "2x ********************************::date-picker.selected", "param_count": null, "params": [], "start": 1748249632.332386, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/date-picker/selected.blade.php********************************::date-picker.selected", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fdate-picker%2Fselected.blade.php&line=1", "ajax": false, "filename": "selected.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::date-picker.selected"}, {"name": "2x ********************************::switch", "param_count": null, "params": [], "start": 1748249632.392676, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/switch.blade.php********************************::switch", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::switch"}, {"name": "2x ********************************::with-reversed-inline-field", "param_count": null, "params": [], "start": 1748249632.663978, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-reversed-inline-field.blade.php********************************::with-reversed-inline-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-reversed-inline-field.blade.php&line=1", "ajax": false, "filename": "with-reversed-inline-field.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::with-reversed-inline-field"}, {"name": "1x ********************************::textarea", "param_count": null, "params": [], "start": 1748249633.105359, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/textarea.blade.php********************************::textarea", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::textarea"}, {"name": "3x components.card", "param_count": null, "params": [], "start": 1748249633.271474, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 3, "name_original": "components.card"}, {"name": "3x ********************************::card.index", "param_count": null, "params": [], "start": 1748249633.27292, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/card/index.blade.php********************************::card.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::card.index"}, {"name": "1x livewire.file-uploader", "param_count": null, "params": [], "start": 1748249633.322574, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/file-uploader.blade.phplivewire.file-uploader", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Ffile-uploader.blade.php&line=1", "ajax": false, "filename": "file-uploader.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.file-uploader"}, {"name": "1x ********************************::icon.arrow-up-to-line", "param_count": null, "params": [], "start": 1748249633.8968, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/arrow-up-to-line.blade.php********************************::icon.arrow-up-to-line", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Farrow-up-to-line.blade.php&line=1", "ajax": false, "filename": "arrow-up-to-line.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.arrow-up-to-line"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": 1748249633.990677, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x components.layouts.app", "param_count": null, "params": [], "start": 1748249633.99457, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app.blade.phpcomponents.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app"}, {"name": "1x ********************************::main", "param_count": null, "params": [], "start": 1748249633.996085, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/main.blade.php********************************::main", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmain.blade.php&line=1", "ajax": false, "filename": "main.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::main"}, {"name": "1x components.layouts.app.sidebar", "param_count": null, "params": [], "start": 1748249633.997059, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app/sidebar.blade.phpcomponents.layouts.app.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app.sidebar"}, {"name": "1x partials.head", "param_count": null, "params": [], "start": **********.003034, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/partials/head.blade.phppartials.head", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fpartials%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.head"}, {"name": "2x ********************************::sidebar.toggle", "param_count": null, "params": [], "start": **********.006197, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/toggle.blade.php********************************::sidebar.toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Ftoggle.blade.php&line=1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::sidebar.toggle"}, {"name": "1x components.app-logo", "param_count": null, "params": [], "start": **********.011909, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/app-logo.blade.phpcomponents.app-logo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fapp-logo.blade.php&line=1", "ajax": false, "filename": "app-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.app-logo"}, {"name": "10x ********************************::navlist.item", "param_count": null, "params": [], "start": **********.013671, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/item.blade.php********************************::navlist.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::navlist.item"}, {"name": "1x ********************************::icon.layout-dashboard", "param_count": null, "params": [], "start": **********.0167, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/layout-dashboard.blade.php********************************::icon.layout-dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Flayout-dashboard.blade.php&line=1", "ajax": false, "filename": "layout-dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.layout-dashboard"}, {"name": "1x ********************************::icon.user", "param_count": null, "params": [], "start": **********.022846, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/user.blade.php********************************::icon.user", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.user"}, {"name": "1x ********************************::icon.scroll-text", "param_count": null, "params": [], "start": **********.030358, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/scroll-text.blade.php********************************::icon.scroll-text", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fscroll-text.blade.php&line=1", "ajax": false, "filename": "scroll-text.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.scroll-text"}, {"name": "1x ********************************::icon.queue-list", "param_count": null, "params": [], "start": **********.034779, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/queue-list.blade.php********************************::icon.queue-list", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fqueue-list.blade.php&line=1", "ajax": false, "filename": "queue-list.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.queue-list"}, {"name": "1x ********************************::icon.tags", "param_count": null, "params": [], "start": **********.041244, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/tags.blade.php********************************::icon.tags", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ftags.blade.php&line=1", "ajax": false, "filename": "tags.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.tags"}, {"name": "2x ********************************::navlist.group", "param_count": null, "params": [], "start": **********.043864, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/navlist/group.blade.php********************************::navlist.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::navlist.group"}, {"name": "1x ********************************::icon.shield-check", "param_count": null, "params": [], "start": **********.04854, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/shield-check.blade.php********************************::icon.shield-check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fshield-check.blade.php&line=1", "ajax": false, "filename": "shield-check.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.shield-check"}, {"name": "1x ********************************::icon.key", "param_count": null, "params": [], "start": **********.054648, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/key.blade.php********************************::icon.key", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fkey.blade.php&line=1", "ajax": false, "filename": "key.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.key"}, {"name": "1x ********************************::icon.users", "param_count": null, "params": [], "start": **********.058702, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/users.blade.php********************************::icon.users", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fusers.blade.php&line=1", "ajax": false, "filename": "users.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.users"}, {"name": "1x ********************************::icon.chevron-right", "param_count": null, "params": [], "start": **********.062261, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-right.blade.php********************************::icon.chevron-right", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-right.blade.php&line=1", "ajax": false, "filename": "chevron-right.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.chevron-right"}, {"name": "2x ********************************::navlist.index", "param_count": null, "params": [], "start": **********.063247, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/index.blade.php********************************::navlist.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::navlist.index"}, {"name": "1x ********************************::icon.folder-git-2", "param_count": null, "params": [], "start": **********.066777, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/folder-git-2.blade.php********************************::icon.folder-git-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ffolder-git-2.blade.php&line=1", "ajax": false, "filename": "folder-git-2.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.folder-git-2"}, {"name": "2x ********************************::profile", "param_count": null, "params": [], "start": **********.071365, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/profile.blade.php********************************::profile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fprofile.blade.php&line=1", "ajax": false, "filename": "profile.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::profile"}, {"name": "2x ********************************::avatar.index", "param_count": null, "params": [], "start": **********.072929, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/avatar/index.blade.php********************************::avatar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Favatar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::avatar.index"}, {"name": "2x ********************************::icon.chevrons-up-down", "param_count": null, "params": [], "start": **********.076914, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/chevrons-up-down.blade.php********************************::icon.chevrons-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevrons-up-down.blade.php&line=1", "ajax": false, "filename": "chevrons-up-down.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.chevrons-up-down"}, {"name": "4x ********************************::menu.radio.group", "param_count": null, "params": [], "start": **********.07879, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/radio/group.blade.php********************************::menu.radio.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fradio%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::menu.radio.group"}, {"name": "6x ********************************::menu.separator", "param_count": null, "params": [], "start": **********.079729, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/separator.blade.php********************************::menu.separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::menu.separator"}, {"name": "6x ********************************::separator", "param_count": null, "params": [], "start": **********.080791, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/separator.blade.php********************************::separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::separator"}, {"name": "6x ********************************::radio.index", "param_count": null, "params": [], "start": **********.082331, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/index.blade.php********************************::radio.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::radio.index"}, {"name": "6x ********************************::radio.variants.segmented", "param_count": null, "params": [], "start": **********.083645, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/variants/segmented.blade.php********************************::radio.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::radio.variants.segmented"}, {"name": "2x ********************************::icon.sun", "param_count": null, "params": [], "start": **********.086939, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/sun.blade.php********************************::icon.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.sun"}, {"name": "2x ********************************::icon.moon", "param_count": null, "params": [], "start": **********.090634, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/moon.blade.php********************************::icon.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.moon"}, {"name": "2x ********************************::icon.computer-desktop", "param_count": null, "params": [], "start": **********.094521, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/computer-desktop.blade.php********************************::icon.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.computer-desktop"}, {"name": "2x ********************************::radio.group.index", "param_count": null, "params": [], "start": **********.095862, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/index.blade.php********************************::radio.group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::radio.group.index"}, {"name": "2x ********************************::radio.group.variants.segmented", "param_count": null, "params": [], "start": **********.097638, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/variants/segmented.blade.php********************************::radio.group.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::radio.group.variants.segmented"}, {"name": "4x ********************************::menu.item", "param_count": null, "params": [], "start": **********.103692, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/item.blade.php********************************::menu.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::menu.item"}, {"name": "2x ********************************::icon.cog", "param_count": null, "params": [], "start": **********.10644, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/cog.blade.php********************************::icon.cog", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcog.blade.php&line=1", "ajax": false, "filename": "cog.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.cog"}, {"name": "2x ********************************::icon.arrow-right-start-on-rectangle", "param_count": null, "params": [], "start": **********.112607, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/arrow-right-start-on-rectangle.blade.php********************************::icon.arrow-right-start-on-rectangle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Farrow-right-start-on-rectangle.blade.php&line=1", "ajax": false, "filename": "arrow-right-start-on-rectangle.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.arrow-right-start-on-rectangle"}, {"name": "2x ********************************::menu.index", "param_count": null, "params": [], "start": **********.114183, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/index.blade.php********************************::menu.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::menu.index"}, {"name": "2x ********************************::dropdown", "param_count": null, "params": [], "start": **********.114909, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/dropdown.blade.php********************************::dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::dropdown"}, {"name": "1x ********************************::sidebar.index", "param_count": null, "params": [], "start": **********.115687, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/index.blade.php********************************::sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::sidebar.index"}, {"name": "1x ********************************::sidebar.backdrop", "param_count": null, "params": [], "start": **********.117159, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/backdrop.blade.php********************************::sidebar.backdrop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Fbackdrop.blade.php&line=1", "ajax": false, "filename": "backdrop.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::sidebar.backdrop"}, {"name": "1x ********************************::icon.panel-left", "param_count": null, "params": [], "start": **********.123798, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/panel-left.blade.php********************************::icon.panel-left", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fpanel-left.blade.php&line=1", "ajax": false, "filename": "panel-left.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.panel-left"}, {"name": "1x ********************************::header", "param_count": null, "params": [], "start": **********.163266, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/header.blade.php********************************::header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::header"}, {"name": "1x ********************************::toast.index", "param_count": null, "params": [], "start": **********.164656, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/toast/index.blade.php********************************::toast.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ftoast%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::toast.index"}]}, "queries": {"count": 12, "nb_statements": 11, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01925, "accumulated_duration_str": "19.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 131}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 111}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 60}, {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 88}], "start": **********.494756, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CacheManager.php:245", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheManager.php&line=245", "ajax": false, "filename": "CacheManager.php", "line": "245"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn' limit 1", "type": "query", "params": [], "bindings": ["dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.520478, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 21.558}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.5517461, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 21.558, "width_percent": 3.429}, {"sql": "select * from `cache` where `key` in ('laravel_cache_spatie.permission.cache')", "type": "query", "params": [], "bindings": ["laravel_cache_spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.565153, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "daily", "explain": null, "start_percent": 24.987, "width_percent": 4.571}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.570777, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "daily", "explain": null, "start_percent": 29.558, "width_percent": 3.688}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.58292, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "daily", "explain": null, "start_percent": 33.247, "width_percent": 5.974}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1748336021, 'laravel_cache_spatie.permission.cache', 'a:3:{s:5:\\\"alias\\\";a:4:{s:1:\\\"a\\\";s:2:\\\"id\\\";s:1:\\\"b\\\";s:4:\\\"name\\\";s:1:\\\"c\\\";s:10:\\\"guard_name\\\";s:1:\\\"r\\\";s:5:\\\"roles\\\";}s:11:\\\"permissions\\\";a:31:{i:0;a:4:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:17:\\\"view transactions\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:1;a:4:{s:1:\\\"a\\\";i:2;s:1:\\\"b\\\";s:19:\\\"create transactions\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:3;i:2;i:4;}}i:2;a:4:{s:1:\\\"a\\\";i:3;s:1:\\\"b\\\";s:17:\\\"edit transactions\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:3;i:2;i:4;}}i:3;a:4:{s:1:\\\"a\\\";i:4;s:1:\\\"b\\\";s:19:\\\"delete transactions\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:4;a:4:{s:1:\\\"a\\\";i:5;s:1:\\\"b\\\";s:13:\\\"view accounts\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:5;a:4:{s:1:\\\"a\\\";i:6;s:1:\\\"b\\\";s:15:\\\"create accounts\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:3;}}i:6;a:4:{s:1:\\\"a\\\";i:7;s:1:\\\"b\\\";s:13:\\\"edit accounts\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:3;}}i:7;a:4:{s:1:\\\"a\\\";i:8;s:1:\\\"b\\\";s:15:\\\"delete accounts\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:8;a:4:{s:1:\\\"a\\\";i:9;s:1:\\\"b\\\";s:15:\\\"view categories\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:9;a:4:{s:1:\\\"a\\\";i:10;s:1:\\\"b\\\";s:17:\\\"create categories\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:3;}}i:10;a:4:{s:1:\\\"a\\\";i:11;s:1:\\\"b\\\";s:15:\\\"edit categories\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:3;}}i:11;a:4:{s:1:\\\"a\\\";i:12;s:1:\\\"b\\\";s:17:\\\"delete categories\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:12;a:4:{s:1:\\\"a\\\";i:13;s:1:\\\"b\\\";s:9:\\\"view tags\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:13;a:4:{s:1:\\\"a\\\";i:14;s:1:\\\"b\\\";s:11:\\\"create tags\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:3;}}i:14;a:4:{s:1:\\\"a\\\";i:15;s:1:\\\"b\\\";s:9:\\\"edit tags\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:3;}}i:15;a:4:{s:1:\\\"a\\\";i:16;s:1:\\\"b\\\";s:11:\\\"delete tags\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:16;a:4:{s:1:\\\"a\\\";i:17;s:1:\\\"b\\\";s:10:\\\"view users\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:17;a:4:{s:1:\\\"a\\\";i:18;s:1:\\\"b\\\";s:12:\\\"create users\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:18;a:4:{s:1:\\\"a\\\";i:19;s:1:\\\"b\\\";s:10:\\\"edit users\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:19;a:4:{s:1:\\\"a\\\";i:20;s:1:\\\"b\\\";s:12:\\\"delete users\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:20;a:4:{s:1:\\\"a\\\";i:21;s:1:\\\"b\\\";s:10:\\\"view roles\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:21;a:4:{s:1:\\\"a\\\";i:22;s:1:\\\"b\\\";s:12:\\\"create roles\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:22;a:4:{s:1:\\\"a\\\";i:23;s:1:\\\"b\\\";s:10:\\\"edit roles\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:23;a:4:{s:1:\\\"a\\\";i:24;s:1:\\\"b\\\";s:12:\\\"delete roles\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:24;a:4:{s:1:\\\"a\\\";i:25;s:1:\\\"b\\\";s:16:\\\"view permissions\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:25;a:4:{s:1:\\\"a\\\";i:26;s:1:\\\"b\\\";s:18:\\\"create permissions\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:26;a:4:{s:1:\\\"a\\\";i:27;s:1:\\\"b\\\";s:16:\\\"edit permissions\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:27;a:4:{s:1:\\\"a\\\";i:28;s:1:\\\"b\\\";s:18:\\\"delete permissions\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:28;a:4:{s:1:\\\"a\\\";i:29;s:1:\\\"b\\\";s:12:\\\"assign roles\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:29;a:4:{s:1:\\\"a\\\";i:30;s:1:\\\"b\\\";s:13:\\\"view settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:30;a:4:{s:1:\\\"a\\\";i:31;s:1:\\\"b\\\";s:13:\\\"edit settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}}s:5:\\\"roles\\\";a:4:{i:0;a:3:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:11:\\\"super-admin\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:1;a:3:{s:1:\\\"a\\\";i:3;s:1:\\\"b\\\";s:7:\\\"manager\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:2;a:3:{s:1:\\\"a\\\";i:4;s:1:\\\"b\\\";s:4:\\\"user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:3;a:3:{s:1:\\\"a\\\";i:5;s:1:\\\"b\\\";s:6:\\\"viewer\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}}}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1748336021, "laravel_cache_spatie.permission.cache", "a:3:{s:5:\"alias\";a:4:{s:1:\"a\";s:2:\"id\";s:1:\"b\";s:4:\"name\";s:1:\"c\";s:10:\"guard_name\";s:1:\"r\";s:5:\"roles\";}s:11:\"permissions\";a:31:{i:0;a:4:{s:1:\"a\";i:1;s:1:\"b\";s:17:\"view transactions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:1;a:4:{s:1:\"a\";i:2;s:1:\"b\";s:19:\"create transactions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:3;i:2;i:4;}}i:2;a:4:{s:1:\"a\";i:3;s:1:\"b\";s:17:\"edit transactions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:3;i:2;i:4;}}i:3;a:4:{s:1:\"a\";i:4;s:1:\"b\";s:19:\"delete transactions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:4;a:4:{s:1:\"a\";i:5;s:1:\"b\";s:13:\"view accounts\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:5;a:4:{s:1:\"a\";i:6;s:1:\"b\";s:15:\"create accounts\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:3;}}i:6;a:4:{s:1:\"a\";i:7;s:1:\"b\";s:13:\"edit accounts\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:3;}}i:7;a:4:{s:1:\"a\";i:8;s:1:\"b\";s:15:\"delete accounts\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:8;a:4:{s:1:\"a\";i:9;s:1:\"b\";s:15:\"view categories\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:9;a:4:{s:1:\"a\";i:10;s:1:\"b\";s:17:\"create categories\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:3;}}i:10;a:4:{s:1:\"a\";i:11;s:1:\"b\";s:15:\"edit categories\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:3;}}i:11;a:4:{s:1:\"a\";i:12;s:1:\"b\";s:17:\"delete categories\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:12;a:4:{s:1:\"a\";i:13;s:1:\"b\";s:9:\"view tags\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:13;a:4:{s:1:\"a\";i:14;s:1:\"b\";s:11:\"create tags\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:3;}}i:14;a:4:{s:1:\"a\";i:15;s:1:\"b\";s:9:\"edit tags\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:3;}}i:15;a:4:{s:1:\"a\";i:16;s:1:\"b\";s:11:\"delete tags\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:16;a:4:{s:1:\"a\";i:17;s:1:\"b\";s:10:\"view users\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:17;a:4:{s:1:\"a\";i:18;s:1:\"b\";s:12:\"create users\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:18;a:4:{s:1:\"a\";i:19;s:1:\"b\";s:10:\"edit users\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:19;a:4:{s:1:\"a\";i:20;s:1:\"b\";s:12:\"delete users\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:20;a:4:{s:1:\"a\";i:21;s:1:\"b\";s:10:\"view roles\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:21;a:4:{s:1:\"a\";i:22;s:1:\"b\";s:12:\"create roles\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:22;a:4:{s:1:\"a\";i:23;s:1:\"b\";s:10:\"edit roles\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:23;a:4:{s:1:\"a\";i:24;s:1:\"b\";s:12:\"delete roles\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:24;a:4:{s:1:\"a\";i:25;s:1:\"b\";s:16:\"view permissions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:25;a:4:{s:1:\"a\";i:26;s:1:\"b\";s:18:\"create permissions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:26;a:4:{s:1:\"a\";i:27;s:1:\"b\";s:16:\"edit permissions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:27;a:4:{s:1:\"a\";i:28;s:1:\"b\";s:18:\"delete permissions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:28;a:4:{s:1:\"a\";i:29;s:1:\"b\";s:12:\"assign roles\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:29;a:4:{s:1:\"a\";i:30;s:1:\"b\";s:13:\"view settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:30;a:4:{s:1:\"a\";i:31;s:1:\"b\";s:13:\"edit settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}}s:5:\"roles\";a:4:{i:0;a:3:{s:1:\"a\";i:1;s:1:\"b\";s:11:\"super-admin\";s:1:\"c\";s:3:\"web\";}i:1;a:3:{s:1:\"a\";i:3;s:1:\"b\";s:7:\"manager\";s:1:\"c\";s:3:\"web\";}i:2;a:3:{s:1:\"a\";i:4;s:1:\"b\";s:4:\"user\";s:1:\"c\";s:3:\"web\";}i:3;a:3:{s:1:\"a\";i:5;s:1:\"b\";s:6:\"viewer\";s:1:\"c\";s:3:\"web\";}}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 166}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.5934591, "duration": 0.00786, "duration_str": "7.86ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:189", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=189", "ajax": false, "filename": "DatabaseStore.php", "line": "189"}, "connection": "daily", "explain": null, "start_percent": 39.221, "width_percent": 40.831}, {"sql": "select `id`, `name` from `accounts` where `accounts`.`user_id` = 1 and `accounts`.`user_id` is not null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 148}, {"index": 17, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 112}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.645024, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "TransactionForm.php:148", "source": {"index": 16, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=148", "ajax": false, "filename": "TransactionForm.php", "line": "148"}, "connection": "daily", "explain": null, "start_percent": 80.052, "width_percent": 3.74}, {"sql": "select `id`, `name`, `parent_id` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null and `parent_id` is null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 163}, {"index": 17, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 113}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.649027, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "TransactionForm.php:163", "source": {"index": 16, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=163", "ajax": false, "filename": "TransactionForm.php", "line": "163"}, "connection": "daily", "explain": null, "start_percent": 83.792, "width_percent": 4.26}, {"sql": "select * from `categories` where `categories`.`parent_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 163}, {"index": 22, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 113}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.652322, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "TransactionForm.php:163", "source": {"index": 21, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=163", "ajax": false, "filename": "TransactionForm.php", "line": "163"}, "connection": "daily", "explain": null, "start_percent": 88.052, "width_percent": 3.688}, {"sql": "select `id`, `name` from `tags` where `tags`.`user_id` = 1 and `tags`.`user_id` is not null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 202}, {"index": 18, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 115}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.663302, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "TransactionForm.php:202", "source": {"index": 17, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 202}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=202", "ajax": false, "filename": "TransactionForm.php", "line": "202"}, "connection": "daily", "explain": null, "start_percent": 91.74, "width_percent": 3.948}, {"sql": "select `id`, `name` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null and `parent_id` is null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/CategoryForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryForm.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/helpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\helpers.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportComputed/BaseComputed.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\BaseComputed.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportComputed/BaseComputed.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\BaseComputed.php", "line": 60}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/EventBus.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\EventBus.php", "line": 60}], "start": 1748249629.3458831, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "CategoryForm.php:74", "source": {"index": 16, "namespace": null, "name": "app/Livewire/CategoryForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryForm.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryForm.php&line=74", "ajax": false, "filename": "CategoryForm.php", "line": "74"}, "connection": "daily", "explain": null, "start_percent": 95.688, "width_percent": 4.312}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 56, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Category": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 31, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Account": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FAccount.php&line=1", "ajax": false, "filename": "Account.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 133, "is_counter": true}, "livewire": {"data": {"transaction-form #Q2wpunQ8yCtTwNMnBsBt": "array:4 [\n  \"data\" => array:20 [\n    \"account\" => null\n    \"transaction\" => null\n    \"accounts\" => Illuminate\\Database\\Eloquent\\Collection {#1887\n      #items: array:5 [\n        0 => App\\Models\\Account {#1735\n          #connection: \"mysql\"\n          #table: \"accounts\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 4\n            \"name\" => \"Credit Card\"\n          ]\n          #original: array:2 [\n            \"id\" => 4\n            \"name\" => \"Credit Card\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"type\" => \"App\\Enums\\AccountType\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"user_id\"\n            1 => \"type\"\n            2 => \"name\"\n            3 => \"slug\"\n            4 => \"balance\"\n            5 => \"initial_balance\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        1 => App\\Models\\Account {#1864\n          #connection: \"mysql\"\n          #table: \"accounts\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 3\n            \"name\" => \"Investment\"\n          ]\n          #original: array:2 [\n            \"id\" => 3\n            \"name\" => \"Investment\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"type\" => \"App\\Enums\\AccountType\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"user_id\"\n            1 => \"type\"\n            2 => \"name\"\n            3 => \"slug\"\n            4 => \"balance\"\n            5 => \"initial_balance\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        2 => App\\Models\\Account {#1831\n          #connection: \"mysql\"\n          #table: \"accounts\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 1\n            \"name\" => \"Loan\"\n          ]\n          #original: array:2 [\n            \"id\" => 1\n            \"name\" => \"Loan\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"type\" => \"App\\Enums\\AccountType\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"user_id\"\n            1 => \"type\"\n            2 => \"name\"\n            3 => \"slug\"\n            4 => \"balance\"\n            5 => \"initial_balance\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        3 => App\\Models\\Account {#1766\n          #connection: \"mysql\"\n          #table: \"accounts\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 2\n            \"name\" => \"Loan\"\n          ]\n          #original: array:2 [\n            \"id\" => 2\n            \"name\" => \"Loan\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"type\" => \"App\\Enums\\AccountType\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"user_id\"\n            1 => \"type\"\n            2 => \"name\"\n            3 => \"slug\"\n            4 => \"balance\"\n            5 => \"initial_balance\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        4 => App\\Models\\Account {#1706\n          #connection: \"mysql\"\n          #table: \"accounts\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 5\n            \"name\" => \"Tanvir Hossen Bappy\"\n          ]\n          #original: array:2 [\n            \"id\" => 5\n            \"name\" => \"Tanvir Hossen Bappy\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"type\" => \"App\\Enums\\AccountType\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"user_id\"\n            1 => \"type\"\n            2 => \"name\"\n            3 => \"slug\"\n            4 => \"balance\"\n            5 => \"initial_balance\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"categories\" => array:10 [\n      0 => array:4 [\n        \"id\" => 1\n        \"name\" => \"Auto & Transport 2\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 11\n            \"user_id\" => 1\n            \"name\" => \"Car Insurance\"\n            \"parent_id\" => 1\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 12\n            \"user_id\" => 1\n            \"name\" => \"Car Payment\"\n            \"parent_id\" => 1\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      1 => array:4 [\n        \"id\" => 2\n        \"name\" => \"Food\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 13\n            \"user_id\" => 1\n            \"name\" => \"Fast Food\"\n            \"parent_id\" => 2\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 14\n            \"user_id\" => 1\n            \"name\" => \"Restaurants\"\n            \"parent_id\" => 2\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      2 => array:4 [\n        \"id\" => 4\n        \"name\" => \"Health\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 17\n            \"user_id\" => 1\n            \"name\" => \"Doctor\"\n            \"parent_id\" => 4\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 18\n            \"user_id\" => 1\n            \"name\" => \"Pharmacy\"\n            \"parent_id\" => 4\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      3 => array:4 [\n        \"id\" => 3\n        \"name\" => \"Home\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 15\n            \"user_id\" => 1\n            \"name\" => \"Mortgage\"\n            \"parent_id\" => 3\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 16\n            \"user_id\" => 1\n            \"name\" => \"Rent\"\n            \"parent_id\" => 3\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      4 => array:4 [\n        \"id\" => 5\n        \"name\" => \"Personal Care\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 19\n            \"user_id\" => 1\n            \"name\" => \"Haircut\"\n            \"parent_id\" => 5\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 20\n            \"user_id\" => 1\n            \"name\" => \"Laundry\"\n            \"parent_id\" => 5\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      5 => array:4 [\n        \"id\" => 6\n        \"name\" => \"Personal Income\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 22\n            \"user_id\" => 1\n            \"name\" => \"Bonus\"\n            \"parent_id\" => 6\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 21\n            \"user_id\" => 1\n            \"name\" => \"Paycheck\"\n            \"parent_id\" => 6\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      6 => array:4 [\n        \"id\" => 7\n        \"name\" => \"Pets\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 23\n            \"user_id\" => 1\n            \"name\" => \"Pet Food\"\n            \"parent_id\" => 7\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 24\n            \"user_id\" => 1\n            \"name\" => \"Veterinary\"\n            \"parent_id\" => 7\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      7 => array:4 [\n        \"id\" => 8\n        \"name\" => \"Shopping\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 25\n            \"user_id\" => 1\n            \"name\" => \"Clothing\"\n            \"parent_id\" => 8\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 26\n            \"user_id\" => 1\n            \"name\" => \"Gifts\"\n            \"parent_id\" => 8\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      8 => array:4 [\n        \"id\" => 9\n        \"name\" => \"Travel\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 28\n            \"user_id\" => 1\n            \"name\" => \"Airfare\"\n            \"parent_id\" => 9\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 27\n            \"user_id\" => 1\n            \"name\" => \"Hotel\"\n            \"parent_id\" => 9\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      9 => array:4 [\n        \"id\" => 10\n        \"name\" => \"Utilities\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 30\n            \"user_id\" => 1\n            \"name\" => \"Electric\"\n            \"parent_id\" => 10\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 29\n            \"user_id\" => 1\n            \"name\" => \"Gas\"\n            \"parent_id\" => 10\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n    ]\n    \"account_id\" => null\n    \"transfer_to\" => null\n    \"payee\" => \"\"\n    \"transaction_types\" => array:5 [\n      0 => App\\Enums\\TransactionType {#1802\n        +name: \"CREDIT\"\n        +value: \"credit\"\n      }\n      1 => App\\Enums\\TransactionType {#1826\n        +name: \"DEBIT\"\n        +value: \"debit\"\n      }\n      2 => App\\Enums\\TransactionType {#1914\n        +name: \"DEPOSIT\"\n        +value: \"deposit\"\n      }\n      3 => App\\Enums\\TransactionType {#1948\n        +name: \"TRANSFER\"\n        +value: \"transfer\"\n      }\n      4 => App\\Enums\\TransactionType {#1950\n        +name: \"WITHDRAWAL\"\n        +value: \"withdrawal\"\n      }\n    ]\n    \"type\" => null\n    \"amount\" => null\n    \"category_id\" => null\n    \"date\" => Illuminate\\Support\\Carbon @********** {#1911\n      #endOfTime: false\n      #startOfTime: false\n      #constructedObjectId: \"00000000000007770000000000000000\"\n      -clock: null\n      #localMonthsOverflow: null\n      #localYearsOverflow: null\n      #localStrictModeEnabled: null\n      #localHumanDiffOptions: null\n      #localToStringFormat: null\n      #localSerializer: null\n      #localMacros: null\n      #localGenericMacros: null\n      #localFormatFunction: null\n      #localTranslator: null\n      #dumpProperties: array:3 [\n        0 => \"date\"\n        1 => \"timezone_type\"\n        2 => \"timezone\"\n      ]\n      #dumpLocale: null\n      #dumpDateProperties: null\n      date: 2025-05-26 00:00:00.0 America/Chicago (-05:00)\n    }\n    \"user_tags\" => array:3 [\n      0 => \"Bills\"\n      1 => \"Entertainment\"\n      2 => \"Groceries\"\n    ]\n    \"tags\" => []\n    \"notes\" => null\n    \"attachments\" => []\n    \"status\" => false\n    \"is_recurring\" => false\n    \"frequency\" => null\n    \"recurring_end\" => null\n  ]\n  \"name\" => \"transaction-form\"\n  \"component\" => \"App\\Livewire\\TransactionForm\"\n  \"id\" => \"Q2wpunQ8yCtTwNMnBsBt\"\n]", "category-form #5SIRMN6q7PGATOPZtzxA": "array:4 [\n  \"data\" => array:4 [\n    \"show_category_form\" => false\n    \"category\" => null\n    \"parent_id\" => null\n    \"name\" => \"\"\n  ]\n  \"name\" => \"category-form\"\n  \"component\" => \"App\\Livewire\\CategoryForm\"\n  \"id\" => \"5SIRMN6q7PGATOPZtzxA\"\n]", "tag-form #7kj7lAcV8ygfbxB9qErK": "array:4 [\n  \"data\" => array:3 [\n    \"show_tag_form\" => false\n    \"tag\" => null\n    \"name\" => \"\"\n  ]\n  \"name\" => \"tag-form\"\n  \"component\" => \"App\\Livewire\\TagForm\"\n  \"id\" => \"7kj7lAcV8ygfbxB9qErK\"\n]", "file-uploader #zKG8YoVZNcOu4Wu9EJ5F": "array:4 [\n  \"data\" => array:6 [\n    \"input_uuid\" => \"\"\n    \"files\" => null\n    \"uploaded_files\" => Illuminate\\Support\\Collection {#2368\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n    \"selected_file\" => \"\"\n    \"s3_path\" => \"files\"\n    \"disabled\" => false\n  ]\n  \"name\" => \"file-uploader\"\n  \"component\" => \"App\\Livewire\\FileUploader\"\n  \"id\" => \"zKG8YoVZNcOu4Wu9EJ5F\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => create,\n  target => App\\Models\\Transaction,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Transaction]\n]", "message_html": "<pre class=sf-dump id=sf-dump-974768128 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Transaction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Transaction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Transaction]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-974768128\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.631926, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/transaction-form", "action_name": "create-transaction", "controller_action": "App\\Livewire\\TransactionForm", "uri": "GET transaction-form", "controller": "App\\Livewire\\TransactionForm@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=306\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=306\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/TransactionForm.php:306-309</a>", "middleware": "web, auth, can:create,App\\Models\\Transaction", "duration": "13.17s", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1612366557 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1612366557\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1315643474 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1315643474\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-836180263 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire-navigate</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/transactions</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlpzaWFJRDZWRkdqV2xWSk9KU0hhd1E9PSIsInZhbHVlIjoid29aOThmTWVER0pBWVovL1F1S0hhT2NIQ3dONUsvMmpzYVp3NTNZc1B3WTVMZVFhM202SCtDdHI5WWphQ1RBbVpXbXFrZTQ1c1ppTjRlbDhrdmYvZjBoVHNKamNwcytRYUNjcTA4Q2lRZHJaaFlFUkpOdGMyTWpmNlk0SlFTRHkiLCJtYWMiOiJjMjk2MTFhNTVmODBlMWE2ZGM2NWUwYTk1MGQ5NWM2YjM2ZmJkYzYyMTdlZGEwMzE2OGQ3Y2Q5MDk3Y2MxMThjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZwVit5UmpjVEFJb3A1OU9UdkRsYlE9PSIsInZhbHVlIjoiWHV3aUxWVzdpZDlobmFOMWdNNzZrR0hsd29vTldZWlBFMldBeGx4c0ZXOHBsa0JQem1tUDdNckVBVmZsNGtLVURMa1VITkVBWWtaNGFzWFd1SjBwYXNJV05aOWthRW0yajk1Mnl1Yms0bSsrSk5lbkVaZWxXUjRVaHVXdHI3dEEiLCJtYWMiOiI0ZTI3YThmYzk1NDY5MDFiNTJkNGEzYWRlY2U1YTZkN2E0ZWFiNzZmMTA4OTBlMGFjNGVkZDk5YzQ4MTQ0MjM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836180263\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-161973835 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-161973835\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-200942031 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 26 May 2025 08:53:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-200942031\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-777336545 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/transactions</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777336545\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/transaction-form", "action_name": "create-transaction", "controller_action": "App\\Livewire\\TransactionForm"}, "badge": null}}