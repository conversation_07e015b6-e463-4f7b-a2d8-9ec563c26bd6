[2025-05-25 08:42:01] production.ERROR: rename(C:\laragon\www\pure-finance\bootstrap\cache\pac1E5E.tmp,C:\laragon\www\pure-finance\bootstrap\cache/packages.php): Access is denied (code: 5) {"exception":"[object] (ErrorException(code: 0): rename(C:\\laragon\\www\\pure-finance\\bootstrap\\cache\\pac1E5E.tmp,C:\\laragon\\www\\pure-finance\\bootstrap\\cache/packages.php): Access is denied (code: 5) at C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:233)
[stacktrace]
#0 C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'rename(C:\\\\larag...', 'C:\\\\laragon\\\\www\\\\...', 233)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'rename(C:\\\\larag...', 'C:\\\\laragon\\\\www\\\\...', 233)
#2 C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(233): rename('C:\\\\laragon\\\\www\\\\...', 'C:\\\\laragon\\\\www\\\\...')
#3 C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(182): Illuminate\\Filesystem\\Filesystem->replace('C:\\\\laragon\\\\www\\\\...', '<?php return ar...')
#4 C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write(Array)
#5 C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\laragon\\www\\pure-finance\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#14 {main}
"} 
[2025-05-25 08:42:01] production.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: rename(C:\laragon\www\pure-finance\bootstrap\cache\pac1E6D.tmp,C:\laragon\www\pure-finance\bootstrap\cache/packages.php): Access is denied (code: 5) {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: rename(C:\\laragon\\www\\pure-finance\\bootstrap\\cache\\pac1E6D.tmp,C:\\laragon\\www\\pure-finance\\bootstrap\\cache/packages.php): Access is denied (code: 5) at C:\\laragon\\www\\pure-finance\\vendor\\_laravel_ide\\discover-77e9e6a2445455b87d4f4cd834e4e23d.php:33)
[stacktrace]
#0 C:\\laragon\\www\\pure-finance\\vendor\\_laravel_ide\\discover-77e9e6a2445455b87d4f4cd834e4e23d.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 
[2025-05-25 08:42:01] production.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: rename(C:\laragon\www\pure-finance\bootstrap\cache\ser1F29.tmp,C:\laragon\www\pure-finance\bootstrap\cache/services.php): Access is denied (code: 5) {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: rename(C:\\laragon\\www\\pure-finance\\bootstrap\\cache\\ser1F29.tmp,C:\\laragon\\www\\pure-finance\\bootstrap\\cache/services.php): Access is denied (code: 5) at C:\\laragon\\www\\pure-finance\\vendor\\_laravel_ide\\discover-31b934f114eabc246b541addb8b458e3.php:33)
[stacktrace]
#0 C:\\laragon\\www\\pure-finance\\vendor\\_laravel_ide\\discover-31b934f114eabc246b541addb8b458e3.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 
[2025-05-25 08:42:01] production.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: rename(C:\laragon\www\pure-finance\bootstrap\cache\ser1FA7.tmp,C:\laragon\www\pure-finance\bootstrap\cache\services.php): Access is denied (code: 5) {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: rename(C:\\laragon\\www\\pure-finance\\bootstrap\\cache\\ser1FA7.tmp,C:\\laragon\\www\\pure-finance\\bootstrap\\cache\\services.php): Access is denied (code: 5) at C:\\laragon\\www\\pure-finance\\vendor\\_laravel_ide\\discover-ea280fb1636eb761c4fd23f76666e397.php:33)
[stacktrace]
#0 C:\\laragon\\www\\pure-finance\\vendor\\_laravel_ide\\discover-ea280fb1636eb761c4fd23f76666e397.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 
[2025-05-25 08:42:01] production.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: rename(C:\laragon\www\pure-finance\bootstrap\cache\ser1F86.tmp,C:\laragon\www\pure-finance\bootstrap\cache\services.php): Access is denied (code: 5) {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: rename(C:\\laragon\\www\\pure-finance\\bootstrap\\cache\\ser1F86.tmp,C:\\laragon\\www\\pure-finance\\bootstrap\\cache\\services.php): Access is denied (code: 5) at C:\\laragon\\www\\pure-finance\\vendor\\_laravel_ide\\discover-1e7ea3cf42a2f9b84f3de09530e2c135.php:33)
[stacktrace]
#0 C:\\laragon\\www\\pure-finance\\vendor\\_laravel_ide\\discover-1e7ea3cf42a2f9b84f3de09530e2c135.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 
