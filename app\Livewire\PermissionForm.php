<?php

declare(strict_types=1);

namespace App\Livewire;

use Flux\Flux;
use Livewire\Component;
use Illuminate\Contracts\View\View;
use Spatie\Permission\Models\Permission;

class PermissionForm extends Component
{
    public bool $show_permission_form = false;

    public ?Permission $permission = null;

    public string $name = '';

    public string $guard_name = 'web';

    protected function rules(): array
    {
        return [
            'name' => [
                'required_if:show_permission_form,true',
                'string',
                'max:255',
                'unique:permissions,name' . ($this->permission ? ','. $this->permission->id : ''),
            ],
            'guard_name' => ['required_if:show_permission_form,true', 'string', 'max:255'],
        ];
    }

    protected function messages(): array
    {
        return [
            'name.unique' => 'The permission name has already been taken.',
        ];
    }

    public function mount(): void
    {
        if ($this->permission) {
            $this->name = $this->permission->name;
            $this->guard_name = $this->permission->guard_name;
        }
    }

    public function submit(): void
    {
        $validated_data = $this->validate();

        if ($this->permission) {
            $this->permission->update($validated_data);
        } else {
            Permission::create($validated_data);
        }

        $this->dispatch('permission-saved');

        if (!$this->permission) {
            $this->reset(['name', 'guard_name']);
        }

        Flux::toast(
            variant: 'success',
            text: 'Permission successfully ' . ($this->permission ? 'updated' : 'created'),
        );

        Flux::modals()->close();
    }

    public function render(): View
    {
        return view('livewire.permission-form');
    }
}
