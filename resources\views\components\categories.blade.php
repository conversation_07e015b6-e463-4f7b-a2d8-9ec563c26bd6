@props(['categories', 'disabled' => null])

<flux:field>
    <flux:label @class([
        'opacity-50' => $disabled
    ])>
        Category
    </flux:label>

    <div class="space-y-2">
        <x-select placeholder="Select a category" wire:model='category_id'>
            @foreach ($categories as $category)
                <x-option value="{{ $category['id'] }}" class="font-semibold">
                    {{ $category['name'] }}
                </x-option>

                @foreach($category['children'] as $child)
                    <x-option value="{{ $child['id'] }}">
                        &nbsp;&nbsp;&nbsp;&nbsp;{{ $child['name'] }}
                    </x-option>
                @endforeach
            @endforeach
        </x-select>

        <!-- Add category button -->
        <div class="flex justify-end">
            <flux:modal.trigger name="category-form">
                <flux:button square variant="subtle" size="sm" aria-label="Add new category">
                    <flux:icon.plus variant="micro" />
                    <span class="ml-1 text-xs">Add Category</span>
                </flux:button>
            </flux:modal.trigger>
        </div>
    </div>

    <flux:error name="category_id" />

    <div class="absolute">
        <livewire:category-form />
    </div>
</flux:field>