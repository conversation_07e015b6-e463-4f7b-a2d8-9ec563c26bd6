{"__meta": {"id": "01JW3ERPMC6Z9FB1KZJHM3HH12", "datetime": "2025-05-25 10:10:42", "utime": **********.445093, "method": "GET", "uri": "/account-overview/tanvir-hossen-bappy", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.685084, "end": **********.445106, "duration": 2.***************, "duration_str": "2.76s", "measures": [{"label": "Booting", "start": **********.685084, "relative_start": 0, "end": **********.972789, "relative_end": **********.972789, "duration": 0.****************, "duration_str": "288ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.972802, "relative_start": 0.****************, "end": **********.445108, "relative_end": 1.9073486328125e-06, "duration": 2.*************, "duration_str": "2.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.995012, "relative_start": 0.*****************, "end": **********.999213, "relative_end": **********.999213, "duration": 0.004200935363769531, "duration_str": "4.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.account-overview", "start": **********.09611, "relative_start": 0.****************, "end": **********.09611, "relative_end": **********.09611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.728036, "relative_start": 1.****************, "end": **********.728036, "relative_end": **********.728036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.729637, "relative_start": 1.****************, "end": **********.729637, "relative_end": **********.729637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.733396, "relative_start": 1.0483119487762451, "end": **********.733396, "relative_end": **********.733396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.pencil-square", "start": **********.73492, "relative_start": 1.0498359203338623, "end": **********.73492, "relative_end": **********.73492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.735974, "relative_start": 1.0508899688720703, "end": **********.735974, "relative_end": **********.735974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.736972, "relative_start": 1.0518879890441895, "end": **********.736972, "relative_end": **********.736972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.trigger", "start": **********.737925, "relative_start": 1.****************, "end": **********.737925, "relative_end": **********.737925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.account-form", "start": **********.743061, "relative_start": 1.****************, "end": **********.743061, "relative_end": **********.743061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.745263, "relative_start": 1.****************, "end": **********.745263, "relative_end": **********.745263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.746726, "relative_start": 1.****************, "end": **********.746726, "relative_end": **********.746726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.747741, "relative_start": 1.0626568794250488, "end": **********.747741, "relative_end": **********.747741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.750503, "relative_start": 1.0654189586639404, "end": **********.750503, "relative_end": **********.750503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.751912, "relative_start": 1.0668280124664307, "end": **********.751912, "relative_end": **********.751912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.7528, "relative_start": 1.0677158832550049, "end": **********.7528, "relative_end": **********.7528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.753757, "relative_start": 1.0686728954315186, "end": **********.753757, "relative_end": **********.753757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.754536, "relative_start": 1.0694518089294434, "end": **********.754536, "relative_end": **********.754536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.755955, "relative_start": 1.0708708763122559, "end": **********.755955, "relative_end": **********.755955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.756627, "relative_start": 1.0715429782867432, "end": **********.756627, "relative_end": **********.756627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.75713, "relative_start": 1.0720458030700684, "end": **********.75713, "relative_end": **********.75713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.757579, "relative_start": 1.0724949836730957, "end": **********.757579, "relative_end": **********.757579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.757996, "relative_start": 1.0729119777679443, "end": **********.757996, "relative_end": **********.757996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.758702, "relative_start": 1.073617935180664, "end": **********.758702, "relative_end": **********.758702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.759317, "relative_start": 1.074232816696167, "end": **********.759317, "relative_end": **********.759317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.760057, "relative_start": 1.0749728679656982, "end": **********.760057, "relative_end": **********.760057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.760646, "relative_start": 1.0755620002746582, "end": **********.760646, "relative_end": **********.760646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.765642, "relative_start": 1.0805578231811523, "end": **********.765642, "relative_end": **********.765642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.766641, "relative_start": 1.081556797027588, "end": **********.766641, "relative_end": **********.766641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.767297, "relative_start": 1.0822129249572754, "end": **********.767297, "relative_end": **********.767297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.767878, "relative_start": 1.082793951034546, "end": **********.767878, "relative_end": **********.767878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.769284, "relative_start": 1.0841999053955078, "end": **********.769284, "relative_end": **********.769284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.770396, "relative_start": 1.0853118896484375, "end": **********.770396, "relative_end": **********.770396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.trigger", "start": **********.771044, "relative_start": 1.0859599113464355, "end": **********.771044, "relative_end": **********.771044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.delete-modal", "start": **********.772129, "relative_start": 1.0870449542999268, "end": **********.772129, "relative_end": **********.772129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.644505, "relative_start": 1.959420919418335, "end": **********.644505, "relative_end": **********.644505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::text", "start": **********.645286, "relative_start": 1.9602019786834717, "end": **********.645286, "relative_end": **********.645286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.736899, "relative_start": 2.0518147945404053, "end": **********.736899, "relative_end": **********.736899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.737387, "relative_start": 2.052302837371826, "end": **********.737387, "relative_end": **********.737387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.739505, "relative_start": 2.0544209480285645, "end": **********.739505, "relative_end": **********.739505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.740119, "relative_start": 2.055034875869751, "end": **********.740119, "relative_end": **********.740119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.740683, "relative_start": 2.0555989742279053, "end": **********.740683, "relative_end": **********.740683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.741204, "relative_start": 2.056119918823242, "end": **********.741204, "relative_end": **********.741204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.743284, "relative_start": 2.058199882507324, "end": **********.743284, "relative_end": **********.743284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.744545, "relative_start": 2.0594608783721924, "end": **********.744545, "relative_end": **********.744545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.745455, "relative_start": 2.060370922088623, "end": **********.745455, "relative_end": **********.745455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.746452, "relative_start": 2.061367988586426, "end": **********.746452, "relative_end": **********.746452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": **********.747406, "relative_start": 2.062321901321411, "end": **********.747406, "relative_end": **********.747406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.748938, "relative_start": 2.0638539791107178, "end": **********.748938, "relative_end": **********.748938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.750261, "relative_start": 2.0651769638061523, "end": **********.750261, "relative_end": **********.750261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.751079, "relative_start": 2.06599497795105, "end": **********.751079, "relative_end": **********.751079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.751804, "relative_start": 2.0667200088500977, "end": **********.751804, "relative_end": **********.751804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.75248, "relative_start": 2.0673959255218506, "end": **********.75248, "relative_end": **********.75248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.753179, "relative_start": 2.0680949687957764, "end": **********.753179, "relative_end": **********.753179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.753627, "relative_start": 2.068542957305908, "end": **********.753627, "relative_end": **********.753627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.755812, "relative_start": 2.070727825164795, "end": **********.755812, "relative_end": **********.755812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.756632, "relative_start": 2.0715479850769043, "end": **********.756632, "relative_end": **********.756632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.757281, "relative_start": 2.0721969604492188, "end": **********.757281, "relative_end": **********.757281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.757689, "relative_start": 2.0726048946380615, "end": **********.757689, "relative_end": **********.757689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.759328, "relative_start": 2.0742437839508057, "end": **********.759328, "relative_end": **********.759328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.760054, "relative_start": 2.074970006942749, "end": **********.760054, "relative_end": **********.760054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.760696, "relative_start": 2.0756118297576904, "end": **********.760696, "relative_end": **********.760696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.761387, "relative_start": 2.076303005218506, "end": **********.761387, "relative_end": **********.761387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": **********.762083, "relative_start": 2.0769989490509033, "end": **********.762083, "relative_end": **********.762083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.762996, "relative_start": 2.077911853790283, "end": **********.762996, "relative_end": **********.762996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.764312, "relative_start": 2.079227924346924, "end": **********.764312, "relative_end": **********.764312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.764839, "relative_start": 2.0797548294067383, "end": **********.764839, "relative_end": **********.764839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.765321, "relative_start": 2.0802369117736816, "end": **********.765321, "relative_end": **********.765321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.766079, "relative_start": 2.0809948444366455, "end": **********.766079, "relative_end": **********.766079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.76664, "relative_start": 2.0815558433532715, "end": **********.76664, "relative_end": **********.76664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.779585, "relative_start": 2.094500780105591, "end": **********.779585, "relative_end": **********.779585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.780873, "relative_start": 2.0957889556884766, "end": **********.780873, "relative_end": **********.780873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.781698, "relative_start": 2.096613883972168, "end": **********.781698, "relative_end": **********.781698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.transaction-table", "start": **********.827403, "relative_start": 2.1423189640045166, "end": **********.827403, "relative_end": **********.827403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.880436, "relative_start": 2.1953518390655518, "end": **********.880436, "relative_end": **********.880436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.88705, "relative_start": 2.201965808868408, "end": **********.88705, "relative_end": **********.88705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.890575, "relative_start": 2.205490827560425, "end": **********.890575, "relative_end": **********.890575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.plus", "start": **********.892227, "relative_start": 2.2071428298950195, "end": **********.892227, "relative_end": **********.892227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.893665, "relative_start": 2.20858097076416, "end": **********.893665, "relative_end": **********.893665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.895824, "relative_start": 2.210739850997925, "end": **********.895824, "relative_end": **********.895824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.897276, "relative_start": 2.2121918201446533, "end": **********.897276, "relative_end": **********.897276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.901601, "relative_start": 2.2165169715881348, "end": **********.901601, "relative_end": **********.901601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.magnifying-glass", "start": **********.903297, "relative_start": 2.218212842941284, "end": **********.903297, "relative_end": **********.903297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.907341, "relative_start": 2.222256898880005, "end": **********.907341, "relative_end": **********.907341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.909048, "relative_start": 2.223963975906372, "end": **********.909048, "relative_end": **********.909048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.clearable", "start": **********.910867, "relative_start": 2.225782871246338, "end": **********.910867, "relative_end": **********.910867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.91232, "relative_start": 2.227235794067383, "end": **********.91232, "relative_end": **********.91232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.913259, "relative_start": 2.228174924850464, "end": **********.913259, "relative_end": **********.913259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.915676, "relative_start": 2.2305920124053955, "end": **********.915676, "relative_end": **********.915676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.916688, "relative_start": 2.2316038608551025, "end": **********.916688, "relative_end": **********.916688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.917692, "relative_start": 2.232607841491699, "end": **********.917692, "relative_end": **********.917692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.filters", "start": **********.919187, "relative_start": 2.234102964401245, "end": **********.919187, "relative_end": **********.919187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.923919, "relative_start": 2.238834857940674, "end": **********.923919, "relative_end": **********.923919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.926366, "relative_start": 2.2412819862365723, "end": **********.926366, "relative_end": **********.926366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.funnel", "start": **********.927889, "relative_start": 2.242805004119873, "end": **********.927889, "relative_end": **********.927889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.928854, "relative_start": 2.243769884109497, "end": **********.928854, "relative_end": **********.928854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.92965, "relative_start": 2.244565963745117, "end": **********.92965, "relative_end": **********.92965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.trigger", "start": **********.930595, "relative_start": 2.2455108165740967, "end": **********.930595, "relative_end": **********.930595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.931731, "relative_start": 2.2466468811035156, "end": **********.931731, "relative_end": **********.931731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.932865, "relative_start": 2.2477807998657227, "end": **********.932865, "relative_end": **********.932865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.934953, "relative_start": 2.249868869781494, "end": **********.934953, "relative_end": **********.934953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.935748, "relative_start": 2.250663995742798, "end": **********.935748, "relative_end": **********.935748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.936499, "relative_start": 2.2514150142669678, "end": **********.936499, "relative_end": **********.936499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.939527, "relative_start": 2.2544429302215576, "end": **********.939527, "relative_end": **********.939527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.940694, "relative_start": 2.2556099891662598, "end": **********.940694, "relative_end": **********.940694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.941464, "relative_start": 2.2563798427581787, "end": **********.941464, "relative_end": **********.941464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.943525, "relative_start": 2.2584409713745117, "end": **********.943525, "relative_end": **********.943525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-down", "start": **********.944381, "relative_start": 2.2592968940734863, "end": **********.944381, "relative_end": **********.944381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.94506, "relative_start": 2.2599759101867676, "end": **********.94506, "relative_end": **********.94506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.945625, "relative_start": 2.2605409622192383, "end": **********.945625, "relative_end": **********.945625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.946503, "relative_start": 2.2614188194274902, "end": **********.946503, "relative_end": **********.946503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.default", "start": **********.948043, "relative_start": 2.2629590034484863, "end": **********.948043, "relative_end": **********.948043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.indicator", "start": **********.94963, "relative_start": 2.2645459175109863, "end": **********.94963, "relative_end": **********.94963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.950453, "relative_start": 2.265368938446045, "end": **********.950453, "relative_end": **********.950453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.952181, "relative_start": 2.267096996307373, "end": **********.952181, "relative_end": **********.952181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.953151, "relative_start": 2.268066883087158, "end": **********.953151, "relative_end": **********.953151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.954087, "relative_start": 2.269002914428711, "end": **********.954087, "relative_end": **********.954087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.955613, "relative_start": 2.270528793334961, "end": **********.955613, "relative_end": **********.955613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.default", "start": **********.958014, "relative_start": 2.2729299068450928, "end": **********.958014, "relative_end": **********.958014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.indicator", "start": **********.959408, "relative_start": 2.2743239402770996, "end": **********.959408, "relative_end": **********.959408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.959922, "relative_start": 2.2748379707336426, "end": **********.959922, "relative_end": **********.959922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.960712, "relative_start": 2.275627851486206, "end": **********.960712, "relative_end": **********.960712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.961198, "relative_start": 2.276113986968994, "end": **********.961198, "relative_end": **********.961198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.961799, "relative_start": 2.27671480178833, "end": **********.961799, "relative_end": **********.961799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.index", "start": **********.962328, "relative_start": 2.2772438526153564, "end": **********.962328, "relative_end": **********.962328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.variants.default", "start": **********.963278, "relative_start": 2.278193950653076, "end": **********.963278, "relative_end": **********.963278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.964455, "relative_start": 2.2793707847595215, "end": **********.964455, "relative_end": **********.964455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.96563, "relative_start": 2.280545949935913, "end": **********.96563, "relative_end": **********.96563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.967541, "relative_start": 2.282456874847412, "end": **********.967541, "relative_end": **********.967541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.968191, "relative_start": 2.283106803894043, "end": **********.968191, "relative_end": **********.968191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.968771, "relative_start": 2.283686876296997, "end": **********.968771, "relative_end": **********.968771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.969981, "relative_start": 2.2848968505859375, "end": **********.969981, "relative_end": **********.969981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-down", "start": **********.970486, "relative_start": 2.2854018211364746, "end": **********.970486, "relative_end": **********.970486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.971015, "relative_start": 2.285930871963501, "end": **********.971015, "relative_end": **********.971015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.972539, "relative_start": 2.287454843521118, "end": **********.972539, "relative_end": **********.972539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.974586, "relative_start": 2.289501905441284, "end": **********.974586, "relative_end": **********.974586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.default", "start": **********.976283, "relative_start": 2.291198968887329, "end": **********.976283, "relative_end": **********.976283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.indicator", "start": **********.977067, "relative_start": 2.291982889175415, "end": **********.977067, "relative_end": **********.977067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.977477, "relative_start": 2.2923929691314697, "end": **********.977477, "relative_end": **********.977477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.978232, "relative_start": 2.2931478023529053, "end": **********.978232, "relative_end": **********.978232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.978696, "relative_start": 2.293612003326416, "end": **********.978696, "relative_end": **********.978696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.979205, "relative_start": 2.2941207885742188, "end": **********.979205, "relative_end": **********.979205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.979733, "relative_start": 2.2946488857269287, "end": **********.979733, "relative_end": **********.979733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.default", "start": **********.980587, "relative_start": 2.2955029010772705, "end": **********.980587, "relative_end": **********.980587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.indicator", "start": **********.98117, "relative_start": 2.296085834503174, "end": **********.98117, "relative_end": **********.98117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.981541, "relative_start": 2.296456813812256, "end": **********.981541, "relative_end": **********.981541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.982236, "relative_start": 2.297151803970337, "end": **********.982236, "relative_end": **********.982236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.982681, "relative_start": 2.2975969314575195, "end": **********.982681, "relative_end": **********.982681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.983182, "relative_start": 2.298097848892212, "end": **********.983182, "relative_end": **********.983182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.983708, "relative_start": 2.29862380027771, "end": **********.983708, "relative_end": **********.983708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.default", "start": **********.984951, "relative_start": 2.2998669147491455, "end": **********.984951, "relative_end": **********.984951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.indicator", "start": **********.98592, "relative_start": 2.3008358478546143, "end": **********.98592, "relative_end": **********.98592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.986526, "relative_start": 2.3014419078826904, "end": **********.986526, "relative_end": **********.986526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.987453, "relative_start": 2.3023688793182373, "end": **********.987453, "relative_end": **********.987453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.988943, "relative_start": 2.303858995437622, "end": **********.988943, "relative_end": **********.988943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.990115, "relative_start": 2.3050308227539062, "end": **********.990115, "relative_end": **********.990115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.990716, "relative_start": 2.3056318759918213, "end": **********.990716, "relative_end": **********.990716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.default", "start": **********.991668, "relative_start": 2.306583881378174, "end": **********.991668, "relative_end": **********.991668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.indicator", "start": **********.992425, "relative_start": 2.3073408603668213, "end": **********.992425, "relative_end": **********.992425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.992907, "relative_start": 2.3078229427337646, "end": **********.992907, "relative_end": **********.992907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.994116, "relative_start": 2.3090319633483887, "end": **********.994116, "relative_end": **********.994116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.994908, "relative_start": 2.309823989868164, "end": **********.994908, "relative_end": **********.994908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.995849, "relative_start": 2.310764789581299, "end": **********.995849, "relative_end": **********.995849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.996517, "relative_start": 2.3114328384399414, "end": **********.996517, "relative_end": **********.996517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.default", "start": **********.997427, "relative_start": 2.312342882156372, "end": **********.997427, "relative_end": **********.997427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.indicator", "start": **********.998082, "relative_start": 2.312997817993164, "end": **********.998082, "relative_end": **********.998082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.998566, "relative_start": 2.3134818077087402, "end": **********.998566, "relative_end": **********.998566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.999451, "relative_start": 2.3143668174743652, "end": **********.999451, "relative_end": **********.999451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.000051, "relative_start": 2.314966917037964, "end": **********.000051, "relative_end": **********.000051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.000682, "relative_start": 2.3155980110168457, "end": **********.000682, "relative_end": **********.000682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.index", "start": **********.001264, "relative_start": 2.3161799907684326, "end": **********.001264, "relative_end": **********.001264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.variants.default", "start": **********.00188, "relative_start": 2.316795825958252, "end": **********.00188, "relative_end": **********.00188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.002528, "relative_start": 2.31744384765625, "end": **********.002528, "relative_end": **********.002528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.003405, "relative_start": 2.3183209896087646, "end": **********.003405, "relative_end": **********.003405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.007431, "relative_start": 2.3223469257354736, "end": **********.007431, "relative_end": **********.007431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.008341, "relative_start": 2.3232569694519043, "end": **********.008341, "relative_end": **********.008341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.009078, "relative_start": 2.3239939212799072, "end": **********.009078, "relative_end": **********.009078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.010655, "relative_start": 2.325570821762085, "end": **********.010655, "relative_end": **********.010655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-down", "start": **********.011208, "relative_start": 2.3261239528656006, "end": **********.011208, "relative_end": **********.011208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.011664, "relative_start": 2.3265798091888428, "end": **********.011664, "relative_end": **********.011664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.012212, "relative_start": 2.3271279335021973, "end": **********.012212, "relative_end": **********.012212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.012879, "relative_start": 2.3277947902679443, "end": **********.012879, "relative_end": **********.012879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.013965, "relative_start": 2.328880786895752, "end": **********.013965, "relative_end": **********.013965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.014795, "relative_start": 2.3297109603881836, "end": **********.014795, "relative_end": **********.014795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.015669, "relative_start": 2.33058500289917, "end": **********.015669, "relative_end": **********.015669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.016465, "relative_start": 2.331380844116211, "end": **********.016465, "relative_end": **********.016465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.017065, "relative_start": 2.3319809436798096, "end": **********.017065, "relative_end": **********.017065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.017748, "relative_start": 2.3326640129089355, "end": **********.017748, "relative_end": **********.017748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.018204, "relative_start": 2.3331198692321777, "end": **********.018204, "relative_end": **********.018204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.018769, "relative_start": 2.3336849212646484, "end": **********.018769, "relative_end": **********.018769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.0193, "relative_start": 2.3342158794403076, "end": **********.0193, "relative_end": **********.0193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.023425, "relative_start": 2.338340997695923, "end": **********.023425, "relative_end": **********.023425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.02579, "relative_start": 2.3407058715820312, "end": **********.02579, "relative_end": **********.02579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.027007, "relative_start": 2.3419229984283447, "end": **********.027007, "relative_end": **********.027007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.027734, "relative_start": 2.3426499366760254, "end": **********.027734, "relative_end": **********.027734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.028463, "relative_start": 2.343378782272339, "end": **********.028463, "relative_end": **********.028463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.029444, "relative_start": 2.344359874725342, "end": **********.029444, "relative_end": **********.029444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.029979, "relative_start": 2.3448948860168457, "end": **********.029979, "relative_end": **********.029979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.030528, "relative_start": 2.3454439640045166, "end": **********.030528, "relative_end": **********.030528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.031036, "relative_start": 2.345951795578003, "end": **********.031036, "relative_end": **********.031036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.031974, "relative_start": 2.3468899726867676, "end": **********.031974, "relative_end": **********.031974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.032641, "relative_start": 2.3475568294525146, "end": **********.032641, "relative_end": **********.032641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.033156, "relative_start": 2.348071813583374, "end": **********.033156, "relative_end": **********.033156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.033661, "relative_start": 2.348576784133911, "end": **********.033661, "relative_end": **********.033661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.034111, "relative_start": 2.349026918411255, "end": **********.034111, "relative_end": **********.034111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.034892, "relative_start": 2.3498079776763916, "end": **********.034892, "relative_end": **********.034892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.035558, "relative_start": 2.3504738807678223, "end": **********.035558, "relative_end": **********.035558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.036146, "relative_start": 2.3510618209838867, "end": **********.036146, "relative_end": **********.036146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.036677, "relative_start": 2.351592779159546, "end": **********.036677, "relative_end": **********.036677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.037559, "relative_start": 2.3524749279022217, "end": **********.037559, "relative_end": **********.037559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.038525, "relative_start": 2.353440999984741, "end": **********.038525, "relative_end": **********.038525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.039575, "relative_start": 2.3544909954071045, "end": **********.039575, "relative_end": **********.039575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.040249, "relative_start": 2.3551650047302246, "end": **********.040249, "relative_end": **********.040249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.040767, "relative_start": 2.355682849884033, "end": **********.040767, "relative_end": **********.040767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.041496, "relative_start": 2.356411933898926, "end": **********.041496, "relative_end": **********.041496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.041946, "relative_start": 2.3568618297576904, "end": **********.041946, "relative_end": **********.041946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.04247, "relative_start": 2.3573858737945557, "end": **********.04247, "relative_end": **********.04247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.042969, "relative_start": 2.3578848838806152, "end": **********.042969, "relative_end": **********.042969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.043846, "relative_start": 2.358761787414551, "end": **********.043846, "relative_end": **********.043846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.044469, "relative_start": 2.3593850135803223, "end": **********.044469, "relative_end": **********.044469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.044996, "relative_start": 2.3599119186401367, "end": **********.044996, "relative_end": **********.044996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.045461, "relative_start": 2.3603768348693848, "end": **********.045461, "relative_end": **********.045461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.045899, "relative_start": 2.3608148097991943, "end": **********.045899, "relative_end": **********.045899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.046588, "relative_start": 2.361503839492798, "end": **********.046588, "relative_end": **********.046588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.047045, "relative_start": 2.3619608879089355, "end": **********.047045, "relative_end": **********.047045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.047569, "relative_start": 2.362484931945801, "end": **********.047569, "relative_end": **********.047569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.048084, "relative_start": 2.36299991607666, "end": **********.048084, "relative_end": **********.048084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.048945, "relative_start": 2.363860845565796, "end": **********.048945, "relative_end": **********.048945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.049571, "relative_start": 2.3644869327545166, "end": **********.049571, "relative_end": **********.049571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.050123, "relative_start": 2.3650388717651367, "end": **********.050123, "relative_end": **********.050123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.050826, "relative_start": 2.3657419681549072, "end": **********.050826, "relative_end": **********.050826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.051346, "relative_start": 2.3662619590759277, "end": **********.051346, "relative_end": **********.051346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.052096, "relative_start": 2.367011785507202, "end": **********.052096, "relative_end": **********.052096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.05257, "relative_start": 2.367486000061035, "end": **********.05257, "relative_end": **********.05257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.053116, "relative_start": 2.3680319786071777, "end": **********.053116, "relative_end": **********.053116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.053629, "relative_start": 2.368544816970825, "end": **********.053629, "relative_end": **********.053629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.054748, "relative_start": 2.369663953781128, "end": **********.054748, "relative_end": **********.054748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.056114, "relative_start": 2.371029853820801, "end": **********.056114, "relative_end": **********.056114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.05688, "relative_start": 2.371795892715454, "end": **********.05688, "relative_end": **********.05688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.057417, "relative_start": 2.372332811355591, "end": **********.057417, "relative_end": **********.057417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.057862, "relative_start": 2.3727779388427734, "end": **********.057862, "relative_end": **********.057862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.058532, "relative_start": 2.373447895050049, "end": **********.058532, "relative_end": **********.058532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.05898, "relative_start": 2.3738958835601807, "end": **********.05898, "relative_end": **********.05898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.059487, "relative_start": 2.3744029998779297, "end": **********.059487, "relative_end": **********.059487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.059967, "relative_start": 2.374882936477661, "end": **********.059967, "relative_end": **********.059967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.060775, "relative_start": 2.3756909370422363, "end": **********.060775, "relative_end": **********.060775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.061349, "relative_start": 2.376264810562134, "end": **********.061349, "relative_end": **********.061349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.061844, "relative_start": 2.3767600059509277, "end": **********.061844, "relative_end": **********.061844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.062325, "relative_start": 2.3772408962249756, "end": **********.062325, "relative_end": **********.062325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.062757, "relative_start": 2.3776729106903076, "end": **********.062757, "relative_end": **********.062757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.063429, "relative_start": 2.378345012664795, "end": **********.063429, "relative_end": **********.063429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.063849, "relative_start": 2.3787648677825928, "end": **********.063849, "relative_end": **********.063849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.064381, "relative_start": 2.3792967796325684, "end": **********.064381, "relative_end": **********.064381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.064877, "relative_start": 2.3797929286956787, "end": **********.064877, "relative_end": **********.064877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.06574, "relative_start": 2.3806560039520264, "end": **********.06574, "relative_end": **********.06574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.06664, "relative_start": 2.3815557956695557, "end": **********.06664, "relative_end": **********.06664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.0674, "relative_start": 2.3823158740997314, "end": **********.0674, "relative_end": **********.0674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.06791, "relative_start": 2.3828258514404297, "end": **********.06791, "relative_end": **********.06791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.068444, "relative_start": 2.383359909057617, "end": **********.068444, "relative_end": **********.068444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.069175, "relative_start": 2.3840909004211426, "end": **********.069175, "relative_end": **********.069175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.06966, "relative_start": 2.384575843811035, "end": **********.06966, "relative_end": **********.06966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.070184, "relative_start": 2.3850998878479004, "end": **********.070184, "relative_end": **********.070184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.070689, "relative_start": 2.3856048583984375, "end": **********.070689, "relative_end": **********.070689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.07228, "relative_start": 2.3871958255767822, "end": **********.07228, "relative_end": **********.07228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.073166, "relative_start": 2.3880817890167236, "end": **********.073166, "relative_end": **********.073166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.07373, "relative_start": 2.388645887374878, "end": **********.07373, "relative_end": **********.07373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.074206, "relative_start": 2.3891220092773438, "end": **********.074206, "relative_end": **********.074206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.074686, "relative_start": 2.389601945877075, "end": **********.074686, "relative_end": **********.074686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.075373, "relative_start": 2.390288829803467, "end": **********.075373, "relative_end": **********.075373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.075823, "relative_start": 2.3907389640808105, "end": **********.075823, "relative_end": **********.075823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.076371, "relative_start": 2.391286849975586, "end": **********.076371, "relative_end": **********.076371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.076876, "relative_start": 2.391791820526123, "end": **********.076876, "relative_end": **********.076876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.077745, "relative_start": 2.3926608562469482, "end": **********.077745, "relative_end": **********.077745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.078375, "relative_start": 2.3932909965515137, "end": **********.078375, "relative_end": **********.078375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.078903, "relative_start": 2.3938188552856445, "end": **********.078903, "relative_end": **********.078903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.079377, "relative_start": 2.3942928314208984, "end": **********.079377, "relative_end": **********.079377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.079818, "relative_start": 2.3947339057922363, "end": **********.079818, "relative_end": **********.079818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.080515, "relative_start": 2.39543080329895, "end": **********.080515, "relative_end": **********.080515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.08094, "relative_start": 2.3958559036254883, "end": **********.08094, "relative_end": **********.08094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.081613, "relative_start": 2.396528959274292, "end": **********.081613, "relative_end": **********.081613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.082167, "relative_start": 2.397082805633545, "end": **********.082167, "relative_end": **********.082167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.083017, "relative_start": 2.397933006286621, "end": **********.083017, "relative_end": **********.083017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.083568, "relative_start": 2.398483991622925, "end": **********.083568, "relative_end": **********.083568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.084058, "relative_start": 2.3989739418029785, "end": **********.084058, "relative_end": **********.084058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.084472, "relative_start": 2.399387836456299, "end": **********.084472, "relative_end": **********.084472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.084886, "relative_start": 2.3998019695281982, "end": **********.084886, "relative_end": **********.084886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.085538, "relative_start": 2.400453805923462, "end": **********.085538, "relative_end": **********.085538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.085964, "relative_start": 2.4008798599243164, "end": **********.085964, "relative_end": **********.085964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.08645, "relative_start": 2.4013659954071045, "end": **********.08645, "relative_end": **********.08645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.086924, "relative_start": 2.4018399715423584, "end": **********.086924, "relative_end": **********.086924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.087908, "relative_start": 2.4028239250183105, "end": **********.087908, "relative_end": **********.087908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.089096, "relative_start": 2.4040119647979736, "end": **********.089096, "relative_end": **********.089096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.089899, "relative_start": 2.4048149585723877, "end": **********.089899, "relative_end": **********.089899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.090387, "relative_start": 2.4053030014038086, "end": **********.090387, "relative_end": **********.090387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.090824, "relative_start": 2.4057397842407227, "end": **********.090824, "relative_end": **********.090824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.091485, "relative_start": 2.4064009189605713, "end": **********.091485, "relative_end": **********.091485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.0919, "relative_start": 2.406816005706787, "end": **********.0919, "relative_end": **********.0919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.092401, "relative_start": 2.4073169231414795, "end": **********.092401, "relative_end": **********.092401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.092896, "relative_start": 2.4078118801116943, "end": **********.092896, "relative_end": **********.092896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.093712, "relative_start": 2.408627986907959, "end": **********.093712, "relative_end": **********.093712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.094289, "relative_start": 2.4092049598693848, "end": **********.094289, "relative_end": **********.094289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.094783, "relative_start": 2.409698963165283, "end": **********.094783, "relative_end": **********.094783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.095261, "relative_start": 2.410176992416382, "end": **********.095261, "relative_end": **********.095261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.095703, "relative_start": 2.410618782043457, "end": **********.095703, "relative_end": **********.095703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.096378, "relative_start": 2.4112939834594727, "end": **********.096378, "relative_end": **********.096378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.096816, "relative_start": 2.4117319583892822, "end": **********.096816, "relative_end": **********.096816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.098472, "relative_start": 2.4133880138397217, "end": **********.098472, "relative_end": **********.098472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.099215, "relative_start": 2.414130926132202, "end": **********.099215, "relative_end": **********.099215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.100285, "relative_start": 2.41520094871521, "end": **********.100285, "relative_end": **********.100285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.100921, "relative_start": 2.415836811065674, "end": **********.100921, "relative_end": **********.100921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.101431, "relative_start": 2.416346788406372, "end": **********.101431, "relative_end": **********.101431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.101878, "relative_start": 2.4167938232421875, "end": **********.101878, "relative_end": **********.101878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.102292, "relative_start": 2.417207956314087, "end": **********.102292, "relative_end": **********.102292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.102936, "relative_start": 2.4178519248962402, "end": **********.102936, "relative_end": **********.102936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.10337, "relative_start": 2.418285846710205, "end": **********.10337, "relative_end": **********.10337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.103868, "relative_start": 2.4187839031219482, "end": **********.103868, "relative_end": **********.103868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.104502, "relative_start": 2.4194178581237793, "end": **********.104502, "relative_end": **********.104502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.106153, "relative_start": 2.4210689067840576, "end": **********.106153, "relative_end": **********.106153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.106887, "relative_start": 2.4218029975891113, "end": **********.106887, "relative_end": **********.106887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.107416, "relative_start": 2.4223318099975586, "end": **********.107416, "relative_end": **********.107416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.107861, "relative_start": 2.422776937484741, "end": **********.107861, "relative_end": **********.107861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.108292, "relative_start": 2.423207998275757, "end": **********.108292, "relative_end": **********.108292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.108963, "relative_start": 2.4238789081573486, "end": **********.108963, "relative_end": **********.108963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.109396, "relative_start": 2.424311876296997, "end": **********.109396, "relative_end": **********.109396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.109877, "relative_start": 2.424793004989624, "end": **********.109877, "relative_end": **********.109877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.110413, "relative_start": 2.4253289699554443, "end": **********.110413, "relative_end": **********.110413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.111279, "relative_start": 2.426194906234741, "end": **********.111279, "relative_end": **********.111279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.11188, "relative_start": 2.4267959594726562, "end": **********.11188, "relative_end": **********.11188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.112588, "relative_start": 2.427503824234009, "end": **********.112588, "relative_end": **********.112588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.113147, "relative_start": 2.428062915802002, "end": **********.113147, "relative_end": **********.113147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.113609, "relative_start": 2.428524971008301, "end": **********.113609, "relative_end": **********.113609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.114295, "relative_start": 2.429210901260376, "end": **********.114295, "relative_end": **********.114295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.114705, "relative_start": 2.4296209812164307, "end": **********.114705, "relative_end": **********.114705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.115162, "relative_start": 2.4300777912139893, "end": **********.115162, "relative_end": **********.115162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.115789, "relative_start": 2.4307048320770264, "end": **********.115789, "relative_end": **********.115789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.116941, "relative_start": 2.431856870651245, "end": **********.116941, "relative_end": **********.116941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.117786, "relative_start": 2.432701826095581, "end": **********.117786, "relative_end": **********.117786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.118514, "relative_start": 2.4334299564361572, "end": **********.118514, "relative_end": **********.118514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.119154, "relative_start": 2.434069871902466, "end": **********.119154, "relative_end": **********.119154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.119796, "relative_start": 2.4347119331359863, "end": **********.119796, "relative_end": **********.119796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.120773, "relative_start": 2.4356889724731445, "end": **********.120773, "relative_end": **********.120773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.121686, "relative_start": 2.4366018772125244, "end": **********.121686, "relative_end": **********.121686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.122801, "relative_start": 2.4377169609069824, "end": **********.122801, "relative_end": **********.122801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.123459, "relative_start": 2.4383749961853027, "end": **********.123459, "relative_end": **********.123459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.124373, "relative_start": 2.439288854598999, "end": **********.124373, "relative_end": **********.124373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.124952, "relative_start": 2.4398679733276367, "end": **********.124952, "relative_end": **********.124952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.125492, "relative_start": 2.4404079914093018, "end": **********.125492, "relative_end": **********.125492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.125937, "relative_start": 2.4408528804779053, "end": **********.125937, "relative_end": **********.125937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.126401, "relative_start": 2.441316843032837, "end": **********.126401, "relative_end": **********.126401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.12707, "relative_start": 2.441985845565796, "end": **********.12707, "relative_end": **********.12707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.127544, "relative_start": 2.44245982170105, "end": **********.127544, "relative_end": **********.127544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.128081, "relative_start": 2.4429969787597656, "end": **********.128081, "relative_end": **********.128081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.129038, "relative_start": 2.4439539909362793, "end": **********.129038, "relative_end": **********.129038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.130664, "relative_start": 2.445580005645752, "end": **********.130664, "relative_end": **********.130664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.131663, "relative_start": 2.4465789794921875, "end": **********.131663, "relative_end": **********.131663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.132523, "relative_start": 2.447438955307007, "end": **********.132523, "relative_end": **********.132523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.133266, "relative_start": 2.4481818675994873, "end": **********.133266, "relative_end": **********.133266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.133948, "relative_start": 2.448863983154297, "end": **********.133948, "relative_end": **********.133948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.135131, "relative_start": 2.4500467777252197, "end": **********.135131, "relative_end": **********.135131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.135867, "relative_start": 2.4507830142974854, "end": **********.135867, "relative_end": **********.135867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.136696, "relative_start": 2.4516119956970215, "end": **********.136696, "relative_end": **********.136696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.137507, "relative_start": 2.452422857284546, "end": **********.137507, "relative_end": **********.137507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.13942, "relative_start": 2.454335927963257, "end": **********.13942, "relative_end": **********.13942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.140341, "relative_start": 2.455256938934326, "end": **********.140341, "relative_end": **********.140341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.140897, "relative_start": 2.455812931060791, "end": **********.140897, "relative_end": **********.140897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.14137, "relative_start": 2.4562859535217285, "end": **********.14137, "relative_end": **********.14137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.141832, "relative_start": 2.4567480087280273, "end": **********.141832, "relative_end": **********.141832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.142515, "relative_start": 2.457430839538574, "end": **********.142515, "relative_end": **********.142515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.142967, "relative_start": 2.457882881164551, "end": **********.142967, "relative_end": **********.142967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.143498, "relative_start": 2.45841383934021, "end": **********.143498, "relative_end": **********.143498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.14421, "relative_start": 2.4591259956359863, "end": **********.14421, "relative_end": **********.14421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.145166, "relative_start": 2.4600818157196045, "end": **********.145166, "relative_end": **********.145166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.145841, "relative_start": 2.460756778717041, "end": **********.145841, "relative_end": **********.145841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.14643, "relative_start": 2.461345911026001, "end": **********.14643, "relative_end": **********.14643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.146926, "relative_start": 2.4618418216705322, "end": **********.146926, "relative_end": **********.146926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.147383, "relative_start": 2.46229887008667, "end": **********.147383, "relative_end": **********.147383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.148096, "relative_start": 2.4630119800567627, "end": **********.148096, "relative_end": **********.148096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.148566, "relative_start": 2.463481903076172, "end": **********.148566, "relative_end": **********.148566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.149107, "relative_start": 2.4640228748321533, "end": **********.149107, "relative_end": **********.149107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.149627, "relative_start": 2.464542865753174, "end": **********.149627, "relative_end": **********.149627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.150543, "relative_start": 2.465458869934082, "end": **********.150543, "relative_end": **********.150543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.151182, "relative_start": 2.466097831726074, "end": **********.151182, "relative_end": **********.151182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.151732, "relative_start": 2.4666478633880615, "end": **********.151732, "relative_end": **********.151732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.152243, "relative_start": 2.467158794403076, "end": **********.152243, "relative_end": **********.152243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.152717, "relative_start": 2.467633008956909, "end": **********.152717, "relative_end": **********.152717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.153436, "relative_start": 2.4683518409729004, "end": **********.153436, "relative_end": **********.153436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.153921, "relative_start": 2.468836784362793, "end": **********.153921, "relative_end": **********.153921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.154924, "relative_start": 2.4698398113250732, "end": **********.154924, "relative_end": **********.154924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.155875, "relative_start": 2.4707908630371094, "end": **********.155875, "relative_end": **********.155875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.157089, "relative_start": 2.4720048904418945, "end": **********.157089, "relative_end": **********.157089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.157758, "relative_start": 2.4726738929748535, "end": **********.157758, "relative_end": **********.157758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.158305, "relative_start": 2.4732208251953125, "end": **********.158305, "relative_end": **********.158305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.158911, "relative_start": 2.4738268852233887, "end": **********.158911, "relative_end": **********.158911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.159661, "relative_start": 2.474576950073242, "end": **********.159661, "relative_end": **********.159661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.16039, "relative_start": 2.4753057956695557, "end": **********.16039, "relative_end": **********.16039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.160837, "relative_start": 2.475752830505371, "end": **********.160837, "relative_end": **********.160837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.16137, "relative_start": 2.476285934448242, "end": **********.16137, "relative_end": **********.16137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.161876, "relative_start": 2.4767918586730957, "end": **********.161876, "relative_end": **********.161876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.162763, "relative_start": 2.4776790142059326, "end": **********.162763, "relative_end": **********.162763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.163389, "relative_start": 2.478304862976074, "end": **********.163389, "relative_end": **********.163389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.163935, "relative_start": 2.478850841522217, "end": **********.163935, "relative_end": **********.163935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.164421, "relative_start": 2.479336977005005, "end": **********.164421, "relative_end": **********.164421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.164896, "relative_start": 2.479811906814575, "end": **********.164896, "relative_end": **********.164896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.165582, "relative_start": 2.4804978370666504, "end": **********.165582, "relative_end": **********.165582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.166046, "relative_start": 2.480961799621582, "end": **********.166046, "relative_end": **********.166046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.166585, "relative_start": 2.4815008640289307, "end": **********.166585, "relative_end": **********.166585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.167121, "relative_start": 2.482036828994751, "end": **********.167121, "relative_end": **********.167121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.167998, "relative_start": 2.4829139709472656, "end": **********.167998, "relative_end": **********.167998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.168645, "relative_start": 2.483560800552368, "end": **********.168645, "relative_end": **********.168645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.169191, "relative_start": 2.4841067790985107, "end": **********.169191, "relative_end": **********.169191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.16971, "relative_start": 2.484625816345215, "end": **********.16971, "relative_end": **********.16971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.170175, "relative_start": 2.485090970993042, "end": **********.170175, "relative_end": **********.170175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.170905, "relative_start": 2.485821008682251, "end": **********.170905, "relative_end": **********.170905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.172465, "relative_start": 2.4873809814453125, "end": **********.172465, "relative_end": **********.172465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.173269, "relative_start": 2.488184928894043, "end": **********.173269, "relative_end": **********.173269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.173905, "relative_start": 2.488820791244507, "end": **********.173905, "relative_end": **********.173905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.175251, "relative_start": 2.4901669025421143, "end": **********.175251, "relative_end": **********.175251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.17627, "relative_start": 2.4911859035491943, "end": **********.17627, "relative_end": **********.17627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.177096, "relative_start": 2.492011785507202, "end": **********.177096, "relative_end": **********.177096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.177742, "relative_start": 2.4926578998565674, "end": **********.177742, "relative_end": **********.177742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.178269, "relative_start": 2.493184804916382, "end": **********.178269, "relative_end": **********.178269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.178971, "relative_start": 2.493886947631836, "end": **********.178971, "relative_end": **********.178971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.179464, "relative_start": 2.494379997253418, "end": **********.179464, "relative_end": **********.179464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.180006, "relative_start": 2.494921922683716, "end": **********.180006, "relative_end": **********.180006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.180528, "relative_start": 2.495443820953369, "end": **********.180528, "relative_end": **********.180528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.181434, "relative_start": 2.496349811553955, "end": **********.181434, "relative_end": **********.181434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.182061, "relative_start": 2.496976852416992, "end": **********.182061, "relative_end": **********.182061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.182636, "relative_start": 2.497551918029785, "end": **********.182636, "relative_end": **********.182636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.183126, "relative_start": 2.498041868209839, "end": **********.183126, "relative_end": **********.183126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.183595, "relative_start": 2.4985108375549316, "end": **********.183595, "relative_end": **********.183595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.184298, "relative_start": 2.499213933944702, "end": **********.184298, "relative_end": **********.184298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.184812, "relative_start": 2.499727964401245, "end": **********.184812, "relative_end": **********.184812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.185367, "relative_start": 2.5002830028533936, "end": **********.185367, "relative_end": **********.185367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.185899, "relative_start": 2.500814914703369, "end": **********.185899, "relative_end": **********.185899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.186812, "relative_start": 2.501727819442749, "end": **********.186812, "relative_end": **********.186812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.187456, "relative_start": 2.5023717880249023, "end": **********.187456, "relative_end": **********.187456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.188565, "relative_start": 2.503480911254883, "end": **********.188565, "relative_end": **********.188565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.189498, "relative_start": 2.5044138431549072, "end": **********.189498, "relative_end": **********.189498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.190308, "relative_start": 2.5052239894866943, "end": **********.190308, "relative_end": **********.190308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.191113, "relative_start": 2.506028890609741, "end": **********.191113, "relative_end": **********.191113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.191643, "relative_start": 2.506558895111084, "end": **********.191643, "relative_end": **********.191643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.192197, "relative_start": 2.507112979888916, "end": **********.192197, "relative_end": **********.192197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.192747, "relative_start": 2.5076630115509033, "end": **********.192747, "relative_end": **********.192747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.193686, "relative_start": 2.5086019039154053, "end": **********.193686, "relative_end": **********.193686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.194335, "relative_start": 2.5092508792877197, "end": **********.194335, "relative_end": **********.194335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.194901, "relative_start": 2.509816884994507, "end": **********.194901, "relative_end": **********.194901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.195406, "relative_start": 2.510321855545044, "end": **********.195406, "relative_end": **********.195406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.195874, "relative_start": 2.5107898712158203, "end": **********.195874, "relative_end": **********.195874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.196577, "relative_start": 2.511492967605591, "end": **********.196577, "relative_end": **********.196577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.197073, "relative_start": 2.511988878250122, "end": **********.197073, "relative_end": **********.197073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.197618, "relative_start": 2.5125339031219482, "end": **********.197618, "relative_end": **********.197618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.198152, "relative_start": 2.5130679607391357, "end": **********.198152, "relative_end": **********.198152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.199416, "relative_start": 2.514331817626953, "end": **********.199416, "relative_end": **********.199416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.200006, "relative_start": 2.5149219036102295, "end": **********.200006, "relative_end": **********.200006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.200577, "relative_start": 2.5154929161071777, "end": **********.200577, "relative_end": **********.200577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.201873, "relative_start": 2.516788959503174, "end": **********.201873, "relative_end": **********.201873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-down", "start": **********.202425, "relative_start": 2.517340898513794, "end": **********.202425, "relative_end": **********.202425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.20294, "relative_start": 2.5178558826446533, "end": **********.20294, "relative_end": **********.20294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.203515, "relative_start": 2.5184309482574463, "end": **********.203515, "relative_end": **********.203515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.204285, "relative_start": 2.5192008018493652, "end": **********.204285, "relative_end": **********.204285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.default", "start": **********.206456, "relative_start": 2.521371841430664, "end": **********.206456, "relative_end": **********.206456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.indicator", "start": **********.207219, "relative_start": 2.522134780883789, "end": **********.207219, "relative_end": **********.207219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.207636, "relative_start": 2.522552013397217, "end": **********.207636, "relative_end": **********.207636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.208361, "relative_start": 2.5232768058776855, "end": **********.208361, "relative_end": **********.208361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.208855, "relative_start": 2.523770809173584, "end": **********.208855, "relative_end": **********.208855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.209389, "relative_start": 2.5243048667907715, "end": **********.209389, "relative_end": **********.209389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.210037, "relative_start": 2.5249528884887695, "end": **********.210037, "relative_end": **********.210037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.default", "start": **********.21099, "relative_start": 2.5259058475494385, "end": **********.21099, "relative_end": **********.21099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.indicator", "start": **********.211622, "relative_start": 2.5265378952026367, "end": **********.211622, "relative_end": **********.211622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.212025, "relative_start": 2.5269408226013184, "end": **********.212025, "relative_end": **********.212025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.21276, "relative_start": 2.5276758670806885, "end": **********.21276, "relative_end": **********.21276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.213237, "relative_start": 2.5281529426574707, "end": **********.213237, "relative_end": **********.213237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.213765, "relative_start": 2.5286808013916016, "end": **********.213765, "relative_end": **********.213765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.214436, "relative_start": 2.5293519496917725, "end": **********.214436, "relative_end": **********.214436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.default", "start": **********.215393, "relative_start": 2.530308961868286, "end": **********.215393, "relative_end": **********.215393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.indicator", "start": **********.216038, "relative_start": 2.530953884124756, "end": **********.216038, "relative_end": **********.216038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.216442, "relative_start": 2.531358003616333, "end": **********.216442, "relative_end": **********.216442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.217164, "relative_start": 2.5320799350738525, "end": **********.217164, "relative_end": **********.217164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.217635, "relative_start": 2.532550811767578, "end": **********.217635, "relative_end": **********.217635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.218186, "relative_start": 2.533101797103882, "end": **********.218186, "relative_end": **********.218186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.218836, "relative_start": 2.533751964569092, "end": **********.218836, "relative_end": **********.218836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.default", "start": **********.219763, "relative_start": 2.5346789360046387, "end": **********.219763, "relative_end": **********.219763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.indicator", "start": **********.220389, "relative_start": 2.5353047847747803, "end": **********.220389, "relative_end": **********.220389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.221122, "relative_start": 2.5360379219055176, "end": **********.221122, "relative_end": **********.221122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.22264, "relative_start": 2.5375559329986572, "end": **********.22264, "relative_end": **********.22264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.223312, "relative_start": 2.5382277965545654, "end": **********.223312, "relative_end": **********.223312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.223884, "relative_start": 2.538800001144409, "end": **********.223884, "relative_end": **********.223884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.index", "start": **********.224411, "relative_start": 2.5393269062042236, "end": **********.224411, "relative_end": **********.224411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.variants.default", "start": **********.224892, "relative_start": 2.5398077964782715, "end": **********.224892, "relative_end": **********.224892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.225423, "relative_start": 2.5403389930725098, "end": **********.225423, "relative_end": **********.225423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": **********.226247, "relative_start": 2.5411629676818848, "end": **********.226247, "relative_end": **********.226247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.226998, "relative_start": 2.5419139862060547, "end": **********.226998, "relative_end": **********.226998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.228109, "relative_start": 2.543024778366089, "end": **********.228109, "relative_end": **********.228109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.228599, "relative_start": 2.5435149669647217, "end": **********.228599, "relative_end": **********.228599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.229044, "relative_start": 2.543959856033325, "end": **********.229044, "relative_end": **********.229044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.229534, "relative_start": 2.544449806213379, "end": **********.229534, "relative_end": **********.229534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.230027, "relative_start": 2.544942855834961, "end": **********.230027, "relative_end": **********.230027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.columns", "start": **********.230456, "relative_start": 2.5453720092773438, "end": **********.230456, "relative_end": **********.230456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.231489, "relative_start": 2.5464048385620117, "end": **********.231489, "relative_end": **********.231489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.232697, "relative_start": 2.5476129055023193, "end": **********.232697, "relative_end": **********.232697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.view-columns", "start": **********.233411, "relative_start": 2.5483269691467285, "end": **********.233411, "relative_end": **********.233411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.234012, "relative_start": 2.5489277839660645, "end": **********.234012, "relative_end": **********.234012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.234524, "relative_start": 2.5494399070739746, "end": **********.234524, "relative_end": **********.234524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.235157, "relative_start": 2.5500729084014893, "end": **********.235157, "relative_end": **********.235157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.235759, "relative_start": 2.5506749153137207, "end": **********.235759, "relative_end": **********.235759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.250472, "relative_start": 2.5653879642486572, "end": **********.250472, "relative_end": **********.250472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.252399, "relative_start": 2.567314863204956, "end": **********.252399, "relative_end": **********.252399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.254784, "relative_start": 2.569700002670288, "end": **********.254784, "relative_end": **********.254784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.256366, "relative_start": 2.571281909942627, "end": **********.256366, "relative_end": **********.256366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.256999, "relative_start": 2.5719149112701416, "end": **********.256999, "relative_end": **********.256999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.260932, "relative_start": 2.575847864151001, "end": **********.260932, "relative_end": **********.260932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.261734, "relative_start": 2.5766499042510986, "end": **********.261734, "relative_end": **********.261734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.264022, "relative_start": 2.5789380073547363, "end": **********.264022, "relative_end": **********.264022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.264709, "relative_start": 2.579624891281128, "end": **********.264709, "relative_end": **********.264709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.265338, "relative_start": 2.580253839492798, "end": **********.265338, "relative_end": **********.265338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.266015, "relative_start": 2.5809309482574463, "end": **********.266015, "relative_end": **********.266015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.266891, "relative_start": 2.5818068981170654, "end": **********.266891, "relative_end": **********.266891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.267917, "relative_start": 2.5828328132629395, "end": **********.267917, "relative_end": **********.267917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.268485, "relative_start": 2.5834009647369385, "end": **********.268485, "relative_end": **********.268485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.269257, "relative_start": 2.5841729640960693, "end": **********.269257, "relative_end": **********.269257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.269764, "relative_start": 2.5846798419952393, "end": **********.269764, "relative_end": **********.269764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.270509, "relative_start": 2.5854249000549316, "end": **********.270509, "relative_end": **********.270509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.271732, "relative_start": 2.5866479873657227, "end": **********.271732, "relative_end": **********.271732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.272731, "relative_start": 2.587646961212158, "end": **********.272731, "relative_end": **********.272731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.273834, "relative_start": 2.588749885559082, "end": **********.273834, "relative_end": **********.273834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.274468, "relative_start": 2.589383840560913, "end": **********.274468, "relative_end": **********.274468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.275013, "relative_start": 2.5899288654327393, "end": **********.275013, "relative_end": **********.275013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.275548, "relative_start": 2.590463876724243, "end": **********.275548, "relative_end": **********.275548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.276328, "relative_start": 2.5912439823150635, "end": **********.276328, "relative_end": **********.276328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.276862, "relative_start": 2.591777801513672, "end": **********.276862, "relative_end": **********.276862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.277532, "relative_start": 2.5924479961395264, "end": **********.277532, "relative_end": **********.277532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.278174, "relative_start": 2.5930898189544678, "end": **********.278174, "relative_end": **********.278174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.278945, "relative_start": 2.5938608646392822, "end": **********.278945, "relative_end": **********.278945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.279794, "relative_start": 2.594709873199463, "end": **********.279794, "relative_end": **********.279794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.280426, "relative_start": 2.595341920852661, "end": **********.280426, "relative_end": **********.280426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.280947, "relative_start": 2.595862865447998, "end": **********.280947, "relative_end": **********.280947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.28159, "relative_start": 2.596505880355835, "end": **********.28159, "relative_end": **********.28159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.282535, "relative_start": 2.5974509716033936, "end": **********.282535, "relative_end": **********.282535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.28482, "relative_start": 2.599735975265503, "end": **********.28482, "relative_end": **********.28482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.287088, "relative_start": 2.602003812789917, "end": **********.287088, "relative_end": **********.287088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.289734, "relative_start": 2.604649782180786, "end": **********.289734, "relative_end": **********.289734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.290867, "relative_start": 2.605782985687256, "end": **********.290867, "relative_end": **********.290867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.291989, "relative_start": 2.606904983520508, "end": **********.291989, "relative_end": **********.291989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.292602, "relative_start": 2.607517957687378, "end": **********.292602, "relative_end": **********.292602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.293101, "relative_start": 2.6080169677734375, "end": **********.293101, "relative_end": **********.293101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.293557, "relative_start": 2.6084728240966797, "end": **********.293557, "relative_end": **********.293557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.294293, "relative_start": 2.609208822250366, "end": **********.294293, "relative_end": **********.294293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.29477, "relative_start": 2.6096858978271484, "end": **********.29477, "relative_end": **********.29477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.295317, "relative_start": 2.6102328300476074, "end": **********.295317, "relative_end": **********.295317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.index", "start": **********.295865, "relative_start": 2.610780954360962, "end": **********.295865, "relative_end": **********.295865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.variants.default", "start": **********.296419, "relative_start": 2.611334800720215, "end": **********.296419, "relative_end": **********.296419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::checkbox.indicator", "start": **********.297051, "relative_start": 2.611966848373413, "end": **********.297051, "relative_end": **********.297051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.297598, "relative_start": 2.612513780593872, "end": **********.297598, "relative_end": **********.297598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.minus", "start": **********.298082, "relative_start": 2.6129980087280273, "end": **********.298082, "relative_end": **********.298082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-inline-field", "start": **********.298593, "relative_start": 2.613508939743042, "end": **********.298593, "relative_end": **********.298593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.299535, "relative_start": 2.6144509315490723, "end": **********.299535, "relative_end": **********.299535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.300381, "relative_start": 2.6152968406677246, "end": **********.300381, "relative_end": **********.300381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.30107, "relative_start": 2.615985870361328, "end": **********.30107, "relative_end": **********.30107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.index", "start": **********.30164, "relative_start": 2.61655592918396, "end": **********.30164, "relative_end": **********.30164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown", "start": **********.302137, "relative_start": 2.6170527935028076, "end": **********.302137, "relative_end": **********.302137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.302708, "relative_start": 2.617623805999756, "end": **********.302708, "relative_end": **********.302708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.30324, "relative_start": 2.6181559562683105, "end": **********.30324, "relative_end": **********.30324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.30381, "relative_start": 2.6187257766723633, "end": **********.30381, "relative_end": **********.30381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.attachments", "start": **********.306205, "relative_start": 2.6211209297180176, "end": **********.306205, "relative_end": **********.306205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.306996, "relative_start": 2.6219120025634766, "end": **********.306996, "relative_end": **********.306996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.307741, "relative_start": 2.62265682220459, "end": **********.307741, "relative_end": **********.307741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": **********.308358, "relative_start": 2.6232738494873047, "end": **********.308358, "relative_end": **********.308358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.309282, "relative_start": 2.6241979598999023, "end": **********.309282, "relative_end": **********.309282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.310505, "relative_start": 2.6254208087921143, "end": **********.310505, "relative_end": **********.310505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.310976, "relative_start": 2.625891923904419, "end": **********.310976, "relative_end": **********.310976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.311432, "relative_start": 2.626347780227661, "end": **********.311432, "relative_end": **********.311432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.311922, "relative_start": 2.626837968826294, "end": **********.311922, "relative_end": **********.311922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.312418, "relative_start": 2.627333879470825, "end": **********.312418, "relative_end": **********.312418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.332548, "relative_start": 2.647463798522949, "end": **********.332548, "relative_end": **********.332548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app", "start": **********.3338, "relative_start": 2.6487159729003906, "end": **********.3338, "relative_end": **********.3338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::main", "start": **********.334787, "relative_start": 2.649702787399292, "end": **********.334787, "relative_end": **********.334787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app.sidebar", "start": **********.335495, "relative_start": 2.6504108905792236, "end": **********.335495, "relative_end": **********.335495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.head", "start": **********.339681, "relative_start": 2.6545968055725098, "end": **********.339681, "relative_end": **********.339681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.toggle", "start": **********.341121, "relative_start": 2.656036853790283, "end": **********.341121, "relative_end": **********.341121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.343682, "relative_start": 2.658597946166992, "end": **********.343682, "relative_end": **********.343682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.344921, "relative_start": 2.659837007522583, "end": **********.344921, "relative_end": **********.344921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.345587, "relative_start": 2.6605029106140137, "end": **********.345587, "relative_end": **********.345587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.346189, "relative_start": 2.661104917526245, "end": **********.346189, "relative_end": **********.346189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.346799, "relative_start": 2.661714792251587, "end": **********.346799, "relative_end": **********.346799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.app-logo", "start": **********.347498, "relative_start": 2.6624138355255127, "end": **********.347498, "relative_end": **********.347498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.348632, "relative_start": 2.663547992706299, "end": **********.348632, "relative_end": **********.348632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.349682, "relative_start": 2.664597988128662, "end": **********.349682, "relative_end": **********.349682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.layout-dashboard", "start": **********.35025, "relative_start": 2.665165901184082, "end": **********.35025, "relative_end": **********.35025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.350795, "relative_start": 2.665710926055908, "end": **********.350795, "relative_end": **********.350795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.351602, "relative_start": 2.666517972946167, "end": **********.351602, "relative_end": **********.351602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.352572, "relative_start": 2.667487859725952, "end": **********.352572, "relative_end": **********.352572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.user", "start": **********.353353, "relative_start": 2.668268918991089, "end": **********.353353, "relative_end": **********.353353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.353923, "relative_start": 2.6688389778137207, "end": **********.353923, "relative_end": **********.353923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.355366, "relative_start": 2.6702818870544434, "end": **********.355366, "relative_end": **********.355366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.356601, "relative_start": 2.6715168952941895, "end": **********.356601, "relative_end": **********.356601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.currency-dollar", "start": **********.357434, "relative_start": 2.6723499298095703, "end": **********.357434, "relative_end": **********.357434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.35802, "relative_start": 2.672935962677002, "end": **********.35802, "relative_end": **********.35802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.358849, "relative_start": 2.673764944076538, "end": **********.358849, "relative_end": **********.358849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.35981, "relative_start": 2.6747260093688965, "end": **********.35981, "relative_end": **********.35981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.scroll-text", "start": **********.360504, "relative_start": 2.675419807434082, "end": **********.360504, "relative_end": **********.360504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.361194, "relative_start": 2.676109790802002, "end": **********.361194, "relative_end": **********.361194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.362084, "relative_start": 2.676999807357788, "end": **********.362084, "relative_end": **********.362084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.363063, "relative_start": 2.677978992462158, "end": **********.363063, "relative_end": **********.363063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.queue-list", "start": **********.363856, "relative_start": 2.67877197265625, "end": **********.363856, "relative_end": **********.363856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.364434, "relative_start": 2.679349899291992, "end": **********.364434, "relative_end": **********.364434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.365266, "relative_start": 2.6801819801330566, "end": **********.365266, "relative_end": **********.365266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.366263, "relative_start": 2.6811788082122803, "end": **********.366263, "relative_end": **********.366263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.tags", "start": **********.366871, "relative_start": 2.6817870140075684, "end": **********.366871, "relative_end": **********.366871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.367454, "relative_start": 2.6823699474334717, "end": **********.367454, "relative_end": **********.367454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.group", "start": **********.368074, "relative_start": 2.6829898357391357, "end": **********.368074, "relative_end": **********.368074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.index", "start": **********.368784, "relative_start": 2.6836998462677, "end": **********.368784, "relative_end": **********.368784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.369341, "relative_start": 2.6842567920684814, "end": **********.369341, "relative_end": **********.369341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.369856, "relative_start": 2.68477201461792, "end": **********.369856, "relative_end": **********.369856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.370807, "relative_start": 2.685722827911377, "end": **********.370807, "relative_end": **********.370807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.folder-git-2", "start": **********.372153, "relative_start": 2.6870689392089844, "end": **********.372153, "relative_end": **********.372153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.372941, "relative_start": 2.687856912612915, "end": **********.372941, "relative_end": **********.372941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.index", "start": **********.373615, "relative_start": 2.688530921936035, "end": **********.373615, "relative_end": **********.373615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::profile", "start": **********.374536, "relative_start": 2.6894519329071045, "end": **********.374536, "relative_end": **********.374536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::avatar.index", "start": **********.375843, "relative_start": 2.6907589435577393, "end": **********.375843, "relative_end": **********.375843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.377253, "relative_start": 2.692168951034546, "end": **********.377253, "relative_end": **********.377253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.377829, "relative_start": 2.6927449703216553, "end": **********.377829, "relative_end": **********.377829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.378406, "relative_start": 2.693321943283081, "end": **********.378406, "relative_end": **********.378406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevrons-up-down", "start": **********.378997, "relative_start": 2.693912982940674, "end": **********.378997, "relative_end": **********.378997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.379906, "relative_start": 2.694821834564209, "end": **********.379906, "relative_end": **********.379906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.380479, "relative_start": 2.695394992828369, "end": **********.380479, "relative_end": **********.380479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.381232, "relative_start": 2.696147918701172, "end": **********.381232, "relative_end": **********.381232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.381871, "relative_start": 2.696786880493164, "end": **********.381871, "relative_end": **********.381871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.382628, "relative_start": 2.6975438594818115, "end": **********.382628, "relative_end": **********.382628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.383452, "relative_start": 2.6983678340911865, "end": **********.383452, "relative_end": **********.383452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.sun", "start": **********.384248, "relative_start": 2.6991639137268066, "end": **********.384248, "relative_end": **********.384248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.384896, "relative_start": 2.6998119354248047, "end": **********.384896, "relative_end": **********.384896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.385464, "relative_start": 2.7003798484802246, "end": **********.385464, "relative_end": **********.385464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.386228, "relative_start": 2.701143980026245, "end": **********.386228, "relative_end": **********.386228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.moon", "start": **********.387037, "relative_start": 2.7019529342651367, "end": **********.387037, "relative_end": **********.387037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.387981, "relative_start": 2.7028968334198, "end": **********.387981, "relative_end": **********.387981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.389089, "relative_start": 2.704005002975464, "end": **********.389089, "relative_end": **********.389089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.390035, "relative_start": 2.7049508094787598, "end": **********.390035, "relative_end": **********.390035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.computer-desktop", "start": **********.391113, "relative_start": 2.706028938293457, "end": **********.391113, "relative_end": **********.391113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.index", "start": **********.391791, "relative_start": 2.706707000732422, "end": **********.391791, "relative_end": **********.391791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.variants.segmented", "start": **********.392499, "relative_start": 2.7074148654937744, "end": **********.392499, "relative_end": **********.392499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.393157, "relative_start": 2.7080729007720947, "end": **********.393157, "relative_end": **********.393157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.394028, "relative_start": 2.7089438438415527, "end": **********.394028, "relative_end": **********.394028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.394433, "relative_start": 2.7093489170074463, "end": **********.394433, "relative_end": **********.394433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.395244, "relative_start": 2.7101597785949707, "end": **********.395244, "relative_end": **********.395244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.396251, "relative_start": 2.7111668586730957, "end": **********.396251, "relative_end": **********.396251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.cog", "start": **********.397064, "relative_start": 2.711979866027832, "end": **********.397064, "relative_end": **********.397064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.397665, "relative_start": 2.712580919265747, "end": **********.397665, "relative_end": **********.397665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.398304, "relative_start": 2.7132198810577393, "end": **********.398304, "relative_end": **********.398304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.398683, "relative_start": 2.7135989665985107, "end": **********.398683, "relative_end": **********.398683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.399109, "relative_start": 2.714024782180786, "end": **********.399109, "relative_end": **********.399109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.39984, "relative_start": 2.7147560119628906, "end": **********.39984, "relative_end": **********.39984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.400753, "relative_start": 2.7156689167022705, "end": **********.400753, "relative_end": **********.400753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.arrow-right-start-on-rectangle", "start": **********.401551, "relative_start": 2.7164669036865234, "end": **********.401551, "relative_end": **********.401551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.402154, "relative_start": 2.7170698642730713, "end": **********.402154, "relative_end": **********.402154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.index", "start": **********.402769, "relative_start": 2.7176849842071533, "end": **********.402769, "relative_end": **********.402769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown", "start": **********.403173, "relative_start": 2.7180888652801514, "end": **********.403173, "relative_end": **********.403173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.index", "start": **********.403654, "relative_start": 2.7185699939727783, "end": **********.403654, "relative_end": **********.403654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.backdrop", "start": **********.404891, "relative_start": 2.7198069095611572, "end": **********.404891, "relative_end": **********.404891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.toggle", "start": **********.406154, "relative_start": 2.721069812774658, "end": **********.406154, "relative_end": **********.406154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.407076, "relative_start": 2.721991777420044, "end": **********.407076, "relative_end": **********.407076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.408473, "relative_start": 2.723388910293579, "end": **********.408473, "relative_end": **********.408473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.panel-left", "start": **********.409104, "relative_start": 2.724020004272461, "end": **********.409104, "relative_end": **********.409104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.409695, "relative_start": 2.7246108055114746, "end": **********.409695, "relative_end": **********.409695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.410274, "relative_start": 2.7251899242401123, "end": **********.410274, "relative_end": **********.410274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.410864, "relative_start": 2.7257800102233887, "end": **********.410864, "relative_end": **********.410864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::profile", "start": **********.411353, "relative_start": 2.726269006729126, "end": **********.411353, "relative_end": **********.411353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::avatar.index", "start": **********.412123, "relative_start": 2.727038860321045, "end": **********.412123, "relative_end": **********.412123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.413331, "relative_start": 2.7282469272613525, "end": **********.413331, "relative_end": **********.413331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.413894, "relative_start": 2.7288098335266113, "end": **********.413894, "relative_end": **********.413894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.414512, "relative_start": 2.7294278144836426, "end": **********.414512, "relative_end": **********.414512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevrons-up-down", "start": **********.415033, "relative_start": 2.7299489974975586, "end": **********.415033, "relative_end": **********.415033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.415666, "relative_start": 2.7305819988250732, "end": **********.415666, "relative_end": **********.415666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.416021, "relative_start": 2.7309370040893555, "end": **********.416021, "relative_end": **********.416021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.416447, "relative_start": 2.731362819671631, "end": **********.416447, "relative_end": **********.416447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.417016, "relative_start": 2.7319319248199463, "end": **********.417016, "relative_end": **********.417016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.417591, "relative_start": 2.7325069904327393, "end": **********.417591, "relative_end": **********.417591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.418319, "relative_start": 2.7332348823547363, "end": **********.418319, "relative_end": **********.418319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.sun", "start": **********.418861, "relative_start": 2.733776807785034, "end": **********.418861, "relative_end": **********.418861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.419392, "relative_start": 2.7343080043792725, "end": **********.419392, "relative_end": **********.419392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.419935, "relative_start": 2.7348508834838867, "end": **********.419935, "relative_end": **********.419935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.420667, "relative_start": 2.7355828285217285, "end": **********.420667, "relative_end": **********.420667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.moon", "start": **********.421745, "relative_start": 2.736660957336426, "end": **********.421745, "relative_end": **********.421745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.422882, "relative_start": 2.737797975540161, "end": **********.422882, "relative_end": **********.422882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.423549, "relative_start": 2.738464832305908, "end": **********.423549, "relative_end": **********.423549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.424313, "relative_start": 2.7392289638519287, "end": **********.424313, "relative_end": **********.424313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.computer-desktop", "start": **********.424871, "relative_start": 2.7397868633270264, "end": **********.424871, "relative_end": **********.424871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.index", "start": **********.425409, "relative_start": 2.7403249740600586, "end": **********.425409, "relative_end": **********.425409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.variants.segmented", "start": **********.42593, "relative_start": 2.7408459186553955, "end": **********.42593, "relative_end": **********.42593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.426568, "relative_start": 2.7414839267730713, "end": **********.426568, "relative_end": **********.426568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.427469, "relative_start": 2.742384910583496, "end": **********.427469, "relative_end": **********.427469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.427903, "relative_start": 2.742818832397461, "end": **********.427903, "relative_end": **********.427903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.428474, "relative_start": 2.743389844894409, "end": **********.428474, "relative_end": **********.428474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.429375, "relative_start": 2.744290828704834, "end": **********.429375, "relative_end": **********.429375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.cog", "start": **********.42992, "relative_start": 2.74483585357666, "end": **********.42992, "relative_end": **********.42992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.430468, "relative_start": 2.7453839778900146, "end": **********.430468, "relative_end": **********.430468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.431092, "relative_start": 2.7460079193115234, "end": **********.431092, "relative_end": **********.431092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.431451, "relative_start": 2.7463669776916504, "end": **********.431451, "relative_end": **********.431451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.431872, "relative_start": 2.7467877864837646, "end": **********.431872, "relative_end": **********.431872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.432514, "relative_start": 2.747429847717285, "end": **********.432514, "relative_end": **********.432514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.433422, "relative_start": 2.748337984085083, "end": **********.433422, "relative_end": **********.433422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.arrow-right-start-on-rectangle", "start": **********.433951, "relative_start": 2.7488667964935303, "end": **********.433951, "relative_end": **********.433951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.434456, "relative_start": 2.7493720054626465, "end": **********.434456, "relative_end": **********.434456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.index", "start": **********.43503, "relative_start": 2.749945878982544, "end": **********.43503, "relative_end": **********.43503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown", "start": **********.43541, "relative_start": 2.750325918197632, "end": **********.43541, "relative_end": **********.43541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::header", "start": **********.435885, "relative_start": 2.750800848007202, "end": **********.435885, "relative_end": **********.435885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::toast.index", "start": **********.436852, "relative_start": 2.751767873764038, "end": **********.436852, "relative_end": **********.436852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.442686, "relative_start": 2.7576019763946533, "end": **********.442862, "relative_end": **********.442862, "duration": 0.00017595291137695312, "duration_str": "176μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 705, "nb_templates": 705, "templates": [{"name": "1x livewire.account-overview", "param_count": null, "params": [], "start": **********.095999, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/account-overview.blade.phplivewire.account-overview", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Faccount-overview.blade.php&line=1", "ajax": false, "filename": "account-overview.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.account-overview"}, {"name": "9x ********************************::heading", "param_count": null, "params": [], "start": **********.727927, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.php********************************::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 9, "name_original": "********************************::heading"}, {"name": "25x ********************************::button.index", "param_count": null, "params": [], "start": **********.729534, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.php********************************::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 25, "name_original": "********************************::button.index"}, {"name": "37x ********************************::icon.index", "param_count": null, "params": [], "start": **********.733298, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.php********************************::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 37, "name_original": "********************************::icon.index"}, {"name": "1x ********************************::icon.pencil-square", "param_count": null, "params": [], "start": **********.734822, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/pencil-square.blade.php********************************::icon.pencil-square", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fpencil-square.blade.php&line=1", "ajax": false, "filename": "pencil-square.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.pencil-square"}, {"name": "38x ********************************::button-or-link", "param_count": null, "params": [], "start": **********.735861, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.php********************************::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}, "render_count": 38, "name_original": "********************************::button-or-link"}, {"name": "27x ********************************::with-tooltip", "param_count": null, "params": [], "start": **********.736868, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.php********************************::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}, "render_count": 27, "name_original": "********************************::with-tooltip"}, {"name": "3x ********************************::modal.trigger", "param_count": null, "params": [], "start": **********.737828, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/trigger.blade.php********************************::modal.trigger", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Ftrigger.blade.php&line=1", "ajax": false, "filename": "trigger.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::modal.trigger"}, {"name": "1x livewire.account-form", "param_count": null, "params": [], "start": **********.742995, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/account-form.blade.phplivewire.account-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Faccount-form.blade.php&line=1", "ajax": false, "filename": "account-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.account-form"}, {"name": "50x ********************************::label", "param_count": null, "params": [], "start": **********.746663, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.php********************************::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 50, "name_original": "********************************::label"}, {"name": "3x ********************************::input.index", "param_count": null, "params": [], "start": **********.747637, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.php********************************::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::input.index"}, {"name": "8x ********************************::with-field", "param_count": null, "params": [], "start": **********.750439, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.php********************************::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}, "render_count": 8, "name_original": "********************************::with-field"}, {"name": "50x ********************************::error", "param_count": null, "params": [], "start": **********.75185, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.php********************************::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 50, "name_original": "********************************::error"}, {"name": "50x ********************************::field", "param_count": null, "params": [], "start": **********.752742, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.php********************************::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 50, "name_original": "********************************::field"}, {"name": "5x components.option", "param_count": null, "params": [], "start": **********.754477, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/option.blade.phpcomponents.option", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Foption.blade.php&line=1", "ajax": false, "filename": "option.blade.php", "line": "?"}, "render_count": 5, "name_original": "components.option"}, {"name": "1x components.select", "param_count": null, "params": [], "start": **********.757942, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/select.blade.phpcomponents.select", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.select"}, {"name": "1x components.delete-modal", "param_count": null, "params": [], "start": **********.772029, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/delete-modal.blade.phpcomponents.delete-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fdelete-modal.blade.php&line=1", "ajax": false, "filename": "delete-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.delete-modal"}, {"name": "1x ********************************::text", "param_count": null, "params": [], "start": **********.645213, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/text.blade.php********************************::text", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::text"}, {"name": "3x ********************************::spacer", "param_count": null, "params": [], "start": **********.736843, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/spacer.blade.php********************************::spacer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fspacer.blade.php&line=1", "ajax": false, "filename": "spacer.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::spacer"}, {"name": "6x ********************************::modal.close", "param_count": null, "params": [], "start": **********.740629, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/close.blade.php********************************::modal.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::modal.close"}, {"name": "4x ********************************::icon.loading", "param_count": null, "params": [], "start": **********.744457, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.php********************************::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::icon.loading"}, {"name": "4x ********************************::modal.index", "param_count": null, "params": [], "start": **********.747347, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/index.blade.php********************************::modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::modal.index"}, {"name": "6x ********************************::icon.x-mark", "param_count": null, "params": [], "start": **********.751021, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/x-mark.blade.php********************************::icon.x-mark", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fx-mark.blade.php&line=1", "ajax": false, "filename": "x-mark.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::icon.x-mark"}, {"name": "2x components.card", "param_count": null, "params": [], "start": **********.779454, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.card"}, {"name": "2x ********************************::card.index", "param_count": null, "params": [], "start": **********.781638, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/card/index.blade.php********************************::card.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::card.index"}, {"name": "1x livewire.transaction-table", "param_count": null, "params": [], "start": **********.827294, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/transaction-table.blade.phplivewire.transaction-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Ftransaction-table.blade.php&line=1", "ajax": false, "filename": "transaction-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.transaction-table"}, {"name": "1x ********************************::icon.plus", "param_count": null, "params": [], "start": **********.892123, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/plus.blade.php********************************::icon.plus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fplus.blade.php&line=1", "ajax": false, "filename": "plus.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.plus"}, {"name": "1x ********************************::icon.magnifying-glass", "param_count": null, "params": [], "start": **********.903193, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/magnifying-glass.blade.php********************************::icon.magnifying-glass", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmagnifying-glass.blade.php&line=1", "ajax": false, "filename": "magnifying-glass.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.magnifying-glass"}, {"name": "1x ********************************::input.clearable", "param_count": null, "params": [], "start": **********.910616, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/clearable.blade.php********************************::input.clearable", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Fclearable.blade.php&line=1", "ajax": false, "filename": "clearable.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::input.clearable"}, {"name": "1x components.filters", "param_count": null, "params": [], "start": **********.919096, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/filters.blade.phpcomponents.filters", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ffilters.blade.php&line=1", "ajax": false, "filename": "filters.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.filters"}, {"name": "1x ********************************::icon.funnel", "param_count": null, "params": [], "start": **********.927777, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/funnel.blade.php********************************::icon.funnel", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Ffunnel.blade.php&line=1", "ajax": false, "filename": "funnel.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.funnel"}, {"name": "4x ********************************::icon.chevron-down", "param_count": null, "params": [], "start": **********.944321, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-down.blade.php********************************::icon.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::icon.chevron-down"}, {"name": "17x ********************************::radio.index", "param_count": null, "params": [], "start": **********.946443, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/index.blade.php********************************::radio.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 17, "name_original": "********************************::radio.index"}, {"name": "11x ********************************::radio.variants.default", "param_count": null, "params": [], "start": **********.947956, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/variants/default.blade.php********************************::radio.variants.default", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fvariants%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}, "render_count": 11, "name_original": "********************************::radio.variants.default"}, {"name": "11x ********************************::radio.indicator", "param_count": null, "params": [], "start": **********.949532, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/indicator.blade.php********************************::radio.indicator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Findicator.blade.php&line=1", "ajax": false, "filename": "indicator.blade.php", "line": "?"}, "render_count": 11, "name_original": "********************************::radio.indicator"}, {"name": "47x ********************************::with-inline-field", "param_count": null, "params": [], "start": **********.950366, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-inline-field.blade.php********************************::with-inline-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-inline-field.blade.php&line=1", "ajax": false, "filename": "with-inline-field.blade.php", "line": "?"}, "render_count": 47, "name_original": "********************************::with-inline-field"}, {"name": "5x ********************************::radio.group.index", "param_count": null, "params": [], "start": **********.962267, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/index.blade.php********************************::radio.group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::radio.group.index"}, {"name": "3x ********************************::radio.group.variants.default", "param_count": null, "params": [], "start": **********.963127, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/variants/default.blade.php********************************::radio.group.variants.default", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Fvariants%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::radio.group.variants.default"}, {"name": "36x ********************************::checkbox.index", "param_count": null, "params": [], "start": **********.012824, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/checkbox/index.blade.php********************************::checkbox.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fcheckbox%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 36, "name_original": "********************************::checkbox.index"}, {"name": "36x ********************************::checkbox.variants.default", "param_count": null, "params": [], "start": **********.013908, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/checkbox/variants/default.blade.php********************************::checkbox.variants.default", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fcheckbox%2Fvariants%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}, "render_count": 36, "name_original": "********************************::checkbox.variants.default"}, {"name": "36x ********************************::checkbox.indicator", "param_count": null, "params": [], "start": **********.014739, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/checkbox/indicator.blade.php********************************::checkbox.indicator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fcheckbox%2Findicator.blade.php&line=1", "ajax": false, "filename": "indicator.blade.php", "line": "?"}, "render_count": 36, "name_original": "********************************::checkbox.indicator"}, {"name": "36x ********************************::icon.check", "param_count": null, "params": [], "start": **********.015613, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/check.blade.php********************************::icon.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}, "render_count": 36, "name_original": "********************************::icon.check"}, {"name": "36x ********************************::icon.minus", "param_count": null, "params": [], "start": **********.016408, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/minus.blade.php********************************::icon.minus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fminus.blade.php&line=1", "ajax": false, "filename": "minus.blade.php", "line": "?"}, "render_count": 36, "name_original": "********************************::icon.minus"}, {"name": "1x components.columns", "param_count": null, "params": [], "start": **********.230403, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/columns.blade.phpcomponents.columns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcolumns.blade.php&line=1", "ajax": false, "filename": "columns.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.columns"}, {"name": "1x ********************************::icon.view-columns", "param_count": null, "params": [], "start": **********.233357, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/view-columns.blade.php********************************::icon.view-columns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fview-columns.blade.php&line=1", "ajax": false, "filename": "view-columns.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.view-columns"}, {"name": "3x ********************************::menu.index", "param_count": null, "params": [], "start": **********.301581, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/index.blade.php********************************::menu.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::menu.index"}, {"name": "3x ********************************::dropdown", "param_count": null, "params": [], "start": **********.302077, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/dropdown.blade.php********************************::dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::dropdown"}, {"name": "1x livewire.attachments", "param_count": null, "params": [], "start": **********.306146, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/attachments.blade.phplivewire.attachments", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fattachments.blade.php&line=1", "ajax": false, "filename": "attachments.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.attachments"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.332487, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x components.layouts.app", "param_count": null, "params": [], "start": **********.333742, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app.blade.phpcomponents.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app"}, {"name": "1x ********************************::main", "param_count": null, "params": [], "start": **********.334706, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/main.blade.php********************************::main", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmain.blade.php&line=1", "ajax": false, "filename": "main.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::main"}, {"name": "1x components.layouts.app.sidebar", "param_count": null, "params": [], "start": **********.335439, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app/sidebar.blade.phpcomponents.layouts.app.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app.sidebar"}, {"name": "1x partials.head", "param_count": null, "params": [], "start": **********.33962, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/partials/head.blade.phppartials.head", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fpartials%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.head"}, {"name": "2x ********************************::sidebar.toggle", "param_count": null, "params": [], "start": **********.341065, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/toggle.blade.php********************************::sidebar.toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Ftoggle.blade.php&line=1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::sidebar.toggle"}, {"name": "1x components.app-logo", "param_count": null, "params": [], "start": **********.347428, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/app-logo.blade.phpcomponents.app-logo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fapp-logo.blade.php&line=1", "ajax": false, "filename": "app-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.app-logo"}, {"name": "7x ********************************::navlist.item", "param_count": null, "params": [], "start": **********.348575, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/item.blade.php********************************::navlist.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 7, "name_original": "********************************::navlist.item"}, {"name": "1x ********************************::icon.layout-dashboard", "param_count": null, "params": [], "start": **********.350194, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/layout-dashboard.blade.php********************************::icon.layout-dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Flayout-dashboard.blade.php&line=1", "ajax": false, "filename": "layout-dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.layout-dashboard"}, {"name": "1x ********************************::icon.user", "param_count": null, "params": [], "start": **********.353294, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/user.blade.php********************************::icon.user", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.user"}, {"name": "1x ********************************::icon.currency-dollar", "param_count": null, "params": [], "start": **********.357377, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/currency-dollar.blade.php********************************::icon.currency-dollar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcurrency-dollar.blade.php&line=1", "ajax": false, "filename": "currency-dollar.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.currency-dollar"}, {"name": "1x ********************************::icon.scroll-text", "param_count": null, "params": [], "start": **********.360411, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/scroll-text.blade.php********************************::icon.scroll-text", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fscroll-text.blade.php&line=1", "ajax": false, "filename": "scroll-text.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.scroll-text"}, {"name": "1x ********************************::icon.queue-list", "param_count": null, "params": [], "start": **********.363797, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/queue-list.blade.php********************************::icon.queue-list", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fqueue-list.blade.php&line=1", "ajax": false, "filename": "queue-list.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.queue-list"}, {"name": "1x ********************************::icon.tags", "param_count": null, "params": [], "start": **********.366812, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/tags.blade.php********************************::icon.tags", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ftags.blade.php&line=1", "ajax": false, "filename": "tags.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.tags"}, {"name": "1x ********************************::navlist.group", "param_count": null, "params": [], "start": **********.368014, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/navlist/group.blade.php********************************::navlist.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::navlist.group"}, {"name": "2x ********************************::navlist.index", "param_count": null, "params": [], "start": **********.368723, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/index.blade.php********************************::navlist.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::navlist.index"}, {"name": "1x ********************************::icon.folder-git-2", "param_count": null, "params": [], "start": **********.372037, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/folder-git-2.blade.php********************************::icon.folder-git-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ffolder-git-2.blade.php&line=1", "ajax": false, "filename": "folder-git-2.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.folder-git-2"}, {"name": "2x ********************************::profile", "param_count": null, "params": [], "start": **********.374477, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/profile.blade.php********************************::profile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fprofile.blade.php&line=1", "ajax": false, "filename": "profile.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::profile"}, {"name": "2x ********************************::avatar.index", "param_count": null, "params": [], "start": **********.375778, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/avatar/index.blade.php********************************::avatar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Favatar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::avatar.index"}, {"name": "2x ********************************::icon.chevrons-up-down", "param_count": null, "params": [], "start": **********.378939, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/chevrons-up-down.blade.php********************************::icon.chevrons-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevrons-up-down.blade.php&line=1", "ajax": false, "filename": "chevrons-up-down.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.chevrons-up-down"}, {"name": "4x ********************************::menu.radio.group", "param_count": null, "params": [], "start": **********.379843, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/radio/group.blade.php********************************::menu.radio.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fradio%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::menu.radio.group"}, {"name": "6x ********************************::menu.separator", "param_count": null, "params": [], "start": **********.380419, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/separator.blade.php********************************::menu.separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::menu.separator"}, {"name": "6x ********************************::separator", "param_count": null, "params": [], "start": **********.381172, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/separator.blade.php********************************::separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::separator"}, {"name": "6x ********************************::radio.variants.segmented", "param_count": null, "params": [], "start": **********.382565, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/variants/segmented.blade.php********************************::radio.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::radio.variants.segmented"}, {"name": "2x ********************************::icon.sun", "param_count": null, "params": [], "start": **********.384187, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/sun.blade.php********************************::icon.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.sun"}, {"name": "2x ********************************::icon.moon", "param_count": null, "params": [], "start": **********.386974, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/moon.blade.php********************************::icon.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.moon"}, {"name": "2x ********************************::icon.computer-desktop", "param_count": null, "params": [], "start": **********.391054, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/computer-desktop.blade.php********************************::icon.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.computer-desktop"}, {"name": "2x ********************************::radio.group.variants.segmented", "param_count": null, "params": [], "start": **********.392442, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/variants/segmented.blade.php********************************::radio.group.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::radio.group.variants.segmented"}, {"name": "4x ********************************::menu.item", "param_count": null, "params": [], "start": **********.395174, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/item.blade.php********************************::menu.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::menu.item"}, {"name": "2x ********************************::icon.cog", "param_count": null, "params": [], "start": **********.397005, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/cog.blade.php********************************::icon.cog", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcog.blade.php&line=1", "ajax": false, "filename": "cog.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.cog"}, {"name": "2x ********************************::icon.arrow-right-start-on-rectangle", "param_count": null, "params": [], "start": **********.401492, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/arrow-right-start-on-rectangle.blade.php********************************::icon.arrow-right-start-on-rectangle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Farrow-right-start-on-rectangle.blade.php&line=1", "ajax": false, "filename": "arrow-right-start-on-rectangle.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.arrow-right-start-on-rectangle"}, {"name": "1x ********************************::sidebar.index", "param_count": null, "params": [], "start": **********.403596, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/index.blade.php********************************::sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::sidebar.index"}, {"name": "1x ********************************::sidebar.backdrop", "param_count": null, "params": [], "start": **********.404756, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/backdrop.blade.php********************************::sidebar.backdrop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Fbackdrop.blade.php&line=1", "ajax": false, "filename": "backdrop.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::sidebar.backdrop"}, {"name": "1x ********************************::icon.panel-left", "param_count": null, "params": [], "start": **********.409046, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/panel-left.blade.php********************************::icon.panel-left", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fpanel-left.blade.php&line=1", "ajax": false, "filename": "panel-left.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.panel-left"}, {"name": "1x ********************************::header", "param_count": null, "params": [], "start": **********.435826, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/header.blade.php********************************::header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::header"}, {"name": "1x ********************************::toast.index", "param_count": null, "params": [], "start": **********.436758, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/toast/index.blade.php********************************::toast.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ftoast%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::toast.index"}]}, "queries": {"count": 11, "nb_statements": 10, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.011979999999999998, "accumulated_duration_str": "11.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.018025, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr' limit 1", "type": "query", "params": [], "bindings": ["kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.0287652, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 27.212}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.052837, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 27.212, "width_percent": 5.927}, {"sql": "select * from `accounts` where `slug` = 'tanvir-hossen-bappy' limit 1", "type": "query", "params": [], "bindings": ["tanvir-hossen-bappy"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 119}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 72}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 31}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php", "line": 211}], "start": **********.063203, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:119", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FDrawer%2FImplicitRouteBinding.php&line=119", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "119"}, "connection": "daily", "explain": null, "start_percent": 33.139, "width_percent": 5.426}, {"sql": "select `id`, (select sum(`transactions`.`amount`) from `transactions` where `accounts`.`id` = `transactions`.`account_id` and `type` in ('credit', 'deposit') and `status` = 1) as `cleared_deposits` from `accounts` where `accounts`.`id` in (5)", "type": "query", "params": [], "bindings": ["credit", "deposit", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Livewire/AccountOverview.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\AccountOverview.php", "line": 25}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.080382, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "AccountOverview.php:25", "source": {"index": 18, "namespace": null, "name": "app/Livewire/AccountOverview.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\AccountOverview.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FAccountOverview.php&line=25", "ajax": false, "filename": "AccountOverview.php", "line": "25"}, "connection": "daily", "explain": null, "start_percent": 38.564, "width_percent": 7.012}, {"sql": "select `id`, (select sum(`transactions`.`amount`) from `transactions` where `accounts`.`id` = `transactions`.`account_id` and `type` in ('debit', 'transfer', 'withdrawal') and `status` = 1) as `cleared_debits` from `accounts` where `accounts`.`id` in (5)", "type": "query", "params": [], "bindings": ["debit", "transfer", "withdrawal", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Livewire/AccountOverview.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\AccountOverview.php", "line": 29}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.084586, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "AccountOverview.php:29", "source": {"index": 18, "namespace": null, "name": "app/Livewire/AccountOverview.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\AccountOverview.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FAccountOverview.php&line=29", "ajax": false, "filename": "AccountOverview.php", "line": "29"}, "connection": "daily", "explain": null, "start_percent": 45.576, "width_percent": 6.594}, {"sql": "select count(*) as aggregate from `transactions` where `transactions`.`account_id` = 5 and `transactions`.`account_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "livewire.account-overview", "file": "C:\\laragon\\www\\pure-finance\\resources\\views/livewire/account-overview.blade.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.773265, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "livewire.account-overview:121", "source": {"index": 19, "namespace": "view", "name": "livewire.account-overview", "file": "C:\\laragon\\www\\pure-finance\\resources\\views/livewire/account-overview.blade.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Faccount-overview.blade.php&line=121", "ajax": false, "filename": "account-overview.blade.php", "line": "121"}, "connection": "daily", "explain": null, "start_percent": 52.17, "width_percent": 7.346}, {"sql": "select `id`, `name`, `parent_id` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/TransactionTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionTable.php", "line": 100}, {"index": 17, "namespace": null, "name": "app/Livewire/TransactionTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionTable.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.784678, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "TransactionTable.php:100", "source": {"index": 16, "namespace": null, "name": "app/Livewire/TransactionTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionTable.php", "line": 100}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionTable.php&line=100", "ajax": false, "filename": "TransactionTable.php", "line": "100"}, "connection": "daily", "explain": null, "start_percent": 59.516, "width_percent": 9.349}, {"sql": "select * from `categories` where `categories`.`parent_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30) order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Livewire/TransactionTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionTable.php", "line": 100}, {"index": 22, "namespace": null, "name": "app/Livewire/TransactionTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionTable.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.793642, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "TransactionTable.php:100", "source": {"index": 21, "namespace": null, "name": "app/Livewire/TransactionTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionTable.php", "line": 100}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionTable.php&line=100", "ajax": false, "filename": "TransactionTable.php", "line": "100"}, "connection": "daily", "explain": null, "start_percent": 68.865, "width_percent": 10.434}, {"sql": "select count(*) as aggregate from `transactions` where exists (select * from `accounts` where `transactions`.`account_id` = `accounts`.`id` and `user_id` = 1) and exists (select * from `accounts` where `transactions`.`account_id` = `accounts`.`id` and `name` = '<PERSON><PERSON>') and date(`date`) <= '2025-05-25'", "type": "query", "params": [], "bindings": [1, "<PERSON><PERSON>", "2025-05-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/TransactionTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionTable.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.812841, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "TransactionTable.php:230", "source": {"index": 16, "namespace": null, "name": "app/Livewire/TransactionTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionTable.php", "line": 230}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionTable.php&line=230", "ajax": false, "filename": "TransactionTable.php", "line": "230"}, "connection": "daily", "explain": null, "start_percent": 79.299, "width_percent": 14.608}, {"sql": "select count(*) as aggregate from `accounts` where `accounts`.`user_id` = 1 and `accounts`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "livewire.transaction-table", "file": "C:\\laragon\\www\\pure-finance\\resources\\views/livewire/transaction-table.blade.php", "line": 27}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.881634, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "livewire.transaction-table:27", "source": {"index": 19, "namespace": "view", "name": "livewire.transaction-table", "file": "C:\\laragon\\www\\pure-finance\\resources\\views/livewire/transaction-table.blade.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Ftransaction-table.blade.php&line=27", "ajax": false, "filename": "transaction-table.blade.php", "line": "27"}, "connection": "daily", "explain": null, "start_percent": 93.907, "width_percent": 6.093}]}, "models": {"data": {"App\\Models\\Category": {"value": 50, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Account": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FAccount.php&line=1", "ajax": false, "filename": "Account.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 54, "is_counter": true}, "livewire": {"data": {"account-overview #9CsLu96QtPf3phtjas5j": "array:4 [\n  \"data\" => array:3 [\n    \"account\" => App\\Models\\Account {#1640\n      #connection: \"mysql\"\n      #table: \"accounts\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:12 [\n        \"id\" => 5\n        \"user_id\" => 1\n        \"type\" => \"investment\"\n        \"name\" => \"Tanvir Hossen Bappy\"\n        \"slug\" => \"tanvir-hossen-bappy\"\n        \"balance\" => 0.0\n        \"initial_balance\" => 32500.0\n        \"created_at\" => \"2025-05-25 10:10:31\"\n        \"updated_at\" => \"2025-05-25 10:10:31\"\n        \"cleared_deposits\" => null\n        \"cleared_debits\" => null\n        \"cleared_balance\" => 32500.0\n      ]\n      #original: array:11 [\n        \"id\" => 5\n        \"user_id\" => 1\n        \"type\" => \"investment\"\n        \"name\" => \"Tanvir Hossen Bappy\"\n        \"slug\" => \"tanvir-hossen-bappy\"\n        \"balance\" => 0.0\n        \"initial_balance\" => 32500.0\n        \"created_at\" => \"2025-05-25 10:10:31\"\n        \"updated_at\" => \"2025-05-25 10:10:31\"\n        \"cleared_deposits\" => null\n        \"cleared_debits\" => null\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:2 [\n        \"type\" => \"App\\Enums\\AccountType\"\n        \"id\" => \"int\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:6 [\n        0 => \"user_id\"\n        1 => \"type\"\n        2 => \"name\"\n        3 => \"slug\"\n        4 => \"balance\"\n        5 => \"initial_balance\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"cleared_balance\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"account-overview\"\n  \"component\" => \"App\\Livewire\\AccountOverview\"\n  \"id\" => \"9CsLu96QtPf3phtjas5j\"\n]", "account-form #1fWEZmOqnx7FFA4rlyPT": "array:4 [\n  \"data\" => array:4 [\n    \"account\" => App\\Models\\Account {#1640\n      #connection: \"mysql\"\n      #table: \"accounts\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:12 [\n        \"id\" => 5\n        \"user_id\" => 1\n        \"type\" => \"investment\"\n        \"name\" => \"Tanvir Hossen Bappy\"\n        \"slug\" => \"tanvir-hossen-bappy\"\n        \"balance\" => 0.0\n        \"initial_balance\" => 32500.0\n        \"created_at\" => \"2025-05-25 10:10:31\"\n        \"updated_at\" => \"2025-05-25 10:10:31\"\n        \"cleared_deposits\" => null\n        \"cleared_debits\" => null\n        \"cleared_balance\" => 32500.0\n      ]\n      #original: array:11 [\n        \"id\" => 5\n        \"user_id\" => 1\n        \"type\" => \"investment\"\n        \"name\" => \"Tanvir Hossen Bappy\"\n        \"slug\" => \"tanvir-hossen-bappy\"\n        \"balance\" => 0.0\n        \"initial_balance\" => 32500.0\n        \"created_at\" => \"2025-05-25 10:10:31\"\n        \"updated_at\" => \"2025-05-25 10:10:31\"\n        \"cleared_deposits\" => null\n        \"cleared_debits\" => null\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:2 [\n        \"type\" => \"App\\Enums\\AccountType\"\n        \"id\" => \"int\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:6 [\n        0 => \"user_id\"\n        1 => \"type\"\n        2 => \"name\"\n        3 => \"slug\"\n        4 => \"balance\"\n        5 => \"initial_balance\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"name\" => \"Tanvir Hossen Bappy\"\n    \"type\" => App\\Enums\\AccountType {#1944\n      +name: \"INVESTMENT\"\n      +value: \"investment\"\n    }\n    \"initial_balance\" => 32500.0\n  ]\n  \"name\" => \"account-form\"\n  \"component\" => \"App\\Livewire\\AccountForm\"\n  \"id\" => \"1fWEZmOqnx7FFA4rlyPT\"\n]", "transaction-table #YHSF7imKMSLCcWncxYeq": "array:4 [\n  \"data\" => array:13 [\n    \"account\" => App\\Models\\Account {#1640\n      #connection: \"mysql\"\n      #table: \"accounts\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: true\n      #attributes: array:12 [\n        \"id\" => 5\n        \"user_id\" => 1\n        \"type\" => \"investment\"\n        \"name\" => \"Tanvir Hossen Bappy\"\n        \"slug\" => \"tanvir-hossen-bappy\"\n        \"balance\" => 0.0\n        \"initial_balance\" => 32500.0\n        \"created_at\" => \"2025-05-25 10:10:31\"\n        \"updated_at\" => \"2025-05-25 10:10:31\"\n        \"cleared_deposits\" => null\n        \"cleared_debits\" => null\n        \"cleared_balance\" => 32500.0\n      ]\n      #original: array:11 [\n        \"id\" => 5\n        \"user_id\" => 1\n        \"type\" => \"investment\"\n        \"name\" => \"Tanvir Hossen Bappy\"\n        \"slug\" => \"tanvir-hossen-bappy\"\n        \"balance\" => 0.0\n        \"initial_balance\" => 32500.0\n        \"created_at\" => \"2025-05-25 10:10:31\"\n        \"updated_at\" => \"2025-05-25 10:10:31\"\n        \"cleared_deposits\" => null\n        \"cleared_debits\" => null\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:2 [\n        \"type\" => \"App\\Enums\\AccountType\"\n        \"id\" => \"int\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:6 [\n        0 => \"user_id\"\n        1 => \"type\"\n        2 => \"name\"\n        3 => \"slug\"\n        4 => \"balance\"\n        5 => \"initial_balance\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"search\" => \"\"\n    \"status\" => null\n    \"transaction_type\" => \"\"\n    \"accounts\" => null\n    \"selected_accounts\" => []\n    \"categories\" => array:30 [\n      0 => array:4 [\n        \"id\" => 28\n        \"name\" => \"Airfare\"\n        \"parent_id\" => 9\n        \"children\" => []\n      ]\n      1 => array:4 [\n        \"id\" => 1\n        \"name\" => \"Auto & Transport 2\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 11\n            \"user_id\" => 1\n            \"name\" => \"Car Insurance\"\n            \"parent_id\" => 1\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 12\n            \"user_id\" => 1\n            \"name\" => \"Car Payment\"\n            \"parent_id\" => 1\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      2 => array:4 [\n        \"id\" => 22\n        \"name\" => \"Bonus\"\n        \"parent_id\" => 6\n        \"children\" => []\n      ]\n      3 => array:4 [\n        \"id\" => 11\n        \"name\" => \"Car Insurance\"\n        \"parent_id\" => 1\n        \"children\" => []\n      ]\n      4 => array:4 [\n        \"id\" => 12\n        \"name\" => \"Car Payment\"\n        \"parent_id\" => 1\n        \"children\" => []\n      ]\n      5 => array:4 [\n        \"id\" => 25\n        \"name\" => \"Clothing\"\n        \"parent_id\" => 8\n        \"children\" => []\n      ]\n      6 => array:4 [\n        \"id\" => 17\n        \"name\" => \"Doctor\"\n        \"parent_id\" => 4\n        \"children\" => []\n      ]\n      7 => array:4 [\n        \"id\" => 30\n        \"name\" => \"Electric\"\n        \"parent_id\" => 10\n        \"children\" => []\n      ]\n      8 => array:4 [\n        \"id\" => 13\n        \"name\" => \"Fast Food\"\n        \"parent_id\" => 2\n        \"children\" => []\n      ]\n      9 => array:4 [\n        \"id\" => 2\n        \"name\" => \"Food\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 13\n            \"user_id\" => 1\n            \"name\" => \"Fast Food\"\n            \"parent_id\" => 2\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 14\n            \"user_id\" => 1\n            \"name\" => \"Restaurants\"\n            \"parent_id\" => 2\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      10 => array:4 [\n        \"id\" => 29\n        \"name\" => \"Gas\"\n        \"parent_id\" => 10\n        \"children\" => []\n      ]\n      11 => array:4 [\n        \"id\" => 26\n        \"name\" => \"Gifts\"\n        \"parent_id\" => 8\n        \"children\" => []\n      ]\n      12 => array:4 [\n        \"id\" => 19\n        \"name\" => \"Haircut\"\n        \"parent_id\" => 5\n        \"children\" => []\n      ]\n      13 => array:4 [\n        \"id\" => 4\n        \"name\" => \"Health\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 17\n            \"user_id\" => 1\n            \"name\" => \"Doctor\"\n            \"parent_id\" => 4\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 18\n            \"user_id\" => 1\n            \"name\" => \"Pharmacy\"\n            \"parent_id\" => 4\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      14 => array:4 [\n        \"id\" => 3\n        \"name\" => \"Home\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 15\n            \"user_id\" => 1\n            \"name\" => \"Mortgage\"\n            \"parent_id\" => 3\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 16\n            \"user_id\" => 1\n            \"name\" => \"Rent\"\n            \"parent_id\" => 3\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      15 => array:4 [\n        \"id\" => 27\n        \"name\" => \"Hotel\"\n        \"parent_id\" => 9\n        \"children\" => []\n      ]\n      16 => array:4 [\n        \"id\" => 20\n        \"name\" => \"Laundry\"\n        \"parent_id\" => 5\n        \"children\" => []\n      ]\n      17 => array:4 [\n        \"id\" => 15\n        \"name\" => \"Mortgage\"\n        \"parent_id\" => 3\n        \"children\" => []\n      ]\n      18 => array:4 [\n        \"id\" => 21\n        \"name\" => \"Paycheck\"\n        \"parent_id\" => 6\n        \"children\" => []\n      ]\n      19 => array:4 [\n        \"id\" => 5\n        \"name\" => \"Personal Care\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 19\n            \"user_id\" => 1\n            \"name\" => \"Haircut\"\n            \"parent_id\" => 5\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 20\n            \"user_id\" => 1\n            \"name\" => \"Laundry\"\n            \"parent_id\" => 5\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      20 => array:4 [\n        \"id\" => 6\n        \"name\" => \"Personal Income\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 22\n            \"user_id\" => 1\n            \"name\" => \"Bonus\"\n            \"parent_id\" => 6\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 21\n            \"user_id\" => 1\n            \"name\" => \"Paycheck\"\n            \"parent_id\" => 6\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      21 => array:4 [\n        \"id\" => 23\n        \"name\" => \"Pet Food\"\n        \"parent_id\" => 7\n        \"children\" => []\n      ]\n      22 => array:4 [\n        \"id\" => 7\n        \"name\" => \"Pets\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 23\n            \"user_id\" => 1\n            \"name\" => \"Pet Food\"\n            \"parent_id\" => 7\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 24\n            \"user_id\" => 1\n            \"name\" => \"Veterinary\"\n            \"parent_id\" => 7\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      23 => array:4 [\n        \"id\" => 18\n        \"name\" => \"Pharmacy\"\n        \"parent_id\" => 4\n        \"children\" => []\n      ]\n      24 => array:4 [\n        \"id\" => 16\n        \"name\" => \"Rent\"\n        \"parent_id\" => 3\n        \"children\" => []\n      ]\n      25 => array:4 [\n        \"id\" => 14\n        \"name\" => \"Restaurants\"\n        \"parent_id\" => 2\n        \"children\" => []\n      ]\n      26 => array:4 [\n        \"id\" => 8\n        \"name\" => \"Shopping\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 25\n            \"user_id\" => 1\n            \"name\" => \"Clothing\"\n            \"parent_id\" => 8\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 26\n            \"user_id\" => 1\n            \"name\" => \"Gifts\"\n            \"parent_id\" => 8\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      27 => array:4 [\n        \"id\" => 9\n        \"name\" => \"Travel\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 28\n            \"user_id\" => 1\n            \"name\" => \"Airfare\"\n            \"parent_id\" => 9\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 27\n            \"user_id\" => 1\n            \"name\" => \"Hotel\"\n            \"parent_id\" => 9\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      28 => array:4 [\n        \"id\" => 10\n        \"name\" => \"Utilities\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 30\n            \"user_id\" => 1\n            \"name\" => \"Electric\"\n            \"parent_id\" => 10\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 29\n            \"user_id\" => 1\n            \"name\" => \"Gas\"\n            \"parent_id\" => 10\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      29 => array:4 [\n        \"id\" => 24\n        \"name\" => \"Veterinary\"\n        \"parent_id\" => 7\n        \"children\" => []\n      ]\n    ]\n    \"selected_categories\" => []\n    \"columns\" => array:6 [\n      0 => \"date\"\n      1 => \"category\"\n      2 => \"type\"\n      3 => \"amount\"\n      4 => \"payee\"\n      5 => \"status\"\n    ]\n    \"date\" => \"\"\n    \"sort_col\" => \"date\"\n    \"sort_direction\" => \"desc\"\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"transaction-table\"\n  \"component\" => \"App\\Livewire\\TransactionTable\"\n  \"id\" => \"YHSF7imKMSLCcWncxYeq\"\n]", "attachments #17Qmka3YXIA2cYBgG0hn": "array:4 [\n  \"data\" => array:2 [\n    \"show_attachments\" => false\n    \"attachments\" => []\n  ]\n  \"name\" => \"attachments\"\n  \"component\" => \"App\\Livewire\\Attachments\"\n  \"id\" => \"17Qmka3YXIA2cYBgG0hn\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => update,\n  target => App\\Models\\Account(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Account)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Account(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\Account(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Account)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.069662, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/account-overview/tanvir-hossen-bappy", "action_name": "account-overview", "controller_action": "App\\Livewire\\AccountOverview", "uri": "GET account-overview/{account}", "controller": "App\\Livewire\\AccountOverview@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FAccountOverview.php&line=22\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FAccountOverview.php&line=22\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/AccountOverview.php:22-38</a>", "middleware": "web, auth, can:update,account", "duration": "2.76s", "peak_memory": "34MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire-navigate</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/accounts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"751 characters\">PHPSESSID=aa0mh29tpvppc77i3l7peen7g0; XSRF-TOKEN=eyJpdiI6IndSY09XUEltUGxhSkliUktFQjF6c3c9PSIsInZhbHVlIjoiQ2Zqb3Nua0FBUDIydFR3eUNKREQ3VGFGYklkS3JHWDY2YlhOMlBNVTl0MVJMcklEQVB0aE1kdlFvOXZJSXpKMnh3MWJtVEIzdUhYbWZMbzdIakszYlNWNEtGNDVjY3FVUnlacVdHSkgyM2dic0Z0ekpWK001QStqOUNoZlVHWGQiLCJtYWMiOiI2YjM5OGZkZGE0MjdjNThmZGY2MDFmODA4NTgyOTc2ODY5NmY2ZjJiM2U2MTYxZWNjYzBiMGRiZjk5ZWMzZjk1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkpSMmYrSnZVM2QrS2xIbnJQRVhyNHc9PSIsInZhbHVlIjoiQnZuam1jemZxMXJlSkFtMkMyTGVrNGNIN3lLc1gvLzJNMm9lMzdNcnppVmhNZVJ5SXFMLzN3dHJxTXc1L3FtRE1BT2NjcFMycGZEdFhwSzA3d1BTbE91NldWN2RkdG5ENnFVbXZLNHcxeVk3cFZ3bTJFRTdhSUZ0TlZablpualAiLCJtYWMiOiJmMzQ1MjNjODA1OGI0M2Y5OWUzODNhM2NiZTIxNjZiMTZkOGMzMDYzZDA0YTJiMDljYmRhMGU0M2MwNDNmYjVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1747435169 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1747435169\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2090492438 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 25 May 2025 10:10:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2090492438\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/accounts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/account-overview/tanvir-hossen-bappy", "action_name": "account-overview", "controller_action": "App\\Livewire\\AccountOverview"}, "badge": null}}