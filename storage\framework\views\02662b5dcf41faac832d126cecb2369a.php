<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'key' => null,
    'class' => '',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'key' => null,
    'class' => '',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$classes = 'border-b border-zinc-200 dark:border-white/20 hover:bg-zinc-50 dark:hover:bg-zinc-800/50 transition-colors ' . $class;
?>

<tr <?php echo e($attributes->merge(['class' => $classes])); ?> <?php if($key): ?> wire:key="<?php echo e($key); ?>" <?php endif; ?>>
    <?php echo e($slot); ?>

</tr>
<?php /**PATH C:\laragon\www\pure-finance\resources\views/components/table/row.blade.php ENDPATH**/ ?>