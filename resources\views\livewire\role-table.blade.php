<div class="space-y-4 mx-auto max-w-6xl">
    <div class="flex items-center justify-between">
        <flux:heading size="xl">
            Roles
        </flux:heading>

        <div>
            <flux:modal.trigger name="role-form">
                <flux:button icon="plus" variant="primary" size="sm">
                    Add Role
                </flux:button>
            </flux:modal.trigger>
            
            <livewire:role-form data-name="role-form" />
        </div>
    </div>

    <x-card>
        <x-slot:content>
            <div class="p-3">
                <flux:input icon="magnifying-glass" placeholder="Search roles..." wire:model.live.debounce.300ms='search' clearable />
            </div>

            @if ($roles->count() > 0)
                <x-table :paginate="$roles" class="border-t border-zinc-200 dark:border-white/20">
                    <x-table.columns class="[&>tr>th]:px-3! bg-zinc-50 dark:bg-zinc-800">
                        <x-table.column>
                            Name
                        </x-table.column>

                        <x-table.column>
                            Guard
                        </x-table.column>

                        <x-table.column>
                            Permissions
                        </x-table.column>

                        <x-table.column>
                            Users
                        </x-table.column>

                        <x-table.column class="[&>div]:justify-end!">
                            Actions
                        </x-table.column>
                    </x-table.columns>

                    <x-table.rows class="dark:bg-zinc-900">
                        @foreach ($roles as $role)
                            <x-table.row :key="$role->id" class="[&>td]:px-3! [&>td]:py-2!">
                                <x-table.cell class="whitespace-nowrap" variant="strong">
                                    {{ $role->name }}
                                </x-table.cell>

                                <x-table.cell class="whitespace-nowrap">
                                    {{ $role->guard_name }}
                                </x-table.cell>

                                <x-table.cell class="whitespace-nowrap">
                                    <flux:badge color="blue" size="sm">
                                        {{ $role->permissions_count }} permissions
                                    </flux:badge>
                                </x-table.cell>

                                <x-table.cell class="whitespace-nowrap">
                                    <flux:badge color="green" size="sm">
                                        {{ $role->users_count }} users
                                    </flux:badge>
                                </x-table.cell>

                                <x-table.cell class="[&>div]:justify-end!">
                                    <div class="flex items-center">
                                        <div>
                                            <flux:modal.trigger name="edit-role-{{ $role->id }}">
                                                <flux:button icon="pencil-square" variant="ghost" size="sm"
                                                    class="text-indigo-500!" 
                                                    x-on:click="$dispatch('load-role', { role: {{ $role }} })" />
                                            </flux:modal.trigger>

                                            <livewire:role-form data-name="edit-role-{{ $role->id }}" :key="$role->id" />
                                        </div>

                                        @if($role->name !== 'super-admin')
                                            <div>
                                                <flux:modal.trigger name="delete-role-{{ $role->id }}">
                                                    <flux:button icon="trash" variant="ghost" size="sm"
                                                        class="text-red-500!" />
                                                </flux:modal.trigger>

                                                <flux:modal name="delete-role-{{ $role->id }}" class="min-w-[22rem]">
                                                    <form wire:submit="delete({{ $role->id }})" class="space-y-6">
                                                        <div class="space-y-4!">
                                                            <flux:heading size="lg" class="font-semibold -mt-1.5!">
                                                                Delete Role
                                                            </flux:heading>

                                                            <flux:subheading>
                                                                Are you sure you want to delete the "{{ $role->name }}" role? This action cannot be undone.
                                                            </flux:subheading>
                                                        </div>

                                                        <div class="flex gap-2">
                                                            <flux:spacer />

                                                            <flux:modal.close>
                                                                <flux:button variant="ghost" size="sm">
                                                                    Cancel
                                                                </flux:button>
                                                            </flux:modal.close>

                                                            <flux:button type="submit" variant="danger" size="sm">
                                                                Confirm
                                                            </flux:button>
                                                        </div>
                                                    </form>
                                                </flux:modal>
                                            </div>
                                        @endif
                                    </div>
                                </x-table.cell>
                            </x-table.row>
                        @endforeach
                    </x-table.rows>
                </x-table>
            @else
                <flux:heading class="italic! font-medium text-center pb-3">
                    No roles found...
                </flux:heading>
            @endif
        </x-slot:content>
    </x-card>
</div>
