<div>
    <flux:modal name="{{ $permission ? 'edit-permission-' . $permission['id'] : 'create-permission' }}">
        <div x-cloak wire:loading.remove class="space-y-6">
            <flux:heading size="lg" class="font-semibold -mt-1.5!">
                {{ $permission ? 'Edit' : 'Create' }} Permission
            </flux:heading>

            <div class="space-y-4">
                <flux:field>
                    <flux:label>Name</flux:label>

                    <flux:input type="text" wire:model='name' required />

                    <flux:error name="name" />
                </flux:field>

                <flux:field>
                    <flux:label>Guard Name</flux:label>

                    <x-select wire:model='guard_name' required>
                        <x-option value="web">Web</x-option>
                        <x-option value="api">API</x-option>
                    </x-select>

                    <flux:error name="guard_name" />
                </flux:field>
            </div>

            <div class="flex gap-2">
                <flux:spacer />

                   <flux:modal.close>
                        <flux:button variant="ghost" size="sm">
                            Cancel
                        </flux:button>
                    </flux:modal.close>

                <flux:button type="button" wire:click='submit' variant="primary" size="sm">
                    Save
                </flux:button>
            </div>
        </div>

        <div x-cloak wire:loading.flex class="flex items-center justify-center w-full h-[300px]">
            <flux:icon.loading />
        </div>
    </flux:modal>
</div>
