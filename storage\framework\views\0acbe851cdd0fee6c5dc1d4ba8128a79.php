<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name' => $attributes->whereStartsWith('wire:model')->first(),
    'placeholder' => null,
    'invalid' => null,
    'size' => null,
    'multiple' => false,
    'clearable' => false,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name' => $attributes->whereStartsWith('wire:model')->first(),
    'placeholder' => null,
    'invalid' => null,
    'size' => null,
    'multiple' => false,
    'clearable' => false,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$invalid ??= ($name && $errors->has($name));

$classes = 'appearance-none w-full pl-3 pr-10 block border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-zinc-900 dark:text-zinc-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:focus:ring-indigo-400 dark:focus:border-indigo-400 ' .
           match ($size) {
               'sm' => 'h-8 py-1.5 text-sm leading-none rounded-md',
               'xs' => 'h-6 text-xs leading-none rounded-md',
               default => 'h-10 py-2 text-base sm:text-sm leading-none rounded-lg',
           } . ' ' .
           ($invalid ? 'border-red-500 dark:border-red-400 focus:ring-red-500 focus:border-red-500' : '');
?>

<div class="relative">
    <select
        <?php echo e($attributes->class($classes)); ?>

        <?php if($invalid): ?> aria-invalid="true" <?php endif; ?>
        <?php if($name): ?> name="<?php echo e($name); ?>" <?php endif; ?>
        <?php if($multiple): ?> multiple <?php endif; ?>
    >
        <!--[if BLOCK]><![endif]--><?php if($placeholder && !$multiple): ?>
            <option value="" disabled selected><?php echo e($placeholder); ?></option>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        
        <?php echo e($slot); ?>

    </select>
    
    <!--[if BLOCK]><![endif]--><?php if(!$multiple): ?>
        <!-- Dropdown arrow -->
        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg class="w-4 h-4 text-zinc-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    
    <!--[if BLOCK]><![endif]--><?php if($clearable && !$multiple): ?>
        <!-- Clear button -->
        <button 
            type="button" 
            class="absolute inset-y-0 right-8 flex items-center pr-1 text-zinc-400 hover:text-zinc-600 dark:hover:text-zinc-300"
            onclick="this.parentElement.querySelector('select').value = ''; this.parentElement.querySelector('select').dispatchEvent(new Event('change', { bubbles: true }));"
        >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH C:\laragon\www\pure-finance\resources\views/components/select.blade.php ENDPATH**/ ?>