<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['transaction']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['transaction']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php use \App\Enums\TransactionType as TransactionType; ?>

<div wire:key='<?php echo e($transaction->id); ?>' x-data="transaction" x-on:transaction-deleted.window="resetSwipe"
    x-on:status-changed.window="resetSwipe" x-on:click.outside="resetSwipe"
    class="relative overflow-hidden">
    <div x-show="leftSwipe" x-transition:enter="transform duration-200" x-transition:enter-start="translate-x-full"
        x-transition:enter-end="translate-x-0" x-transition:leave="transform duration-200"
        x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full"
        class="<?php echo \Illuminate\Support\Arr::toCssClasses([
            'absolute top-0 bottom-0 right-0 flex items-center text-white border',
            'bg-emerald-400 border-emerald-500 dark:bg-emerald-600/80' => !$transaction->status,
            'bg-amber-400 border-amber-500 dark:bg-amber-600/80' =>
                $transaction->status,
        ]); ?>">
        <?php if (isset($component)) { $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::button.index','data' => ['class' => '!p-0 hover:!bg-transparent','variant' => 'ghost','wire:click' => 'toggleStatus('.e($transaction->id).')']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => '!p-0 hover:!bg-transparent','variant' => 'ghost','wire:click' => 'toggleStatus('.e($transaction->id).')']); ?>
            <!--[if BLOCK]><![endif]--><?php if($transaction->status): ?>
                <?php if (isset($component)) { $__componentOriginald608ca80896d43e1a2b4f714caad87ff = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald608ca80896d43e1a2b4f714caad87ff = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.clock-alert','data' => ['class' => 'w-[44px] p-1 ease-in-out rounded-md text-amber-500 h-7']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon.clock-alert'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-[44px] p-1 ease-in-out rounded-md text-amber-500 h-7']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald608ca80896d43e1a2b4f714caad87ff)): ?>
<?php $attributes = $__attributesOriginald608ca80896d43e1a2b4f714caad87ff; ?>
<?php unset($__attributesOriginald608ca80896d43e1a2b4f714caad87ff); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald608ca80896d43e1a2b4f714caad87ff)): ?>
<?php $component = $__componentOriginald608ca80896d43e1a2b4f714caad87ff; ?>
<?php unset($__componentOriginald608ca80896d43e1a2b4f714caad87ff); ?>
<?php endif; ?>
            <?php else: ?>
                <?php if (isset($component)) { $__componentOriginal99e1287553cbf55f278732425b3f00bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal99e1287553cbf55f278732425b3f00bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.check-circle','data' => ['class' => 'w-[44px] p-1 ease-in-out rounded-md text-emerald-500 h-8']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon.check-circle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-[44px] p-1 ease-in-out rounded-md text-emerald-500 h-8']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal99e1287553cbf55f278732425b3f00bd)): ?>
<?php $attributes = $__attributesOriginal99e1287553cbf55f278732425b3f00bd; ?>
<?php unset($__attributesOriginal99e1287553cbf55f278732425b3f00bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal99e1287553cbf55f278732425b3f00bd)): ?>
<?php $component = $__componentOriginal99e1287553cbf55f278732425b3f00bd; ?>
<?php unset($__componentOriginal99e1287553cbf55f278732425b3f00bd); ?>
<?php endif; ?>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $attributes = $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $component = $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
    </div>

    <a href="<?php echo e(route('edit-transaction', $transaction)); ?>" class="<?php echo \Illuminate\Support\Arr::toCssClasses([
        'flex flex-col p-3 py-2.5 text-sm bg-white dark:bg-zinc-900 transform transition-transform duration-300',
        'border-r-2 !border-r-emerald-500' => $transaction->status === true,
        'border-r-2 !border-r-amber-500' => $transaction->status === false,
    ]); ?>"
        x-bind:style="contentStyle">
        <div class="flex items-center justify-between font-medium">
            <p class="text-zinc-700 truncate max-w-[215px] dark:text-zinc-200">
                <?php echo e($transaction->payee); ?>

            </p>

            <div class="flex items-center">
                <!--[if BLOCK]><![endif]--><?php if($transaction->attachments): ?> 
                    <?php if (isset($component)) { $__componentOriginal1db8c57e729d67f7d4103875cf3230cb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1db8c57e729d67f7d4103875cf3230cb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger','data' => ['name' => 'attachments']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::modal.trigger'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'attachments']); ?>
                        <div x-data="{ attachments: <?php echo \Illuminate\Support\Js::from($transaction->attachments)->toHtml() ?> }">
                            <?php if (isset($component)) { $__componentOriginal2d7605e1adbee8a1737ebec29a91da61 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2d7605e1adbee8a1737ebec29a91da61 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.photo','data' => ['class' => '!text-zinc-600 !h-5 mr-1.5 dark:!text-zinc-100','xOn:click.prevent' => '$dispatch(\'load-attachments\', { attachments })']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon.photo'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => '!text-zinc-600 !h-5 mr-1.5 dark:!text-zinc-100','x-on:click.prevent' => '$dispatch(\'load-attachments\', { attachments })']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2d7605e1adbee8a1737ebec29a91da61)): ?>
<?php $attributes = $__attributesOriginal2d7605e1adbee8a1737ebec29a91da61; ?>
<?php unset($__attributesOriginal2d7605e1adbee8a1737ebec29a91da61); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2d7605e1adbee8a1737ebec29a91da61)): ?>
<?php $component = $__componentOriginal2d7605e1adbee8a1737ebec29a91da61; ?>
<?php unset($__componentOriginal2d7605e1adbee8a1737ebec29a91da61); ?>
<?php endif; ?>
                        </div>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1db8c57e729d67f7d4103875cf3230cb)): ?>
<?php $attributes = $__attributesOriginal1db8c57e729d67f7d4103875cf3230cb; ?>
<?php unset($__attributesOriginal1db8c57e729d67f7d4103875cf3230cb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1db8c57e729d67f7d4103875cf3230cb)): ?>
<?php $component = $__componentOriginal1db8c57e729d67f7d4103875cf3230cb; ?>
<?php unset($__componentOriginal1db8c57e729d67f7d4103875cf3230cb); ?>
<?php endif; ?>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <div class="flex items-center">
                    <!--[if BLOCK]><![endif]--><?php if(in_array($transaction->type, [TransactionType::DEBIT, TransactionType::TRANSFER, TransactionType::WITHDRAWAL])): ?>
                        <span class="text-zinc-700 dark:text-zinc-200">-</span>
                    <?php else: ?>
                        <span class="text-emerald-500">+</span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <span class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                        '!text-emerald-500' => in_array($transaction->type, [
                            TransactionType::CREDIT,
                            TransactionType::DEPOSIT,
                        ]),
                        'text-zinc-700 dark:text-zinc-200'
                    ]); ?>">
                        $<?php echo e(Number::format($transaction->amount ?? 0, 2)); ?>

                    </span>
                </div>
            </div>
        </div>

        <div class="flex items-center justify-between text-zinc-500 dark:text-zinc-300">
            <div class="flex items-center">
                <p class="max-w-[215px] truncate">
                    <!--[if BLOCK]><![endif]--><?php if($transaction->category->parent): ?>
                        <?php echo e($transaction->category->parent->name); ?> &rarr; <?php echo e($transaction->category->name); ?>

                    <?php else: ?>
                        <?php echo e($transaction->category->name); ?>

                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </p>
            </div>

            <p>
                <?php echo e(Carbon\Carbon::parse($transaction->date)->format('M j, Y')); ?>

            </p>
        </div>

        <!--[if BLOCK]><![endif]--><?php if($transaction->tags): ?>
            <div class="text-zinc-500 dark:text-zinc-300">
                <?php echo e($transaction->tags->pluck('name')->implode(', ')); ?>

            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </a>
</div>

    <?php
        $__scriptKey = '3774330832-0';
        ob_start();
    ?>
    <script>
        Alpine.data('transaction', () => {
            return {
                leftSwipe: false,
                startX: 0,
                currentX: 0,

                get contentStyle() {
                    return `transform: translateX(${
                        this.leftSwipe ? '-44px' : '0px'
                    })`;
                },

                resetSwipe() {
                    this.leftSwipe = false;
                },

                handleTouchStart(event) {
                    this.startX = event.touches[0].clientX;
                    this.currentX = this.startX;
                },

                handleTouchMove(event) {
                    this.currentX = event.touches[0].clientX;
                    const swipeDistance = 50;

                    if (this.startX - this.currentX > swipeDistance) {
                        this.leftSwipe = true;
                    }
                },

                init() {
                    this.$el.addEventListener('touchstart', this.handleTouchStart.bind(this));
                    this.$el.addEventListener('touchmove', this.handleTouchMove.bind(this));
                }
            };
        })
    </script>
    <?php
        $__output = ob_get_clean();

        \Livewire\store($this)->push('scripts', $__output, $__scriptKey)
    ?>
<?php /**PATH C:\laragon\www\pure-finance\resources\views/components/transaction.blade.php ENDPATH**/ ?>