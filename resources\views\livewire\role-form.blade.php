<div>
    <flux:modal name="{{ $role ? 'edit-role-' . $role['id'] : 'create-role' }}">
        <div x-cloak wire:loading.remove class="space-y-6">
            <flux:heading size="lg" class="font-semibold -mt-1.5!">
                {{ $role ? 'Edit' : 'Create' }} Role
            </flux:heading>

            <div class="space-y-4">
                <flux:field>
                    <flux:label>Name</flux:label>

                    <flux:input type="text" wire:model='name' required />

                    <flux:error name="name" />
                </flux:field>

                <flux:field>
                    <flux:label>Guard Name</flux:label>

                    <x-select wire:model='guard_name' required>
                        <x-option value="web">Web</x-option>
                        <x-option value="api">API</x-option>
                    </x-select>

                    <flux:error name="guard_name" />
                </flux:field>

                <flux:field>
                    <flux:label>Permissions</flux:label>

                    <div class="space-y-2 max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg p-3">
                        @foreach($availablePermissions as $permission)
                            <label class="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    wire:model="permissions"
                                    value="{{ $permission->id }}"
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                >
                                <span class="text-sm">{{ $permission->name }}</span>
                            </label>
                        @endforeach
                    </div>

                    <flux:error name="permissions" />
                </flux:field>
            </div>

            <div class="flex gap-2">
                <flux:spacer />

                <flux:modal.close>
                    <flux:button variant="ghost" size="sm" wire:click="resetForm">
                        Cancel
                    </flux:button>
                </flux:modal.close>


                <flux:button type="button" wire:click='submit' variant="primary" size="sm">
                    Save
                </flux:button>
            </div>
        </div>

        <div x-cloak wire:loading.flex class="flex items-center justify-center w-full h-[400px]">
            <flux:icon.loading />
        </div>
    </flux:modal>
</div>
