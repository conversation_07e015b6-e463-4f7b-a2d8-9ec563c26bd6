@props([
    'variant' => 'default',
    'align' => 'left',
    'class' => '',
])

@php
$variantClasses = [
    'default' => 'text-zinc-900 dark:text-zinc-100',
    'strong' => 'font-medium text-zinc-900 dark:text-zinc-100',
    'muted' => 'text-zinc-500 dark:text-zinc-400',
];

$alignClasses = [
    'left' => 'text-left',
    'center' => 'text-center',
    'right' => 'text-right',
    'end' => 'text-right',
];

$classes = 'px-3 py-4 text-sm border-b border-zinc-200 dark:border-white/20 ' . 
           ($variantClasses[$variant] ?? $variantClasses['default']) . ' ' . 
           ($alignClasses[$align] ?? 'text-left') . ' ' . 
           $class;
@endphp

<td {{ $attributes->merge(['class' => $classes]) }}>
    {{ $slot }}
</td>
