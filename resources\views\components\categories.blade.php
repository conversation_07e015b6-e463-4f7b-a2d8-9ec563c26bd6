@props(['categories', 'disabled' => null])

<flux:field>
    <flux:label @class([
        'opacity-50' => $disabled
    ])>
        Category
    </flux:label>

    <div class="space-y-2">
        <!-- Debug: Show categories structure -->
        @if(config('app.debug'))
            <div class="text-xs text-gray-500 p-2 bg-gray-100 rounded">
                Debug: {{ count($categories) }} categories found
            </div>
        @endif

        <x-select placeholder="Select a category" wire:model='category_id'>
            @forelse ($categories as $category)
                @php
                    $categoryId = is_array($category) ? $category['id'] : $category->id;
                    $categoryName = is_array($category) ? $category['name'] : $category->name;
                    $children = is_array($category) ? ($category['children'] ?? []) : ($category->children ?? []);
                @endphp

                <x-option value="{{ $categoryId }}">
                    {{ $categoryName }}
                </x-option>

                @foreach($children as $child)
                    @php
                        $childId = is_array($child) ? $child['id'] : $child->id;
                        $childName = is_array($child) ? $child['name'] : $child->name;
                    @endphp
                    <x-option value="{{ $childId }}">
                        &nbsp;&nbsp;&nbsp;&nbsp;{{ $childName }}
                    </x-option>
                @endforeach
            @empty
                <x-option value="" disabled>No categories available</x-option>
            @endforelse
        </x-select>

        <!-- Add category button -->
        <div class="flex justify-end">
            <flux:modal.trigger name="category-form">
                <flux:button square variant="subtle" size="sm" aria-label="Add new category">
                    <flux:icon.plus variant="micro" />
                    <span class="ml-1 text-xs">Add Category</span>
                </flux:button>
            </flux:modal.trigger>
        </div>
    </div>

    <flux:error name="category_id" />

    <div class="absolute">
        <livewire:category-form />
    </div>
</flux:field>