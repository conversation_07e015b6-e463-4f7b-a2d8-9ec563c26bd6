{"__meta": {"id": "01JW5WE0FZ1J4QG4JZJ6VGB70H", "datetime": "2025-05-26 08:48:01", "utime": **********.024671, "method": "GET", "uri": "/roles", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748249273.974297, "end": **********.024693, "duration": 7.050395965576172, "duration_str": "7.05s", "measures": [{"label": "Booting", "start": 1748249273.974297, "relative_start": 0, "end": **********.270215, "relative_end": **********.270215, "duration": 0.*****************, "duration_str": "296ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.270225, "relative_start": 0.****************, "end": **********.024695, "relative_end": 1.9073486328125e-06, "duration": 6.***************, "duration_str": "6.75s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.310619, "relative_start": 0.****************, "end": **********.314339, "relative_end": **********.314339, "duration": 0.003719806671142578, "duration_str": "3.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.360512, "relative_start": 0.****************, "end": **********.019808, "relative_end": **********.019808, "duration": 6.***************, "duration_str": "6.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: roles", "start": **********.365113, "relative_start": 0.*****************, "end": **********.365113, "relative_end": **********.365113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.role-table", "start": **********.408785, "relative_start": 0.*****************, "end": **********.408785, "relative_end": **********.408785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249278.117219, "relative_start": 4.1429219245910645, "end": 1748249278.117219, "relative_end": 1748249278.117219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249278.118261, "relative_start": 4.143964052200317, "end": 1748249278.118261, "relative_end": 1748249278.118261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249278.120196, "relative_start": 4.145899057388306, "end": 1748249278.120196, "relative_end": 1748249278.120196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "start": 1748249278.120971, "relative_start": 4.146673917770386, "end": 1748249278.120971, "relative_end": 1748249278.120971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249278.122066, "relative_start": 4.147768974304199, "end": 1748249278.122066, "relative_end": 1748249278.122066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249278.123136, "relative_start": 4.148838996887207, "end": 1748249278.123136, "relative_end": 1748249278.123136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249278.123819, "relative_start": 4.149522066116333, "end": 1748249278.123819, "relative_end": 1748249278.123819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.role-form", "start": 1748249278.129822, "relative_start": 4.155524969100952, "end": 1748249278.129822, "relative_end": 1748249278.129822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249280.363695, "relative_start": 6.389397859573364, "end": 1748249280.363695, "relative_end": 1748249280.363695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.36435, "relative_start": 6.390053033828735, "end": 1748249280.36435, "relative_end": 1748249280.36435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": 1748249280.365026, "relative_start": 6.390728950500488, "end": 1748249280.365026, "relative_end": 1748249280.365026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": 1748249280.3675, "relative_start": 6.393203020095825, "end": 1748249280.3675, "relative_end": 1748249280.3675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.368535, "relative_start": 6.394237995147705, "end": 1748249280.368535, "relative_end": 1748249280.368535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.369247, "relative_start": 6.394949913024902, "end": 1748249280.369247, "relative_end": 1748249280.369247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.369856, "relative_start": 6.395559072494507, "end": 1748249280.369856, "relative_end": 1748249280.369856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249280.370442, "relative_start": 6.396144866943359, "end": 1748249280.370442, "relative_end": 1748249280.370442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249280.371709, "relative_start": 6.397412061691284, "end": 1748249280.371709, "relative_end": 1748249280.371709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": 1748249280.372551, "relative_start": 6.398253917694092, "end": 1748249280.372551, "relative_end": 1748249280.372551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.373546, "relative_start": 6.399248838424683, "end": 1748249280.373546, "relative_end": 1748249280.373546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.374501, "relative_start": 6.4002039432525635, "end": 1748249280.374501, "relative_end": 1748249280.374501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.375684, "relative_start": 6.401386976242065, "end": 1748249280.375684, "relative_end": 1748249280.375684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.377363, "relative_start": 6.403065919876099, "end": 1748249280.377363, "relative_end": 1748249280.377363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.37833, "relative_start": 6.404032945632935, "end": 1748249280.37833, "relative_end": 1748249280.37833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249280.379174, "relative_start": 6.404876947402954, "end": 1748249280.379174, "relative_end": 1748249280.379174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.379951, "relative_start": 6.405653953552246, "end": 1748249280.379951, "relative_end": 1748249280.379951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.382116, "relative_start": 6.407819032669067, "end": 1748249280.382116, "relative_end": 1748249280.382116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.383094, "relative_start": 6.408797025680542, "end": 1748249280.383094, "relative_end": 1748249280.383094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.383975, "relative_start": 6.409677982330322, "end": 1748249280.383975, "relative_end": 1748249280.383975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.384633, "relative_start": 6.410336017608643, "end": 1748249280.384633, "relative_end": 1748249280.384633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.386156, "relative_start": 6.411859035491943, "end": 1748249280.386156, "relative_end": 1748249280.386156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249280.387006, "relative_start": 6.41270899772644, "end": 1748249280.387006, "relative_end": 1748249280.387006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.389033, "relative_start": 6.414736032485962, "end": 1748249280.389033, "relative_end": 1748249280.389033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.390103, "relative_start": 6.41580605506897, "end": 1748249280.390103, "relative_end": 1748249280.390103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249280.391136, "relative_start": 6.416838884353638, "end": 1748249280.391136, "relative_end": 1748249280.391136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.393249, "relative_start": 6.418951988220215, "end": 1748249280.393249, "relative_end": 1748249280.393249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.395055, "relative_start": 6.420758008956909, "end": 1748249280.395055, "relative_end": 1748249280.395055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249280.396273, "relative_start": 6.42197585105896, "end": 1748249280.396273, "relative_end": 1748249280.396273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.396997, "relative_start": 6.422699928283691, "end": 1748249280.396997, "relative_end": 1748249280.396997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.397618, "relative_start": 6.423321008682251, "end": 1748249280.397618, "relative_end": 1748249280.397618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.398173, "relative_start": 6.423876047134399, "end": 1748249280.398173, "relative_end": 1748249280.398173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": 1748249280.403984, "relative_start": 6.429687023162842, "end": 1748249280.403984, "relative_end": 1748249280.403984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.408105, "relative_start": 6.433807849884033, "end": 1748249280.408105, "relative_end": 1748249280.408105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "start": 1748249280.409028, "relative_start": 6.4347310066223145, "end": 1748249280.409028, "relative_end": 1748249280.409028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.409736, "relative_start": 6.435438871383667, "end": 1748249280.409736, "relative_end": 1748249280.409736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249280.410275, "relative_start": 6.435977935791016, "end": 1748249280.410275, "relative_end": 1748249280.410275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "start": 1748249280.410964, "relative_start": 6.436666965484619, "end": 1748249280.410964, "relative_end": 1748249280.410964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249280.411722, "relative_start": 6.437424898147583, "end": 1748249280.411722, "relative_end": 1748249280.411722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.412217, "relative_start": 6.437919855117798, "end": 1748249280.412217, "relative_end": 1748249280.412217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.413553, "relative_start": 6.439255952835083, "end": 1748249280.413553, "relative_end": 1748249280.413553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.414164, "relative_start": 6.43986701965332, "end": 1748249280.414164, "relative_end": 1748249280.414164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": 1748249280.414777, "relative_start": 6.44047999382019, "end": 1748249280.414777, "relative_end": 1748249280.414777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": 1748249280.415768, "relative_start": 6.4414708614349365, "end": 1748249280.415768, "relative_end": 1748249280.415768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": 1748249280.416405, "relative_start": 6.442107915878296, "end": 1748249280.416405, "relative_end": 1748249280.416405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": 1748249280.416909, "relative_start": 6.442611932754517, "end": 1748249280.416909, "relative_end": 1748249280.416909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": 1748249280.417396, "relative_start": 6.443099021911621, "end": 1748249280.417396, "relative_end": 1748249280.417396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": 1748249280.417875, "relative_start": 6.443578004837036, "end": 1748249280.417875, "relative_end": 1748249280.417875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.columns", "start": 1748249280.418355, "relative_start": 6.444057941436768, "end": 1748249280.418355, "relative_end": 1748249280.418355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.419012, "relative_start": 6.4447150230407715, "end": 1748249280.419012, "relative_end": 1748249280.419012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.419601, "relative_start": 6.445303916931152, "end": 1748249280.419601, "relative_end": 1748249280.419601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": 1748249280.420227, "relative_start": 6.445930004119873, "end": 1748249280.420227, "relative_end": 1748249280.420227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": 1748249280.423432, "relative_start": 6.449135065078735, "end": 1748249280.423432, "relative_end": 1748249280.423432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.424305, "relative_start": 6.450007915496826, "end": 1748249280.424305, "relative_end": 1748249280.424305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": 1748249280.425043, "relative_start": 6.450746059417725, "end": 1748249280.425043, "relative_end": 1748249280.425043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": 1748249280.426072, "relative_start": 6.451774835586548, "end": 1748249280.426072, "relative_end": 1748249280.426072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.426506, "relative_start": 6.452208995819092, "end": 1748249280.426506, "relative_end": 1748249280.426506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.427049, "relative_start": 6.452751874923706, "end": 1748249280.427049, "relative_end": 1748249280.427049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.42827, "relative_start": 6.453973054885864, "end": 1748249280.42827, "relative_end": 1748249280.42827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249280.42909, "relative_start": 6.4547929763793945, "end": 1748249280.42909, "relative_end": 1748249280.42909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.42969, "relative_start": 6.455392837524414, "end": 1748249280.42969, "relative_end": 1748249280.42969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.430256, "relative_start": 6.455958843231201, "end": 1748249280.430256, "relative_end": 1748249280.430256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249280.430774, "relative_start": 6.456476926803589, "end": 1748249280.430774, "relative_end": 1748249280.430774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.role-form", "start": 1748249280.46889, "relative_start": 6.494592905044556, "end": 1748249280.46889, "relative_end": 1748249280.46889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249280.472523, "relative_start": 6.498225927352905, "end": 1748249280.472523, "relative_end": 1748249280.472523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.473687, "relative_start": 6.499389886856079, "end": 1748249280.473687, "relative_end": 1748249280.473687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": 1748249280.474326, "relative_start": 6.500028848648071, "end": 1748249280.474326, "relative_end": 1748249280.474326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": 1748249280.476281, "relative_start": 6.501983880996704, "end": 1748249280.476281, "relative_end": 1748249280.476281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.477197, "relative_start": 6.502899885177612, "end": 1748249280.477197, "relative_end": 1748249280.477197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.477806, "relative_start": 6.503509044647217, "end": 1748249280.477806, "relative_end": 1748249280.477806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.478418, "relative_start": 6.5041210651397705, "end": 1748249280.478418, "relative_end": 1748249280.478418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249280.479187, "relative_start": 6.504889965057373, "end": 1748249280.479187, "relative_end": 1748249280.479187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249280.479806, "relative_start": 6.505508899688721, "end": 1748249280.479806, "relative_end": 1748249280.479806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": 1748249280.480542, "relative_start": 6.506244897842407, "end": 1748249280.480542, "relative_end": 1748249280.480542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.481343, "relative_start": 6.5070459842681885, "end": 1748249280.481343, "relative_end": 1748249280.481343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.482331, "relative_start": 6.508033990859985, "end": 1748249280.482331, "relative_end": 1748249280.482331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.483228, "relative_start": 6.508930921554565, "end": 1748249280.483228, "relative_end": 1748249280.483228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.484846, "relative_start": 6.510549068450928, "end": 1748249280.484846, "relative_end": 1748249280.484846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.486098, "relative_start": 6.51180100440979, "end": 1748249280.486098, "relative_end": 1748249280.486098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249280.487009, "relative_start": 6.512712001800537, "end": 1748249280.487009, "relative_end": 1748249280.487009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.487935, "relative_start": 6.513638019561768, "end": 1748249280.487935, "relative_end": 1748249280.487935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.49051, "relative_start": 6.5162129402160645, "end": 1748249280.49051, "relative_end": 1748249280.49051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.491415, "relative_start": 6.517117977142334, "end": 1748249280.491415, "relative_end": 1748249280.491415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.492123, "relative_start": 6.5178258419036865, "end": 1748249280.492123, "relative_end": 1748249280.492123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.492551, "relative_start": 6.518254041671753, "end": 1748249280.492551, "relative_end": 1748249280.492551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.494207, "relative_start": 6.519909858703613, "end": 1748249280.494207, "relative_end": 1748249280.494207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249280.495102, "relative_start": 6.5208048820495605, "end": 1748249280.495102, "relative_end": 1748249280.495102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.495874, "relative_start": 6.521576881408691, "end": 1748249280.495874, "relative_end": 1748249280.495874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.497052, "relative_start": 6.522754907608032, "end": 1748249280.497052, "relative_end": 1748249280.497052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249280.497789, "relative_start": 6.523491859436035, "end": 1748249280.497789, "relative_end": 1748249280.497789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.499056, "relative_start": 6.52475905418396, "end": 1748249280.499056, "relative_end": 1748249280.499056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.502013, "relative_start": 6.5277159214019775, "end": 1748249280.502013, "relative_end": 1748249280.502013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249280.503025, "relative_start": 6.528728008270264, "end": 1748249280.503025, "relative_end": 1748249280.503025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.50398, "relative_start": 6.529682874679565, "end": 1748249280.50398, "relative_end": 1748249280.50398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.505804, "relative_start": 6.5315070152282715, "end": 1748249280.505804, "relative_end": 1748249280.505804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.506602, "relative_start": 6.532305002212524, "end": 1748249280.506602, "relative_end": 1748249280.506602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.515163, "relative_start": 6.540865898132324, "end": 1748249280.515163, "relative_end": 1748249280.515163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.517769, "relative_start": 6.543472051620483, "end": 1748249280.517769, "relative_end": 1748249280.517769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249280.518926, "relative_start": 6.544628858566284, "end": 1748249280.518926, "relative_end": 1748249280.518926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.519756, "relative_start": 6.545459032058716, "end": 1748249280.519756, "relative_end": 1748249280.519756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.52047, "relative_start": 6.546172857284546, "end": 1748249280.52047, "relative_end": 1748249280.52047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249280.521942, "relative_start": 6.547644853591919, "end": 1748249280.521942, "relative_end": 1748249280.521942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249280.522752, "relative_start": 6.548454999923706, "end": 1748249280.522752, "relative_end": 1748249280.522752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249280.523522, "relative_start": 6.549224853515625, "end": 1748249280.523522, "relative_end": 1748249280.523522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249280.524297, "relative_start": 6.549999952316284, "end": 1748249280.524297, "relative_end": 1748249280.524297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.524764, "relative_start": 6.550467014312744, "end": 1748249280.524764, "relative_end": 1748249280.524764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.526587, "relative_start": 6.552289962768555, "end": 1748249280.526587, "relative_end": 1748249280.526587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.527338, "relative_start": 6.553040981292725, "end": 1748249280.527338, "relative_end": 1748249280.527338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.528067, "relative_start": 6.553770065307617, "end": 1748249280.528067, "relative_end": 1748249280.528067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.528492, "relative_start": 6.554194927215576, "end": 1748249280.528492, "relative_end": 1748249280.528492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.530316, "relative_start": 6.556019067764282, "end": 1748249280.530316, "relative_end": 1748249280.530316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249280.531014, "relative_start": 6.5567169189453125, "end": 1748249280.531014, "relative_end": 1748249280.531014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.531685, "relative_start": 6.557388067245483, "end": 1748249280.531685, "relative_end": 1748249280.531685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.532643, "relative_start": 6.5583460330963135, "end": 1748249280.532643, "relative_end": 1748249280.532643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249280.533738, "relative_start": 6.559440851211548, "end": 1748249280.533738, "relative_end": 1748249280.533738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.534967, "relative_start": 6.560669898986816, "end": 1748249280.534967, "relative_end": 1748249280.534967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.536857, "relative_start": 6.5625598430633545, "end": 1748249280.536857, "relative_end": 1748249280.536857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249280.538063, "relative_start": 6.563766002655029, "end": 1748249280.538063, "relative_end": 1748249280.538063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.539191, "relative_start": 6.564893960952759, "end": 1748249280.539191, "relative_end": 1748249280.539191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.540085, "relative_start": 6.56578803062439, "end": 1748249280.540085, "relative_end": 1748249280.540085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.540842, "relative_start": 6.566545009613037, "end": 1748249280.540842, "relative_end": 1748249280.540842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.541242, "relative_start": 6.56694483757019, "end": 1748249280.541242, "relative_end": 1748249280.541242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249280.541844, "relative_start": 6.567546844482422, "end": 1748249280.541844, "relative_end": 1748249280.541844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.542673, "relative_start": 6.568376064300537, "end": 1748249280.542673, "relative_end": 1748249280.542673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.543283, "relative_start": 6.568985939025879, "end": 1748249280.543283, "relative_end": 1748249280.543283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": 1748249280.543911, "relative_start": 6.569613933563232, "end": 1748249280.543911, "relative_end": 1748249280.543911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": 1748249280.545299, "relative_start": 6.571002006530762, "end": 1748249280.545299, "relative_end": 1748249280.545299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.545895, "relative_start": 6.571598052978516, "end": 1748249280.545895, "relative_end": 1748249280.545895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": 1748249280.546542, "relative_start": 6.572244882583618, "end": 1748249280.546542, "relative_end": 1748249280.546542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": 1748249280.547885, "relative_start": 6.573587894439697, "end": 1748249280.547885, "relative_end": 1748249280.547885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.548424, "relative_start": 6.574126958847046, "end": 1748249280.548424, "relative_end": 1748249280.548424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.549287, "relative_start": 6.5749900341033936, "end": 1748249280.549287, "relative_end": 1748249280.549287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.55132, "relative_start": 6.577023029327393, "end": 1748249280.55132, "relative_end": 1748249280.55132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249280.55206, "relative_start": 6.577762842178345, "end": 1748249280.55206, "relative_end": 1748249280.55206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.552756, "relative_start": 6.578459024429321, "end": 1748249280.552756, "relative_end": 1748249280.552756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.553494, "relative_start": 6.579196929931641, "end": 1748249280.553494, "relative_end": 1748249280.553494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249280.555128, "relative_start": 6.580831050872803, "end": 1748249280.555128, "relative_end": 1748249280.555128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.role-form", "start": 1748249280.616618, "relative_start": 6.642320871353149, "end": 1748249280.616618, "relative_end": 1748249280.616618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249280.619501, "relative_start": 6.645204067230225, "end": 1748249280.619501, "relative_end": 1748249280.619501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.622802, "relative_start": 6.648504972457886, "end": 1748249280.622802, "relative_end": 1748249280.622802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": 1748249280.640211, "relative_start": 6.665914058685303, "end": 1748249280.640211, "relative_end": 1748249280.640211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": 1748249280.645955, "relative_start": 6.671658039093018, "end": 1748249280.645955, "relative_end": 1748249280.645955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.648151, "relative_start": 6.673853874206543, "end": 1748249280.648151, "relative_end": 1748249280.648151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.649096, "relative_start": 6.674798965454102, "end": 1748249280.649096, "relative_end": 1748249280.649096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.649981, "relative_start": 6.675683975219727, "end": 1748249280.649981, "relative_end": 1748249280.649981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249280.650761, "relative_start": 6.676463842391968, "end": 1748249280.650761, "relative_end": 1748249280.650761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249280.651375, "relative_start": 6.677078008651733, "end": 1748249280.651375, "relative_end": 1748249280.651375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": 1748249280.652023, "relative_start": 6.6777260303497314, "end": 1748249280.652023, "relative_end": 1748249280.652023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.65284, "relative_start": 6.678542852401733, "end": 1748249280.65284, "relative_end": 1748249280.65284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.653713, "relative_start": 6.679415941238403, "end": 1748249280.653713, "relative_end": 1748249280.653713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.66596, "relative_start": 6.691663026809692, "end": 1748249280.66596, "relative_end": 1748249280.66596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.669495, "relative_start": 6.695198059082031, "end": 1748249280.669495, "relative_end": 1748249280.669495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.672375, "relative_start": 6.698077917098999, "end": 1748249280.672375, "relative_end": 1748249280.672375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249280.674492, "relative_start": 6.700194835662842, "end": 1748249280.674492, "relative_end": 1748249280.674492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.675345, "relative_start": 6.701047897338867, "end": 1748249280.675345, "relative_end": 1748249280.675345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.678091, "relative_start": 6.703794002532959, "end": 1748249280.678091, "relative_end": 1748249280.678091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.679035, "relative_start": 6.704737901687622, "end": 1748249280.679035, "relative_end": 1748249280.679035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.679866, "relative_start": 6.70556902885437, "end": 1748249280.679866, "relative_end": 1748249280.679866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.68045, "relative_start": 6.70615291595459, "end": 1748249280.68045, "relative_end": 1748249280.68045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.684272, "relative_start": 6.709975004196167, "end": 1748249280.684272, "relative_end": 1748249280.684272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249280.685364, "relative_start": 6.711066961288452, "end": 1748249280.685364, "relative_end": 1748249280.685364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.68615, "relative_start": 6.71185302734375, "end": 1748249280.68615, "relative_end": 1748249280.68615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.687011, "relative_start": 6.712713956832886, "end": 1748249280.687011, "relative_end": 1748249280.687011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249280.689333, "relative_start": 6.715035915374756, "end": 1748249280.689333, "relative_end": 1748249280.689333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.690946, "relative_start": 6.716649055480957, "end": 1748249280.690946, "relative_end": 1748249280.690946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.693111, "relative_start": 6.718813896179199, "end": 1748249280.693111, "relative_end": 1748249280.693111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249280.693929, "relative_start": 6.719631910324097, "end": 1748249280.693929, "relative_end": 1748249280.693929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.694664, "relative_start": 6.720366954803467, "end": 1748249280.694664, "relative_end": 1748249280.694664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.69555, "relative_start": 6.721252918243408, "end": 1748249280.69555, "relative_end": 1748249280.69555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.696427, "relative_start": 6.722130060195923, "end": 1748249280.696427, "relative_end": 1748249280.696427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.701105, "relative_start": 6.726808071136475, "end": 1748249280.701105, "relative_end": 1748249280.701105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249280.701924, "relative_start": 6.7276270389556885, "end": 1748249280.701924, "relative_end": 1748249280.701924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.702733, "relative_start": 6.72843599319458, "end": 1748249280.702733, "relative_end": 1748249280.702733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.703435, "relative_start": 6.729137897491455, "end": 1748249280.703435, "relative_end": 1748249280.703435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": 1748249280.705551, "relative_start": 6.7312538623809814, "end": 1748249280.705551, "relative_end": 1748249280.705551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": 1748249280.707167, "relative_start": 6.732869863510132, "end": 1748249280.707167, "relative_end": 1748249280.707167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.707815, "relative_start": 6.73351788520813, "end": 1748249280.707815, "relative_end": 1748249280.707815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": 1748249280.708484, "relative_start": 6.734186887741089, "end": 1748249280.708484, "relative_end": 1748249280.708484, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": 1748249280.71079, "relative_start": 6.736492872238159, "end": 1748249280.71079, "relative_end": 1748249280.71079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.711404, "relative_start": 6.737107038497925, "end": 1748249280.711404, "relative_end": 1748249280.711404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.712058, "relative_start": 6.7377610206604, "end": 1748249280.712058, "relative_end": 1748249280.712058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.713723, "relative_start": 6.739425897598267, "end": 1748249280.713723, "relative_end": 1748249280.713723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249280.714538, "relative_start": 6.740241050720215, "end": 1748249280.714538, "relative_end": 1748249280.714538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.715317, "relative_start": 6.74101996421814, "end": 1748249280.715317, "relative_end": 1748249280.715317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.716308, "relative_start": 6.742011070251465, "end": 1748249280.716308, "relative_end": 1748249280.716308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249280.717025, "relative_start": 6.742727994918823, "end": 1748249280.717025, "relative_end": 1748249280.717025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.role-form", "start": 1748249280.735291, "relative_start": 6.760993957519531, "end": 1748249280.735291, "relative_end": 1748249280.735291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249280.736819, "relative_start": 6.762521982192993, "end": 1748249280.736819, "relative_end": 1748249280.736819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.737998, "relative_start": 6.76370096206665, "end": 1748249280.737998, "relative_end": 1748249280.737998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": 1748249280.740104, "relative_start": 6.7658069133758545, "end": 1748249280.740104, "relative_end": 1748249280.740104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": 1748249280.742344, "relative_start": 6.768046855926514, "end": 1748249280.742344, "relative_end": 1748249280.742344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.743574, "relative_start": 6.769276857376099, "end": 1748249280.743574, "relative_end": 1748249280.743574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.744821, "relative_start": 6.770524024963379, "end": 1748249280.744821, "relative_end": 1748249280.744821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.745412, "relative_start": 6.771115064620972, "end": 1748249280.745412, "relative_end": 1748249280.745412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249280.745913, "relative_start": 6.771615982055664, "end": 1748249280.745913, "relative_end": 1748249280.745913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249280.746284, "relative_start": 6.771986961364746, "end": 1748249280.746284, "relative_end": 1748249280.746284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": 1748249280.746629, "relative_start": 6.772331953048706, "end": 1748249280.746629, "relative_end": 1748249280.746629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.747122, "relative_start": 6.772825002670288, "end": 1748249280.747122, "relative_end": 1748249280.747122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.747628, "relative_start": 6.773330926895142, "end": 1748249280.747628, "relative_end": 1748249280.747628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.748079, "relative_start": 6.773782014846802, "end": 1748249280.748079, "relative_end": 1748249280.748079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.748781, "relative_start": 6.774483919143677, "end": 1748249280.748781, "relative_end": 1748249280.748781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.749288, "relative_start": 6.774991035461426, "end": 1748249280.749288, "relative_end": 1748249280.749288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249280.749725, "relative_start": 6.775428056716919, "end": 1748249280.749725, "relative_end": 1748249280.749725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.75005, "relative_start": 6.775753021240234, "end": 1748249280.75005, "relative_end": 1748249280.75005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.751165, "relative_start": 6.776867866516113, "end": 1748249280.751165, "relative_end": 1748249280.751165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.751682, "relative_start": 6.777384996414185, "end": 1748249280.751682, "relative_end": 1748249280.751682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.752171, "relative_start": 6.777873992919922, "end": 1748249280.752171, "relative_end": 1748249280.752171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.752462, "relative_start": 6.778164863586426, "end": 1748249280.752462, "relative_end": 1748249280.752462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.753715, "relative_start": 6.779417991638184, "end": 1748249280.753715, "relative_end": 1748249280.753715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249280.75487, "relative_start": 6.780572891235352, "end": 1748249280.75487, "relative_end": 1748249280.75487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.75596, "relative_start": 6.781662940979004, "end": 1748249280.75596, "relative_end": 1748249280.75596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.756893, "relative_start": 6.782595872879028, "end": 1748249280.756893, "relative_end": 1748249280.756893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249280.757627, "relative_start": 6.783329963684082, "end": 1748249280.757627, "relative_end": 1748249280.757627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.758742, "relative_start": 6.78444504737854, "end": 1748249280.758742, "relative_end": 1748249280.758742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.761185, "relative_start": 6.786887884140015, "end": 1748249280.761185, "relative_end": 1748249280.761185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249280.762144, "relative_start": 6.78784704208374, "end": 1748249280.762144, "relative_end": 1748249280.762144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.762848, "relative_start": 6.788550853729248, "end": 1748249280.762848, "relative_end": 1748249280.762848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.763433, "relative_start": 6.789135932922363, "end": 1748249280.763433, "relative_end": 1748249280.763433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.764022, "relative_start": 6.789725065231323, "end": 1748249280.764022, "relative_end": 1748249280.764022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.766717, "relative_start": 6.792419910430908, "end": 1748249280.766717, "relative_end": 1748249280.766717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.768013, "relative_start": 6.793715953826904, "end": 1748249280.768013, "relative_end": 1748249280.768013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249280.76851, "relative_start": 6.794213056564331, "end": 1748249280.76851, "relative_end": 1748249280.76851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.768959, "relative_start": 6.794661998748779, "end": 1748249280.768959, "relative_end": 1748249280.768959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.769456, "relative_start": 6.795158863067627, "end": 1748249280.769456, "relative_end": 1748249280.769456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249280.769948, "relative_start": 6.795650959014893, "end": 1748249280.769948, "relative_end": 1748249280.769948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249280.770375, "relative_start": 6.7960779666900635, "end": 1748249280.770375, "relative_end": 1748249280.770375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249280.771826, "relative_start": 6.797528982162476, "end": 1748249280.771826, "relative_end": 1748249280.771826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249280.77253, "relative_start": 6.7982330322265625, "end": 1748249280.77253, "relative_end": 1748249280.77253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.772974, "relative_start": 6.79867696762085, "end": 1748249280.772974, "relative_end": 1748249280.772974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.774624, "relative_start": 6.8003270626068115, "end": 1748249280.774624, "relative_end": 1748249280.774624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.775528, "relative_start": 6.8012309074401855, "end": 1748249280.775528, "relative_end": 1748249280.775528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.776314, "relative_start": 6.802016973495483, "end": 1748249280.776314, "relative_end": 1748249280.776314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.776741, "relative_start": 6.802443981170654, "end": 1748249280.776741, "relative_end": 1748249280.776741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.778102, "relative_start": 6.803804874420166, "end": 1748249280.778102, "relative_end": 1748249280.778102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249280.778971, "relative_start": 6.804673910140991, "end": 1748249280.778971, "relative_end": 1748249280.778971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.779702, "relative_start": 6.805404901504517, "end": 1748249280.779702, "relative_end": 1748249280.779702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.780326, "relative_start": 6.806028842926025, "end": 1748249280.780326, "relative_end": 1748249280.780326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249280.780858, "relative_start": 6.80656099319458, "end": 1748249280.780858, "relative_end": 1748249280.780858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.78163, "relative_start": 6.807332992553711, "end": 1748249280.78163, "relative_end": 1748249280.78163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.782808, "relative_start": 6.808511018753052, "end": 1748249280.782808, "relative_end": 1748249280.782808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249280.783327, "relative_start": 6.809030055999756, "end": 1748249280.783327, "relative_end": 1748249280.783327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.78378, "relative_start": 6.809483051300049, "end": 1748249280.78378, "relative_end": 1748249280.78378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.784294, "relative_start": 6.809996843338013, "end": 1748249280.784294, "relative_end": 1748249280.784294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.784808, "relative_start": 6.810510873794556, "end": 1748249280.784808, "relative_end": 1748249280.784808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.785113, "relative_start": 6.810816049575806, "end": 1748249280.785113, "relative_end": 1748249280.785113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249280.785584, "relative_start": 6.811286926269531, "end": 1748249280.785584, "relative_end": 1748249280.785584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.786044, "relative_start": 6.811746835708618, "end": 1748249280.786044, "relative_end": 1748249280.786044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.786469, "relative_start": 6.812171936035156, "end": 1748249280.786469, "relative_end": 1748249280.786469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": 1748249280.786931, "relative_start": 6.812633991241455, "end": 1748249280.786931, "relative_end": 1748249280.786931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": 1748249280.789014, "relative_start": 6.814717054367065, "end": 1748249280.789014, "relative_end": 1748249280.789014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.790001, "relative_start": 6.815703868865967, "end": 1748249280.790001, "relative_end": 1748249280.790001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": 1748249280.790629, "relative_start": 6.81633186340332, "end": 1748249280.790629, "relative_end": 1748249280.790629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": 1748249280.791682, "relative_start": 6.817384958267212, "end": 1748249280.791682, "relative_end": 1748249280.791682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.792197, "relative_start": 6.817899942398071, "end": 1748249280.792197, "relative_end": 1748249280.792197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.792794, "relative_start": 6.818496942520142, "end": 1748249280.792794, "relative_end": 1748249280.792794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.794257, "relative_start": 6.819959878921509, "end": 1748249280.794257, "relative_end": 1748249280.794257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249280.795039, "relative_start": 6.820741891860962, "end": 1748249280.795039, "relative_end": 1748249280.795039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.795826, "relative_start": 6.821528911590576, "end": 1748249280.795826, "relative_end": 1748249280.795826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.796422, "relative_start": 6.82212495803833, "end": 1748249280.796422, "relative_end": 1748249280.796422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249280.797242, "relative_start": 6.82294487953186, "end": 1748249280.797242, "relative_end": 1748249280.797242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.role-form", "start": 1748249280.813961, "relative_start": 6.839663982391357, "end": 1748249280.813961, "relative_end": 1748249280.813961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249280.815553, "relative_start": 6.8412559032440186, "end": 1748249280.815553, "relative_end": 1748249280.815553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.816125, "relative_start": 6.841827869415283, "end": 1748249280.816125, "relative_end": 1748249280.816125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": 1748249280.816559, "relative_start": 6.842262029647827, "end": 1748249280.816559, "relative_end": 1748249280.816559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": 1748249280.818182, "relative_start": 6.8438849449157715, "end": 1748249280.818182, "relative_end": 1748249280.818182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.819143, "relative_start": 6.84484601020813, "end": 1748249280.819143, "relative_end": 1748249280.819143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.819737, "relative_start": 6.845439910888672, "end": 1748249280.819737, "relative_end": 1748249280.819737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.820176, "relative_start": 6.845878839492798, "end": 1748249280.820176, "relative_end": 1748249280.820176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249280.821242, "relative_start": 6.84694504737854, "end": 1748249280.821242, "relative_end": 1748249280.821242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249280.822217, "relative_start": 6.847919940948486, "end": 1748249280.822217, "relative_end": 1748249280.822217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": 1748249280.822869, "relative_start": 6.848572015762329, "end": 1748249280.822869, "relative_end": 1748249280.822869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.823629, "relative_start": 6.849331855773926, "end": 1748249280.823629, "relative_end": 1748249280.823629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.824415, "relative_start": 6.850117921829224, "end": 1748249280.824415, "relative_end": 1748249280.824415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249280.825113, "relative_start": 6.850816011428833, "end": 1748249280.825113, "relative_end": 1748249280.825113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249280.826489, "relative_start": 6.852191925048828, "end": 1748249280.826489, "relative_end": 1748249280.826489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249280.827333, "relative_start": 6.853035926818848, "end": 1748249280.827333, "relative_end": 1748249280.827333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249280.828054, "relative_start": 6.853756904602051, "end": 1748249280.828054, "relative_end": 1748249280.828054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.828579, "relative_start": 6.854281902313232, "end": 1748249280.828579, "relative_end": 1748249280.828579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.830105, "relative_start": 6.8558080196380615, "end": 1748249280.830105, "relative_end": 1748249280.830105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.830759, "relative_start": 6.856462001800537, "end": 1748249280.830759, "relative_end": 1748249280.830759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.831378, "relative_start": 6.857080936431885, "end": 1748249280.831378, "relative_end": 1748249280.831378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.831956, "relative_start": 6.857658863067627, "end": 1748249280.831956, "relative_end": 1748249280.831956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.8336, "relative_start": 6.859302997589111, "end": 1748249280.8336, "relative_end": 1748249280.8336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249280.834159, "relative_start": 6.859861850738525, "end": 1748249280.834159, "relative_end": 1748249280.834159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.834655, "relative_start": 6.860357999801636, "end": 1748249280.834655, "relative_end": 1748249280.834655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.835173, "relative_start": 6.860875844955444, "end": 1748249280.835173, "relative_end": 1748249280.835173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249280.835667, "relative_start": 6.861369848251343, "end": 1748249280.835667, "relative_end": 1748249280.835667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.836539, "relative_start": 6.862241983413696, "end": 1748249280.836539, "relative_end": 1748249280.836539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.839068, "relative_start": 6.864770889282227, "end": 1748249280.839068, "relative_end": 1748249280.839068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249280.839937, "relative_start": 6.865639925003052, "end": 1748249280.839937, "relative_end": 1748249280.839937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.840643, "relative_start": 6.8663458824157715, "end": 1748249280.840643, "relative_end": 1748249280.840643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.841438, "relative_start": 6.867141008377075, "end": 1748249280.841438, "relative_end": 1748249280.841438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.842243, "relative_start": 6.867945909500122, "end": 1748249280.842243, "relative_end": 1748249280.842243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.845356, "relative_start": 6.871058940887451, "end": 1748249280.845356, "relative_end": 1748249280.845356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.84719, "relative_start": 6.8728928565979, "end": 1748249280.84719, "relative_end": 1748249280.84719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249280.847908, "relative_start": 6.873610973358154, "end": 1748249280.847908, "relative_end": 1748249280.847908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.848461, "relative_start": 6.874163866043091, "end": 1748249280.848461, "relative_end": 1748249280.848461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.849021, "relative_start": 6.8747239112854, "end": 1748249280.849021, "relative_end": 1748249280.849021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249280.84954, "relative_start": 6.8752429485321045, "end": 1748249280.84954, "relative_end": 1748249280.84954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249280.850009, "relative_start": 6.875711917877197, "end": 1748249280.850009, "relative_end": 1748249280.850009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249280.850528, "relative_start": 6.876230955123901, "end": 1748249280.850528, "relative_end": 1748249280.850528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249280.850954, "relative_start": 6.876657009124756, "end": 1748249280.850954, "relative_end": 1748249280.850954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.851289, "relative_start": 6.8769919872283936, "end": 1748249280.851289, "relative_end": 1748249280.851289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.853216, "relative_start": 6.878918886184692, "end": 1748249280.853216, "relative_end": 1748249280.853216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.853951, "relative_start": 6.8796539306640625, "end": 1748249280.853951, "relative_end": 1748249280.853951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.855708, "relative_start": 6.881410837173462, "end": 1748249280.855708, "relative_end": 1748249280.855708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.856194, "relative_start": 6.88189697265625, "end": 1748249280.856194, "relative_end": 1748249280.856194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.857551, "relative_start": 6.883254051208496, "end": 1748249280.857551, "relative_end": 1748249280.857551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249280.858192, "relative_start": 6.883894920349121, "end": 1748249280.858192, "relative_end": 1748249280.858192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.858752, "relative_start": 6.884454965591431, "end": 1748249280.858752, "relative_end": 1748249280.858752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.859377, "relative_start": 6.885079860687256, "end": 1748249280.859377, "relative_end": 1748249280.859377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249280.860042, "relative_start": 6.885745048522949, "end": 1748249280.860042, "relative_end": 1748249280.860042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.861132, "relative_start": 6.8868348598480225, "end": 1748249280.861132, "relative_end": 1748249280.861132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.8625, "relative_start": 6.888202905654907, "end": 1748249280.8625, "relative_end": 1748249280.8625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249280.863404, "relative_start": 6.88910698890686, "end": 1748249280.863404, "relative_end": 1748249280.863404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.864196, "relative_start": 6.889899015426636, "end": 1748249280.864196, "relative_end": 1748249280.864196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.864973, "relative_start": 6.890676021575928, "end": 1748249280.864973, "relative_end": 1748249280.864973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249280.86551, "relative_start": 6.8912129402160645, "end": 1748249280.86551, "relative_end": 1748249280.86551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249280.865801, "relative_start": 6.8915040493011475, "end": 1748249280.865801, "relative_end": 1748249280.865801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249280.866191, "relative_start": 6.8918938636779785, "end": 1748249280.866191, "relative_end": 1748249280.866191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.rows", "start": 1748249280.866995, "relative_start": 6.892698049545288, "end": 1748249280.866995, "relative_end": 1748249280.866995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.index", "start": 1748249280.867847, "relative_start": 6.893549919128418, "end": 1748249280.867847, "relative_end": 1748249280.867847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": 1748249280.868938, "relative_start": 6.894640922546387, "end": 1748249280.868938, "relative_end": 1748249280.868938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": 1748249280.870004, "relative_start": 6.89570689201355, "end": 1748249280.870004, "relative_end": 1748249280.870004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "start": 1748249280.870768, "relative_start": 6.89647102355957, "end": 1748249280.870768, "relative_end": 1748249280.870768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app", "start": 1748249280.879952, "relative_start": 6.9056549072265625, "end": 1748249280.879952, "relative_end": 1748249280.879952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::main", "start": 1748249280.880906, "relative_start": 6.906609058380127, "end": 1748249280.880906, "relative_end": 1748249280.880906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app.sidebar", "start": 1748249280.881518, "relative_start": 6.907220840454102, "end": 1748249280.881518, "relative_end": 1748249280.881518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.head", "start": 1748249280.885502, "relative_start": 6.911205053329468, "end": 1748249280.885502, "relative_end": 1748249280.885502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle", "start": 1748249280.886846, "relative_start": 6.912549018859863, "end": 1748249280.886846, "relative_end": 1748249280.886846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.891178, "relative_start": 6.91688084602356, "end": 1748249280.891178, "relative_end": 1748249280.891178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.892811, "relative_start": 6.918514013290405, "end": 1748249280.892811, "relative_end": 1748249280.892811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249280.89374, "relative_start": 6.919442892074585, "end": 1748249280.89374, "relative_end": 1748249280.89374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.89439, "relative_start": 6.920093059539795, "end": 1748249280.89439, "relative_end": 1748249280.89439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.895044, "relative_start": 6.9207470417022705, "end": 1748249280.895044, "relative_end": 1748249280.895044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.app-logo", "start": 1748249280.897465, "relative_start": 6.923167943954468, "end": 1748249280.897465, "relative_end": 1748249280.897465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249280.898863, "relative_start": 6.924566030502319, "end": 1748249280.898863, "relative_end": 1748249280.898863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.899854, "relative_start": 6.925556898117065, "end": 1748249280.899854, "relative_end": 1748249280.899854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.layout-dashboard", "start": 1748249280.900381, "relative_start": 6.926084041595459, "end": 1748249280.900381, "relative_end": 1748249280.900381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.900872, "relative_start": 6.926574945449829, "end": 1748249280.900872, "relative_end": 1748249280.900872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249280.901607, "relative_start": 6.927309989929199, "end": 1748249280.901607, "relative_end": 1748249280.901607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.902485, "relative_start": 6.928187847137451, "end": 1748249280.902485, "relative_end": 1748249280.902485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.user", "start": 1748249280.903165, "relative_start": 6.928868055343628, "end": 1748249280.903165, "relative_end": 1748249280.903165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.903659, "relative_start": 6.929362058639526, "end": 1748249280.903659, "relative_end": 1748249280.903659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249280.905767, "relative_start": 6.931469917297363, "end": 1748249280.905767, "relative_end": 1748249280.905767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.90688, "relative_start": 6.932582855224609, "end": 1748249280.90688, "relative_end": 1748249280.90688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.currency-dollar", "start": 1748249280.907805, "relative_start": 6.933507919311523, "end": 1748249280.907805, "relative_end": 1748249280.907805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.908586, "relative_start": 6.93428897857666, "end": 1748249280.908586, "relative_end": 1748249280.908586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249280.90981, "relative_start": 6.935513019561768, "end": 1748249280.90981, "relative_end": 1748249280.90981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.910828, "relative_start": 6.936531066894531, "end": 1748249280.910828, "relative_end": 1748249280.910828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.scroll-text", "start": 1748249280.912189, "relative_start": 6.937891960144043, "end": 1748249280.912189, "relative_end": 1748249280.912189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.913093, "relative_start": 6.938796043395996, "end": 1748249280.913093, "relative_end": 1748249280.913093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249280.914263, "relative_start": 6.9399659633636475, "end": 1748249280.914263, "relative_end": 1748249280.914263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.915517, "relative_start": 6.941220045089722, "end": 1748249280.915517, "relative_end": 1748249280.915517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.queue-list", "start": 1748249280.916487, "relative_start": 6.942189931869507, "end": 1748249280.916487, "relative_end": 1748249280.916487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.917228, "relative_start": 6.9429309368133545, "end": 1748249280.917228, "relative_end": 1748249280.917228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249280.918194, "relative_start": 6.943897008895874, "end": 1748249280.918194, "relative_end": 1748249280.918194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.919078, "relative_start": 6.944781064987183, "end": 1748249280.919078, "relative_end": 1748249280.919078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.tags", "start": 1748249280.919615, "relative_start": 6.945317983627319, "end": 1748249280.919615, "relative_end": 1748249280.919615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.92009, "relative_start": 6.94579291343689, "end": 1748249280.92009, "relative_end": 1748249280.92009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group", "start": 1748249280.920598, "relative_start": 6.946300983428955, "end": 1748249280.920598, "relative_end": 1748249280.920598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249280.922853, "relative_start": 6.948555946350098, "end": 1748249280.922853, "relative_end": 1748249280.922853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.924369, "relative_start": 6.9500720500946045, "end": 1748249280.924369, "relative_end": 1748249280.924369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.shield-check", "start": 1748249280.925486, "relative_start": 6.951189041137695, "end": 1748249280.925486, "relative_end": 1748249280.925486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.926335, "relative_start": 6.952038049697876, "end": 1748249280.926335, "relative_end": 1748249280.926335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249280.928263, "relative_start": 6.953965902328491, "end": 1748249280.928263, "relative_end": 1748249280.928263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.929804, "relative_start": 6.955507040023804, "end": 1748249280.929804, "relative_end": 1748249280.929804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.key", "start": 1748249280.930905, "relative_start": 6.956608057022095, "end": 1748249280.930905, "relative_end": 1748249280.930905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.931753, "relative_start": 6.95745587348938, "end": 1748249280.931753, "relative_end": 1748249280.931753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249280.932526, "relative_start": 6.958229064941406, "end": 1748249280.932526, "relative_end": 1748249280.932526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.9334, "relative_start": 6.9591028690338135, "end": 1748249280.9334, "relative_end": 1748249280.9334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.users", "start": 1748249280.934091, "relative_start": 6.959794044494629, "end": 1748249280.934091, "relative_end": 1748249280.934091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.934614, "relative_start": 6.960316896438599, "end": 1748249280.934614, "relative_end": 1748249280.934614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group", "start": 1748249280.935127, "relative_start": 6.960829973220825, "end": 1748249280.935127, "relative_end": 1748249280.935127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-down", "start": 1748249280.935908, "relative_start": 6.961611032485962, "end": 1748249280.935908, "relative_end": 1748249280.935908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-right", "start": 1748249280.936646, "relative_start": 6.962348937988281, "end": 1748249280.936646, "relative_end": 1748249280.936646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index", "start": 1748249280.937175, "relative_start": 6.962877988815308, "end": 1748249280.937175, "relative_end": 1748249280.937175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249280.93887, "relative_start": 6.964572906494141, "end": 1748249280.93887, "relative_end": 1748249280.93887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249280.939615, "relative_start": 6.965317964553833, "end": 1748249280.939615, "relative_end": 1748249280.939615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.941047, "relative_start": 6.966749906539917, "end": 1748249280.941047, "relative_end": 1748249280.941047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.folder-git-2", "start": 1748249280.941843, "relative_start": 6.967545986175537, "end": 1748249280.941843, "relative_end": 1748249280.941843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.942885, "relative_start": 6.968587875366211, "end": 1748249280.942885, "relative_end": 1748249280.942885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index", "start": 1748249280.943868, "relative_start": 6.969570875167847, "end": 1748249280.943868, "relative_end": 1748249280.943868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::profile", "start": 1748249280.945393, "relative_start": 6.971096038818359, "end": 1748249280.945393, "relative_end": 1748249280.945393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index", "start": 1748249280.946702, "relative_start": 6.972404956817627, "end": 1748249280.946702, "relative_end": 1748249280.946702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.948179, "relative_start": 6.973881959915161, "end": 1748249280.948179, "relative_end": 1748249280.948179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.949201, "relative_start": 6.9749040603637695, "end": 1748249280.949201, "relative_end": 1748249280.949201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.949943, "relative_start": 6.975646018981934, "end": 1748249280.949943, "relative_end": 1748249280.949943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down", "start": 1748249280.950552, "relative_start": 6.976254940032959, "end": 1748249280.950552, "relative_end": 1748249280.950552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "start": 1748249280.951449, "relative_start": 6.977151870727539, "end": 1748249280.951449, "relative_end": 1748249280.951449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": 1748249280.951933, "relative_start": 6.977635860443115, "end": 1748249280.951933, "relative_end": 1748249280.951933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": 1748249280.952571, "relative_start": 6.978273868560791, "end": 1748249280.952571, "relative_end": 1748249280.952571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": 1748249280.953335, "relative_start": 6.9790380001068115, "end": 1748249280.953335, "relative_end": 1748249280.953335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": 1748249280.954054, "relative_start": 6.979757070541382, "end": 1748249280.954054, "relative_end": 1748249280.954054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.956058, "relative_start": 6.9817609786987305, "end": 1748249280.956058, "relative_end": 1748249280.956058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun", "start": 1748249280.956926, "relative_start": 6.982629060745239, "end": 1748249280.956926, "relative_end": 1748249280.956926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": 1748249280.958045, "relative_start": 6.983747959136963, "end": 1748249280.958045, "relative_end": 1748249280.958045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": 1748249280.958882, "relative_start": 6.9845850467681885, "end": 1748249280.958882, "relative_end": 1748249280.958882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.95987, "relative_start": 6.985573053359985, "end": 1748249280.95987, "relative_end": 1748249280.95987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon", "start": 1748249280.960804, "relative_start": 6.986506938934326, "end": 1748249280.960804, "relative_end": 1748249280.960804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": 1748249280.961742, "relative_start": 6.987444877624512, "end": 1748249280.961742, "relative_end": 1748249280.961742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": 1748249280.962672, "relative_start": 6.988374948501587, "end": 1748249280.962672, "relative_end": 1748249280.962672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.963577, "relative_start": 6.9892799854278564, "end": 1748249280.963577, "relative_end": 1748249280.963577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop", "start": 1748249280.964342, "relative_start": 6.990045070648193, "end": 1748249280.964342, "relative_end": 1748249280.964342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index", "start": 1748249280.964907, "relative_start": 6.990609884262085, "end": 1748249280.964907, "relative_end": 1748249280.964907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented", "start": 1748249280.965615, "relative_start": 6.991317987442017, "end": 1748249280.965615, "relative_end": 1748249280.965615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": 1748249280.96621, "relative_start": 6.991912841796875, "end": 1748249280.96621, "relative_end": 1748249280.96621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": 1748249280.967005, "relative_start": 6.992707967758179, "end": 1748249280.967005, "relative_end": 1748249280.967005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": 1748249280.967369, "relative_start": 6.993072032928467, "end": 1748249280.967369, "relative_end": 1748249280.967369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "start": 1748249280.9681, "relative_start": 6.993803024291992, "end": 1748249280.9681, "relative_end": 1748249280.9681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.968965, "relative_start": 6.994668006896973, "end": 1748249280.968965, "relative_end": 1748249280.968965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog", "start": 1748249280.969691, "relative_start": 6.995393991470337, "end": 1748249280.969691, "relative_end": 1748249280.969691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.97024, "relative_start": 6.995943069458008, "end": 1748249280.97024, "relative_end": 1748249280.97024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "start": 1748249280.972104, "relative_start": 6.997807025909424, "end": 1748249280.972104, "relative_end": 1748249280.972104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": 1748249280.972706, "relative_start": 6.998409032821655, "end": 1748249280.972706, "relative_end": 1748249280.972706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": 1748249280.973573, "relative_start": 6.9992759227752686, "end": 1748249280.973573, "relative_end": 1748249280.973573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "start": 1748249280.97489, "relative_start": 7.000592947006226, "end": 1748249280.97489, "relative_end": 1748249280.97489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.975891, "relative_start": 7.001594066619873, "end": 1748249280.975891, "relative_end": 1748249280.975891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle", "start": 1748249280.976769, "relative_start": 7.002471923828125, "end": 1748249280.976769, "relative_end": 1748249280.976769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.977547, "relative_start": 7.003249883651733, "end": 1748249280.977547, "relative_end": 1748249280.977547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.index", "start": 1748249280.978403, "relative_start": 7.004106044769287, "end": 1748249280.978403, "relative_end": 1748249280.978403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::dropdown", "start": 1748249280.97897, "relative_start": 7.004673004150391, "end": 1748249280.97897, "relative_end": 1748249280.97897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.index", "start": 1748249280.979545, "relative_start": 7.005248069763184, "end": 1748249280.979545, "relative_end": 1748249280.979545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.backdrop", "start": 1748249280.980572, "relative_start": 7.006274938583374, "end": 1748249280.980572, "relative_end": 1748249280.980572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle", "start": 1748249280.981689, "relative_start": 7.007391929626465, "end": 1748249280.981689, "relative_end": 1748249280.981689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249280.982422, "relative_start": 7.008125066757202, "end": 1748249280.982422, "relative_end": 1748249280.982422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.983633, "relative_start": 7.009335994720459, "end": 1748249280.983633, "relative_end": 1748249280.983633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.panel-left", "start": 1748249280.98418, "relative_start": 7.009882926940918, "end": 1748249280.98418, "relative_end": 1748249280.98418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.984714, "relative_start": 7.0104169845581055, "end": 1748249280.984714, "relative_end": 1748249280.984714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.985218, "relative_start": 7.010921001434326, "end": 1748249280.985218, "relative_end": 1748249280.985218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249280.985725, "relative_start": 7.011427879333496, "end": 1748249280.985725, "relative_end": 1748249280.985725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::profile", "start": 1748249280.986173, "relative_start": 7.011875867843628, "end": 1748249280.986173, "relative_end": 1748249280.986173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index", "start": 1748249280.986839, "relative_start": 7.012542009353638, "end": 1748249280.986839, "relative_end": 1748249280.986839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249280.989003, "relative_start": 7.0147058963775635, "end": 1748249280.989003, "relative_end": 1748249280.989003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249280.991387, "relative_start": 7.01708984375, "end": 1748249280.991387, "relative_end": 1748249280.991387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.992246, "relative_start": 7.017948865890503, "end": 1748249280.992246, "relative_end": 1748249280.992246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down", "start": 1748249280.99294, "relative_start": 7.018642902374268, "end": 1748249280.99294, "relative_end": 1748249280.99294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "start": 1748249280.993863, "relative_start": 7.019566059112549, "end": 1748249280.993863, "relative_end": 1748249280.993863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": 1748249280.994319, "relative_start": 7.020021915435791, "end": 1748249280.994319, "relative_end": 1748249280.994319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": 1748249280.994758, "relative_start": 7.020460844039917, "end": 1748249280.994758, "relative_end": 1748249280.994758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": 1748249280.995417, "relative_start": 7.021120071411133, "end": 1748249280.995417, "relative_end": 1748249280.995417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": 1748249280.996366, "relative_start": 7.022068977355957, "end": 1748249280.996366, "relative_end": 1748249280.996366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.997385, "relative_start": 7.023087978363037, "end": 1748249280.997385, "relative_end": 1748249280.997385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun", "start": 1748249280.997945, "relative_start": 7.023648023605347, "end": 1748249280.997945, "relative_end": 1748249280.997945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": 1748249280.998462, "relative_start": 7.024164915084839, "end": 1748249280.998462, "relative_end": 1748249280.998462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": 1748249280.998958, "relative_start": 7.024661064147949, "end": 1748249280.998958, "relative_end": 1748249280.998958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249280.99963, "relative_start": 7.025332927703857, "end": 1748249280.99963, "relative_end": 1748249280.99963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon", "start": **********.000093, "relative_start": 7.025795936584473, "end": **********.000093, "relative_end": **********.000093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": **********.00059, "relative_start": 7.026293039321899, "end": **********.00059, "relative_end": **********.00059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": **********.001091, "relative_start": 7.026793956756592, "end": **********.001091, "relative_end": **********.001091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.001738, "relative_start": 7.027441024780273, "end": **********.001738, "relative_end": **********.001738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop", "start": **********.002222, "relative_start": 7.02792501449585, "end": **********.002222, "relative_end": **********.002222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index", "start": **********.00267, "relative_start": 7.0283730030059814, "end": **********.00267, "relative_end": **********.00267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented", "start": **********.003135, "relative_start": 7.0288379192352295, "end": **********.003135, "relative_end": **********.003135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.003693, "relative_start": 7.029396057128906, "end": **********.003693, "relative_end": **********.003693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": **********.006377, "relative_start": 7.0320799350738525, "end": **********.006377, "relative_end": **********.006377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": **********.007443, "relative_start": 7.033145904541016, "end": **********.007443, "relative_end": **********.007443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "start": **********.00839, "relative_start": 7.034092903137207, "end": **********.00839, "relative_end": **********.00839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.009938, "relative_start": 7.0356409549713135, "end": **********.009938, "relative_end": **********.009938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog", "start": **********.010959, "relative_start": 7.036661863327026, "end": **********.010959, "relative_end": **********.010959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.011741, "relative_start": 7.0374438762664795, "end": **********.011741, "relative_end": **********.011741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "start": **********.012391, "relative_start": 7.0380940437316895, "end": **********.012391, "relative_end": **********.012391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": **********.012747, "relative_start": 7.038450002670288, "end": **********.012747, "relative_end": **********.012747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": **********.01328, "relative_start": 7.03898286819458, "end": **********.01328, "relative_end": **********.01328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "start": **********.014346, "relative_start": 7.040048837661743, "end": **********.014346, "relative_end": **********.014346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.01544, "relative_start": 7.04114294052124, "end": **********.01544, "relative_end": **********.01544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle", "start": **********.01596, "relative_start": 7.041662931442261, "end": **********.01596, "relative_end": **********.01596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.016539, "relative_start": 7.042242050170898, "end": **********.016539, "relative_end": **********.016539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.index", "start": **********.017234, "relative_start": 7.0429370403289795, "end": **********.017234, "relative_end": **********.017234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::dropdown", "start": **********.017642, "relative_start": 7.043344974517822, "end": **********.017642, "relative_end": **********.017642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::header", "start": **********.018075, "relative_start": 7.043777942657471, "end": **********.018075, "relative_end": **********.018075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::toast.index", "start": **********.018823, "relative_start": 7.044525861740112, "end": **********.018823, "relative_end": **********.018823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 32411360, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 482, "nb_templates": 482, "templates": [{"name": "1x roles", "param_count": null, "params": [], "start": **********.365022, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/roles.blade.phproles", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Froles.blade.php&line=1", "ajax": false, "filename": "roles.blade.php", "line": "?"}, "render_count": 1, "name_original": "roles"}, {"name": "1x livewire.role-table", "param_count": null, "params": [], "start": **********.40869, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/role-table.blade.phplivewire.role-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Frole-table.blade.php&line=1", "ajax": false, "filename": "role-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.role-table"}, {"name": "9x e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "param_count": null, "params": [], "start": 1748249278.11713, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 9, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::heading"}, {"name": "35x e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "param_count": null, "params": [], "start": 1748249278.118212, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 35, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button.index"}, {"name": "50x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "param_count": null, "params": [], "start": 1748249278.120131, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 50, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "param_count": null, "params": [], "start": 1748249278.120923, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/plus.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fplus.blade.php&line=1", "ajax": false, "filename": "plus.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus"}, {"name": "51x e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "param_count": null, "params": [], "start": 1748249278.12171, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}, "render_count": 51, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link"}, {"name": "37x e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "param_count": null, "params": [], "start": 1748249278.123078, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}, "render_count": 37, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip"}, {"name": "8x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "param_count": null, "params": [], "start": 1748249278.123771, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/trigger.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Ftrigger.blade.php&line=1", "ajax": false, "filename": "trigger.blade.php", "line": "?"}, "render_count": 8, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger"}, {"name": "5x livewire.role-form", "param_count": null, "params": [], "start": 1748249278.129769, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/role-form.blade.phplivewire.role-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Frole-form.blade.php&line=1", "ajax": false, "filename": "role-form.blade.php", "line": "?"}, "render_count": 5, "name_original": "livewire.role-form"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::label", "param_count": null, "params": [], "start": 1748249280.364291, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::label"}, {"name": "6x e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "param_count": null, "params": [], "start": 1748249280.364969, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.index"}, {"name": "8x e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "param_count": null, "params": [], "start": 1748249280.367434, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}, "render_count": 8, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-field"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::error", "param_count": null, "params": [], "start": 1748249280.368475, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::error"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::field", "param_count": null, "params": [], "start": 1748249280.36919, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::field"}, {"name": "10x components.option", "param_count": null, "params": [], "start": 1748249280.370346, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/option.blade.phpcomponents.option", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Foption.blade.php&line=1", "ajax": false, "filename": "option.blade.php", "line": "?"}, "render_count": 10, "name_original": "components.option"}, {"name": "5x components.select", "param_count": null, "params": [], "start": 1748249280.37249, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/select.blade.phpcomponents.select", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 5, "name_original": "components.select"}, {"name": "10x e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "param_count": null, "params": [], "start": 1748249280.379084, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/spacer.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fspacer.blade.php&line=1", "ajax": false, "filename": "spacer.blade.php", "line": "?"}, "render_count": 10, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::spacer"}, {"name": "16x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "param_count": null, "params": [], "start": 1748249280.383882, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/close.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 16, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close"}, {"name": "9x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "param_count": null, "params": [], "start": 1748249280.386945, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 9, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading"}, {"name": "8x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "param_count": null, "params": [], "start": 1748249280.391045, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 8, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index"}, {"name": "10x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "param_count": null, "params": [], "start": 1748249280.396188, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/x-mark.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fx-mark.blade.php&line=1", "ajax": false, "filename": "x-mark.blade.php", "line": "?"}, "render_count": 10, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "param_count": null, "params": [], "start": 1748249280.408968, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/magnifying-glass.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmagnifying-glass.blade.php&line=1", "ajax": false, "filename": "magnifying-glass.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "param_count": null, "params": [], "start": 1748249280.410901, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/clearable.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Fclearable.blade.php&line=1", "ajax": false, "filename": "clearable.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable"}, {"name": "5x components.table.column", "param_count": null, "params": [], "start": 1748249280.415706, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/column.blade.phpcomponents.table.column", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}, "render_count": 5, "name_original": "components.table.column"}, {"name": "1x components.table.columns", "param_count": null, "params": [], "start": 1748249280.418295, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/columns.blade.phpcomponents.table.columns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcolumns.blade.php&line=1", "ajax": false, "filename": "columns.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.columns"}, {"name": "20x components.table.cell", "param_count": null, "params": [], "start": 1748249280.418952, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/cell.blade.phpcomponents.table.cell", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}, "render_count": 20, "name_original": "components.table.cell"}, {"name": "8x e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "param_count": null, "params": [], "start": 1748249280.420166, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/badge/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbadge%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 8, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index"}, {"name": "8x e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "param_count": null, "params": [], "start": 1748249280.423369, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-div.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-div.blade.php&line=1", "ajax": false, "filename": "button-or-div.blade.php", "line": "?"}, "render_count": 8, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div"}, {"name": "4x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "param_count": null, "params": [], "start": 1748249280.429031, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/pencil-square.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fpencil-square.blade.php&line=1", "ajax": false, "filename": "pencil-square.blade.php", "line": "?"}, "render_count": 4, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square"}, {"name": "3x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "param_count": null, "params": [], "start": 1748249280.518848, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/trash.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Ftrash.blade.php&line=1", "ajax": false, "filename": "trash.blade.php", "line": "?"}, "render_count": 3, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash"}, {"name": "3x e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "param_count": null, "params": [], "start": 1748249280.523432, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/subheading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsubheading.blade.php&line=1", "ajax": false, "filename": "subheading.blade.php", "line": "?"}, "render_count": 3, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::subheading"}, {"name": "4x components.table.row", "param_count": null, "params": [], "start": 1748249280.541763, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/row.blade.phpcomponents.table.row", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}, "render_count": 4, "name_original": "components.table.row"}, {"name": "1x components.table.rows", "param_count": null, "params": [], "start": 1748249280.866942, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/rows.blade.phpcomponents.table.rows", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Frows.blade.php&line=1", "ajax": false, "filename": "rows.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.rows"}, {"name": "1x components.table.index", "param_count": null, "params": [], "start": 1748249280.867798, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/index.blade.phpcomponents.table.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.index"}, {"name": "1x livewire::tailwind", "param_count": null, "params": [], "start": 1748249280.868802, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire::tailwind"}, {"name": "1x components.card", "param_count": null, "params": [], "start": 1748249280.869927, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.card"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "param_count": null, "params": [], "start": 1748249280.870719, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/card/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::card.index"}, {"name": "1x components.layouts.app", "param_count": null, "params": [], "start": 1748249280.879885, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app.blade.phpcomponents.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::main", "param_count": null, "params": [], "start": 1748249280.880854, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/main.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::main", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmain.blade.php&line=1", "ajax": false, "filename": "main.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::main"}, {"name": "1x components.layouts.app.sidebar", "param_count": null, "params": [], "start": 1748249280.881467, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app/sidebar.blade.phpcomponents.layouts.app.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app.sidebar"}, {"name": "1x partials.head", "param_count": null, "params": [], "start": 1748249280.885445, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/partials/head.blade.phppartials.head", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fpartials%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.head"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle", "param_count": null, "params": [], "start": 1748249280.886791, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/toggle.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Ftoggle.blade.php&line=1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle"}, {"name": "1x components.app-logo", "param_count": null, "params": [], "start": 1748249280.897406, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/app-logo.blade.phpcomponents.app-logo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fapp-logo.blade.php&line=1", "ajax": false, "filename": "app-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.app-logo"}, {"name": "10x e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "param_count": null, "params": [], "start": 1748249280.898812, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/item.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 10, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.layout-dashboard", "param_count": null, "params": [], "start": 1748249280.900331, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/layout-dashboard.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.layout-dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Flayout-dashboard.blade.php&line=1", "ajax": false, "filename": "layout-dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.layout-dashboard"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.user", "param_count": null, "params": [], "start": 1748249280.903116, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/user.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.user", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.user"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.currency-dollar", "param_count": null, "params": [], "start": 1748249280.907752, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/currency-dollar.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.currency-dollar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcurrency-dollar.blade.php&line=1", "ajax": false, "filename": "currency-dollar.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.currency-dollar"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.scroll-text", "param_count": null, "params": [], "start": 1748249280.912077, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/scroll-text.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.scroll-text", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fscroll-text.blade.php&line=1", "ajax": false, "filename": "scroll-text.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.scroll-text"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.queue-list", "param_count": null, "params": [], "start": 1748249280.916403, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/queue-list.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.queue-list", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fqueue-list.blade.php&line=1", "ajax": false, "filename": "queue-list.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.queue-list"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.tags", "param_count": null, "params": [], "start": 1748249280.919567, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/tags.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.tags", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ftags.blade.php&line=1", "ajax": false, "filename": "tags.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.tags"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group", "param_count": null, "params": [], "start": 1748249280.920536, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/navlist/group.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.shield-check", "param_count": null, "params": [], "start": 1748249280.925402, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/shield-check.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.shield-check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fshield-check.blade.php&line=1", "ajax": false, "filename": "shield-check.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.shield-check"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.key", "param_count": null, "params": [], "start": 1748249280.930847, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/key.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.key", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fkey.blade.php&line=1", "ajax": false, "filename": "key.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.key"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.users", "param_count": null, "params": [], "start": 1748249280.93404, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/users.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.users", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fusers.blade.php&line=1", "ajax": false, "filename": "users.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.users"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-down", "param_count": null, "params": [], "start": 1748249280.935857, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-down.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-down"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-right", "param_count": null, "params": [], "start": 1748249280.936596, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-right.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-right", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-right.blade.php&line=1", "ajax": false, "filename": "chevron-right.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-right"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index", "param_count": null, "params": [], "start": 1748249280.937125, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.folder-git-2", "param_count": null, "params": [], "start": 1748249280.941768, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/folder-git-2.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.folder-git-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ffolder-git-2.blade.php&line=1", "ajax": false, "filename": "folder-git-2.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.folder-git-2"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::profile", "param_count": null, "params": [], "start": 1748249280.945337, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/profile.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::profile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fprofile.blade.php&line=1", "ajax": false, "filename": "profile.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::profile"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index", "param_count": null, "params": [], "start": 1748249280.946646, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/avatar/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Favatar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down", "param_count": null, "params": [], "start": 1748249280.9505, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/chevrons-up-down.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevrons-up-down.blade.php&line=1", "ajax": false, "filename": "chevrons-up-down.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down"}, {"name": "4x e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "param_count": null, "params": [], "start": 1748249280.951397, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/radio/group.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fradio%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 4, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group"}, {"name": "6x e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "param_count": null, "params": [], "start": 1748249280.951883, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/separator.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator"}, {"name": "6x e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "param_count": null, "params": [], "start": 1748249280.952521, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/separator.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::separator"}, {"name": "6x e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "param_count": null, "params": [], "start": 1748249280.953286, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index"}, {"name": "6x e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "param_count": null, "params": [], "start": 1748249280.953997, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/variants/segmented.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 6, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun", "param_count": null, "params": [], "start": 1748249280.956873, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/sun.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon", "param_count": null, "params": [], "start": 1748249280.96075, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/moon.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop", "param_count": null, "params": [], "start": 1748249280.964293, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/computer-desktop.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index", "param_count": null, "params": [], "start": 1748249280.964857, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented", "param_count": null, "params": [], "start": 1748249280.965561, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/variants/segmented.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented"}, {"name": "4x e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "param_count": null, "params": [], "start": 1748249280.968049, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/item.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog", "param_count": null, "params": [], "start": 1748249280.969639, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/cog.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcog.blade.php&line=1", "ajax": false, "filename": "cog.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle", "param_count": null, "params": [], "start": 1748249280.976712, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/arrow-right-start-on-rectangle.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Farrow-right-start-on-rectangle.blade.php&line=1", "ajax": false, "filename": "arrow-right-start-on-rectangle.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::menu.index", "param_count": null, "params": [], "start": 1748249280.978331, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::menu.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::menu.index"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::dropdown", "param_count": null, "params": [], "start": 1748249280.978917, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/dropdown.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::dropdown"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.index", "param_count": null, "params": [], "start": 1748249280.979491, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.backdrop", "param_count": null, "params": [], "start": 1748249280.980501, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/backdrop.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.backdrop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Fbackdrop.blade.php&line=1", "ajax": false, "filename": "backdrop.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.backdrop"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.panel-left", "param_count": null, "params": [], "start": 1748249280.98413, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/panel-left.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.panel-left", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fpanel-left.blade.php&line=1", "ajax": false, "filename": "panel-left.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.panel-left"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::header", "param_count": null, "params": [], "start": **********.018022, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/header.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::header"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::toast.index", "param_count": null, "params": [], "start": **********.018773, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/toast/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::toast.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ftoast%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::toast.index"}]}, "queries": {"count": 14, "nb_statements": 13, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01606, "accumulated_duration_str": "16.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 131}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 111}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 60}, {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 88}], "start": **********.304271, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CacheManager.php:245", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheManager.php&line=245", "ajax": false, "filename": "CacheManager.php", "line": "245"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn' limit 1", "type": "query", "params": [], "bindings": ["dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.3276649, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 16.687}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.351612, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 16.687, "width_percent": 3.238}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/RoleTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleTable.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.394074, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "RoleTable.php:59", "source": {"index": 16, "namespace": null, "name": "app/Livewire/RoleTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleTable.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FRoleTable.php&line=59", "ajax": false, "filename": "RoleTable.php", "line": "59"}, "connection": "daily", "explain": null, "start_percent": 19.925, "width_percent": 16.625}, {"sql": "select `roles`.*, (select count(*) from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `roles`.`id` = `role_has_permissions`.`role_id`) as `permissions_count`, (select count(*) from `users` inner join `model_has_roles` on `users`.`id` = `model_has_roles`.`model_id` where `roles`.`id` = `model_has_roles`.`role_id` and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User') as `users_count` from `roles` order by `name` asc limit 15 offset 0", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/RoleTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleTable.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.3995929, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "RoleTable.php:59", "source": {"index": 16, "namespace": null, "name": "app/Livewire/RoleTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleTable.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FRoleTable.php&line=59", "ajax": false, "filename": "RoleTable.php", "line": "59"}, "connection": "daily", "explain": null, "start_percent": 36.55, "width_percent": 8.095}, {"sql": "select * from `permissions` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1748249278.125477, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "RoleForm.php:96", "source": {"index": 15, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FRoleForm.php&line=96", "ajax": false, "filename": "RoleForm.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 44.645, "width_percent": 4.981}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 3", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1748249280.432138, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "RoleForm.php:53", "source": {"index": 20, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FRoleForm.php&line=53", "ajax": false, "filename": "RoleForm.php", "line": "53"}, "connection": "daily", "explain": null, "start_percent": 49.626, "width_percent": 6.351}, {"sql": "select * from `permissions` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1748249280.4386349, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "RoleForm.php:96", "source": {"index": 15, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FRoleForm.php&line=96", "ajax": false, "filename": "RoleForm.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 55.978, "width_percent": 10.212}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1748249280.557168, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "RoleForm.php:53", "source": {"index": 20, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FRoleForm.php&line=53", "ajax": false, "filename": "RoleForm.php", "line": "53"}, "connection": "daily", "explain": null, "start_percent": 66.189, "width_percent": 8.78}, {"sql": "select * from `permissions` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1748249280.56562, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "RoleForm.php:96", "source": {"index": 15, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FRoleForm.php&line=96", "ajax": false, "filename": "RoleForm.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 74.969, "width_percent": 5.604}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1748249280.718188, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "RoleForm.php:53", "source": {"index": 20, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FRoleForm.php&line=53", "ajax": false, "filename": "RoleForm.php", "line": "53"}, "connection": "daily", "explain": null, "start_percent": 80.573, "width_percent": 5.168}, {"sql": "select * from `permissions` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1748249280.7238219, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "RoleForm.php:96", "source": {"index": 15, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FRoleForm.php&line=96", "ajax": false, "filename": "RoleForm.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 85.741, "width_percent": 5.853}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 5", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1748249280.7985718, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "RoleForm.php:53", "source": {"index": 20, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FRoleForm.php&line=53", "ajax": false, "filename": "RoleForm.php", "line": "53"}, "connection": "daily", "explain": null, "start_percent": 91.594, "width_percent": 4.795}, {"sql": "select * from `permissions` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1748249280.801656, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "RoleForm.php:96", "source": {"index": 15, "namespace": null, "name": "app/Livewire/RoleForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\RoleForm.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FRoleForm.php&line=96", "ajax": false, "filename": "RoleForm.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 96.389, "width_percent": 3.611}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 211, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 216, "is_counter": true}, "livewire": {"data": {"role-table #sGnBcNfmlRZcBOXGGQGj": "array:4 [\n  \"data\" => array:2 [\n    \"search\" => \"\"\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"role-table\"\n  \"component\" => \"App\\Livewire\\RoleTable\"\n  \"id\" => \"sGnBcNfmlRZcBOXGGQGj\"\n]", "role-form #G4oBPO52ZLWpppIFxy8w": "array:4 [\n  \"data\" => array:5 [\n    \"show_role_form\" => false\n    \"role\" => null\n    \"name\" => \"\"\n    \"guard_name\" => \"web\"\n    \"permissions\" => []\n  ]\n  \"name\" => \"role-form\"\n  \"component\" => \"App\\Livewire\\RoleForm\"\n  \"id\" => \"G4oBPO52ZLWpppIFxy8w\"\n]", "role-form #JKj4FY3XUGt9gCZoRLb7": "array:4 [\n  \"data\" => array:5 [\n    \"show_role_form\" => false\n    \"role\" => Spatie\\Permission\\Models\\Role {#1740\n      #connection: \"mysql\"\n      #table: \"roles\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 3\n        \"name\" => \"manager\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"permissions_count\" => 13\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 3\n        \"name\" => \"manager\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"permissions_count\" => 13\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"permissions\" => Illuminate\\Database\\Eloquent\\Collection {#2001\n          #items: array:13 [\n            0 => Spatie\\Permission\\Models\\Permission {#2003\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 1\n                \"name\" => \"view transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 1\n                \"name\" => \"view transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 3\n                \"pivot_permission_id\" => 1\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2013\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 1\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 1\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1740}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2038\n                    #connection: \"mysql\"\n                    #table: \"permissions\"\n                    #primaryKey: \"id\"\n                    #keyType: \"int\"\n                    +incrementing: true\n                    #with: []\n                    #withCount: []\n                    +preventsLazyLoading: false\n                    #perPage: 15\n                    +exists: false\n                    +wasRecentlyCreated: false\n                    #escapeWhenCastingToString: false\n                    #attributes: array:1 [\n                      \"guard_name\" => \"web\"\n                    ]\n                    #original: []\n                    #changes: []\n                    #previous: []\n                    #casts: []\n                    #classCastCache: []\n                    #attributeCastCache: []\n                    #dateFormat: null\n                    #appends: []\n                    #dispatchesEvents: []\n                    #observables: []\n                    #relations: []\n                    #touches: []\n                    #relationAutoloadCallback: null\n                    #relationAutoloadContext: null\n                    +timestamps: true\n                    +usesUniqueIds: false\n                    #hidden: []\n                    #visible: []\n                    #fillable: []\n                    #guarded: array:1 [\n                      0 => \"id\"\n                    ]\n                    -roleClass: null\n                    -permissionClass: null\n                    -wildcardClass: null\n                    -wildcardPermissionsIndex: ? array\n                  }\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            1 => Spatie\\Permission\\Models\\Permission {#2000\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 2\n                \"name\" => \"create transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 2\n                \"name\" => \"create transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 3\n                \"pivot_permission_id\" => 2\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2002\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 2\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 2\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1740}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2038}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            2 => Spatie\\Permission\\Models\\Permission {#1926\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 3\n                \"name\" => \"edit transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 3\n                \"name\" => \"edit transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 3\n                \"pivot_permission_id\" => 3\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1999\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 3\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 3\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1740}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2038}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            3 => Spatie\\Permission\\Models\\Permission {#1924\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 5\n                \"name\" => \"view accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 5\n                \"name\" => \"view accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 3\n                \"pivot_permission_id\" => 5\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2009\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 5\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 5\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1740}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2038}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            4 => Spatie\\Permission\\Models\\Permission {#1915\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 6\n                \"name\" => \"create accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 6\n                \"name\" => \"create accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 3\n                \"pivot_permission_id\" => 6\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1988\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 6\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 6\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1740}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2038}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            5 => Spatie\\Permission\\Models\\Permission {#1916\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 7\n                \"name\" => \"edit accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 7\n                \"name\" => \"edit accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 3\n                \"pivot_permission_id\" => 7\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2011\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 7\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 7\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1740}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2038}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            6 => Spatie\\Permission\\Models\\Permission {#1917\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 9\n                \"name\" => \"view categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 9\n                \"name\" => \"view categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 3\n                \"pivot_permission_id\" => 9\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2018\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 9\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 9\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1740}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2038}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            7 => Spatie\\Permission\\Models\\Permission {#1918\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 10\n                \"name\" => \"create categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 10\n                \"name\" => \"create categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 3\n                \"pivot_permission_id\" => 10\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2016\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 10\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 10\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1740}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2038}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            8 => Spatie\\Permission\\Models\\Permission {#1919\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 11\n                \"name\" => \"edit categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 11\n                \"name\" => \"edit categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 3\n                \"pivot_permission_id\" => 11\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2006\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 11\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 11\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1740}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2038}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            9 => Spatie\\Permission\\Models\\Permission {#1920\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 13\n                \"name\" => \"view tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 13\n                \"name\" => \"view tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 3\n                \"pivot_permission_id\" => 13\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2023\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 13\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 13\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1740}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2038}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            10 => Spatie\\Permission\\Models\\Permission {#1921\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 14\n                \"name\" => \"create tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 14\n                \"name\" => \"create tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 3\n                \"pivot_permission_id\" => 14\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2014\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 14\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 14\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1740}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2038}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            11 => Spatie\\Permission\\Models\\Permission {#1922\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 15\n                \"name\" => \"edit tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 15\n                \"name\" => \"edit tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 3\n                \"pivot_permission_id\" => 15\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2022\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 15\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 15\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1740}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2038}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            12 => Spatie\\Permission\\Models\\Permission {#1925\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 30\n                \"name\" => \"view settings\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n              ]\n              #original: array:7 [\n                \"id\" => 30\n                \"name\" => \"view settings\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n                \"pivot_role_id\" => 3\n                \"pivot_permission_id\" => 30\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2026\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 30\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 3\n                    \"permission_id\" => 30\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1740}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2038}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"manager\"\n    \"guard_name\" => \"web\"\n    \"permissions\" => array:13 [\n      0 => 1\n      1 => 2\n      2 => 3\n      3 => 5\n      4 => 6\n      5 => 7\n      6 => 9\n      7 => 10\n      8 => 11\n      9 => 13\n      10 => 14\n      11 => 15\n      12 => 30\n    ]\n  ]\n  \"name\" => \"role-form\"\n  \"component\" => \"App\\Livewire\\RoleForm\"\n  \"id\" => \"JKj4FY3XUGt9gCZoRLb7\"\n]", "role-form #ypW0q9PIcHpdQ2V5DHhY": "array:4 [\n  \"data\" => array:5 [\n    \"show_role_form\" => false\n    \"role\" => Spatie\\Permission\\Models\\Role {#1727\n      #connection: \"mysql\"\n      #table: \"roles\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 1\n        \"name\" => \"super-admin\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"permissions_count\" => 31\n        \"users_count\" => 1\n      ]\n      #original: array:7 [\n        \"id\" => 1\n        \"name\" => \"super-admin\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"permissions_count\" => 31\n        \"users_count\" => 1\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"permissions\" => Illuminate\\Database\\Eloquent\\Collection {#2328\n          #items: array:31 [\n            0 => Spatie\\Permission\\Models\\Permission {#1928\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 1\n                \"name\" => \"view transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 1\n                \"name\" => \"view transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 1\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2324\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            1 => Spatie\\Permission\\Models\\Permission {#1929\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 2\n                \"name\" => \"create transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 2\n                \"name\" => \"create transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 2\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1930\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            2 => Spatie\\Permission\\Models\\Permission {#2320\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 3\n                \"name\" => \"edit transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 3\n                \"name\" => \"edit transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 3\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1931\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            3 => Spatie\\Permission\\Models\\Permission {#2078\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 4\n                \"name\" => \"delete transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 4\n                \"name\" => \"delete transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 4\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1932\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            4 => Spatie\\Permission\\Models\\Permission {#2327\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 5\n                \"name\" => \"view accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 5\n                \"name\" => \"view accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 5\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1933\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            5 => Spatie\\Permission\\Models\\Permission {#2053\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 6\n                \"name\" => \"create accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 6\n                \"name\" => \"create accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 6\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1936\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            6 => Spatie\\Permission\\Models\\Permission {#2046\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 7\n                \"name\" => \"edit accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 7\n                \"name\" => \"edit accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 7\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1935\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            7 => Spatie\\Permission\\Models\\Permission {#2076\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 8\n                \"name\" => \"delete accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 8\n                \"name\" => \"delete accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 8\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1977\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            8 => Spatie\\Permission\\Models\\Permission {#2059\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 9\n                \"name\" => \"view categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 9\n                \"name\" => \"view categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 9\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1946\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            9 => Spatie\\Permission\\Models\\Permission {#2074\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 10\n                \"name\" => \"create categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 10\n                \"name\" => \"create categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 10\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1943\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            10 => Spatie\\Permission\\Models\\Permission {#2089\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 11\n                \"name\" => \"edit categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 11\n                \"name\" => \"edit categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 11\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1971\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            11 => Spatie\\Permission\\Models\\Permission {#2105\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 12\n                \"name\" => \"delete categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 12\n                \"name\" => \"delete categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 12\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1975\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            12 => Spatie\\Permission\\Models\\Permission {#1945\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 13\n                \"name\" => \"view tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 13\n                \"name\" => \"view tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 13\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1976\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            13 => Spatie\\Permission\\Models\\Permission {#2087\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 14\n                \"name\" => \"create tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 14\n                \"name\" => \"create tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 14\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1973\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            14 => Spatie\\Permission\\Models\\Permission {#2086\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 15\n                \"name\" => \"edit tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 15\n                \"name\" => \"edit tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 15\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1972\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            15 => Spatie\\Permission\\Models\\Permission {#2085\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 16\n                \"name\" => \"delete tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 16\n                \"name\" => \"delete tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 16\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1974\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            16 => Spatie\\Permission\\Models\\Permission {#2084\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 17\n                \"name\" => \"view users\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 17\n                \"name\" => \"view users\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 17\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1934\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            17 => Spatie\\Permission\\Models\\Permission {#2083\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 18\n                \"name\" => \"create users\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 18\n                \"name\" => \"create users\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 18\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2010\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            18 => Spatie\\Permission\\Models\\Permission {#2082\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 19\n                \"name\" => \"edit users\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 19\n                \"name\" => \"edit users\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 19\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2024\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            19 => Spatie\\Permission\\Models\\Permission {#2081\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 20\n                \"name\" => \"delete users\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 20\n                \"name\" => \"delete users\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 20\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2044\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            20 => Spatie\\Permission\\Models\\Permission {#1913\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 21\n                \"name\" => \"view roles\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 21\n                \"name\" => \"view roles\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 21\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1944\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1727}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2252 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            21 => Spatie\\Permission\\Models\\Permission {#1949\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 22\n                \"name\" => \"create roles\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 22\n                \"name\" => \"create roles\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 22\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2041\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                   …16\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            22 => Spatie\\Permission\\Models\\Permission {#1948\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 23\n                \"name\" => \"edit roles\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 23\n                \"name\" => \"edit roles\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 23\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1995 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            23 => Spatie\\Permission\\Models\\Permission {#1912\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 24\n                \"name\" => \"delete roles\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 24\n                \"name\" => \"delete roles\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 24\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1994 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            24 => Spatie\\Permission\\Models\\Permission {#1923\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 25\n                \"name\" => \"view permissions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 25\n                \"name\" => \"view permissions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 25\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2095 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            25 => Spatie\\Permission\\Models\\Permission {#2027\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 26\n                \"name\" => \"create permissions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n              ]\n              #original: array:7 [\n                \"id\" => 26\n                \"name\" => \"create permissions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 26\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2323 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            26 => Spatie\\Permission\\Models\\Permission {#2079\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 27\n                \"name\" => \"edit permissions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n              ]\n              #original: array:7 [\n                \"id\" => 27\n                \"name\" => \"edit permissions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 27\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2325 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            27 => Spatie\\Permission\\Models\\Permission {#2045\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 28\n                \"name\" => \"delete permissions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n              ]\n              #original: array:7 [\n                \"id\" => 28\n                \"name\" => \"delete permissions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 28\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2321 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            28 => Spatie\\Permission\\Models\\Permission {#2319\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 29\n                \"name\" => \"assign roles\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n              ]\n              #original: array:7 [\n                \"id\" => 29\n                \"name\" => \"assign roles\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 29\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1942 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            29 => Spatie\\Permission\\Models\\Permission {#2264\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 30\n                \"name\" => \"view settings\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n              ]\n              #original: array:7 [\n                \"id\" => 30\n                \"name\" => \"view settings\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 30\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1947 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            30 => Spatie\\Permission\\Models\\Permission {#2251\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 31\n                \"name\" => \"edit settings\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n              ]\n              #original: array:7 [\n                \"id\" => 31\n                \"name\" => \"edit settings\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 31\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2243 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"super-admin\"\n    \"guard_name\" => \"web\"\n    \"permissions\" => array:31 [\n      0 => 1\n      1 => 2\n      2 => 3\n      3 => 4\n      4 => 5\n      5 => 6\n      6 => 7\n      7 => 8\n      8 => 9\n      9 => 10\n      10 => 11\n      11 => 12\n      12 => 13\n      13 => 14\n      14 => 15\n      15 => 16\n      16 => 17\n      17 => 18\n      18 => 19\n      19 => 20\n      20 => 21\n      21 => 22\n      22 => 23\n      23 => 24\n      24 => 25\n      25 => 26\n      26 => 27\n      27 => 28\n      28 => 29\n      29 => 30\n      30 => 31\n    ]\n  ]\n  \"name\" => \"role-form\"\n  \"component\" => \"App\\Livewire\\RoleForm\"\n  \"id\" => \"ypW0q9PIcHpdQ2V5DHhY\"\n]", "role-form #Db4Ncu1V4v7jdJsc8pkU": "array:4 [\n  \"data\" => array:5 [\n    \"show_role_form\" => false\n    \"role\" => Spatie\\Permission\\Models\\Role {#1728\n      #connection: \"mysql\"\n      #table: \"roles\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 4\n        \"name\" => \"user\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"permissions_count\" => 7\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 4\n        \"name\" => \"user\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"permissions_count\" => 7\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"permissions\" => Illuminate\\Database\\Eloquent\\Collection {#2065\n          #items: array:7 [\n            0 => Spatie\\Permission\\Models\\Permission {#2132\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 1\n                \"name\" => \"view transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 1\n                \"name\" => \"view transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 4\n                \"pivot_permission_id\" => 1\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2472\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 1\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 1\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1728}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2457\n                    #connection: \"mysql\"\n                    #table: \"permissions\"\n                    #primaryKey: \"id\"\n                    #keyType: \"int\"\n                    +incrementing: true\n                    #with: []\n                    #withCount: []\n                    +preventsLazyLoading: false\n                    #perPage: 15\n                    +exists: false\n                    +wasRecentlyCreated: false\n                    #escapeWhenCastingToString: false\n                    #attributes: array:1 [\n                      \"guard_name\" => \"web\"\n                    ]\n                    #original: []\n                    #changes: []\n                    #previous: []\n                    #casts: []\n                    #classCastCache: []\n                    #attributeCastCache: []\n                    #dateFormat: null\n                    #appends: []\n                    #dispatchesEvents: []\n                    #observables: []\n                    #relations: []\n                    #touches: []\n                    #relationAutoloadCallback: null\n                    #relationAutoloadContext: null\n                    +timestamps: true\n                    +usesUniqueIds: false\n                    #hidden: []\n                    #visible: []\n                    #fillable: []\n                    #guarded: array:1 [\n                      0 => \"id\"\n                    ]\n                    -roleClass: null\n                    -permissionClass: null\n                    -wildcardClass: null\n                    -wildcardPermissionsIndex: ? array\n                  }\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            1 => Spatie\\Permission\\Models\\Permission {#2131\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 2\n                \"name\" => \"create transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 2\n                \"name\" => \"create transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 4\n                \"pivot_permission_id\" => 2\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2094\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 2\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 2\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1728}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2457}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            2 => Spatie\\Permission\\Models\\Permission {#2265\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 3\n                \"name\" => \"edit transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 3\n                \"name\" => \"edit transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 4\n                \"pivot_permission_id\" => 3\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2263\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 3\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 3\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1728}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2457}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            3 => Spatie\\Permission\\Models\\Permission {#2262\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 5\n                \"name\" => \"view accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 5\n                \"name\" => \"view accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 4\n                \"pivot_permission_id\" => 5\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2308\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 5\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 5\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1728}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2457}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            4 => Spatie\\Permission\\Models\\Permission {#2198\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 9\n                \"name\" => \"view categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 9\n                \"name\" => \"view categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 4\n                \"pivot_permission_id\" => 9\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2474\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 9\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 9\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1728}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2457}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            5 => Spatie\\Permission\\Models\\Permission {#2100\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 13\n                \"name\" => \"view tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 13\n                \"name\" => \"view tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 4\n                \"pivot_permission_id\" => 13\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2475\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 13\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 13\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1728}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2457}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            6 => Spatie\\Permission\\Models\\Permission {#2093\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 30\n                \"name\" => \"view settings\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n              ]\n              #original: array:7 [\n                \"id\" => 30\n                \"name\" => \"view settings\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n                \"pivot_role_id\" => 4\n                \"pivot_permission_id\" => 30\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2470\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 30\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 4\n                    \"permission_id\" => 30\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1728}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2457}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"user\"\n    \"guard_name\" => \"web\"\n    \"permissions\" => array:7 [\n      0 => 1\n      1 => 2\n      2 => 3\n      3 => 5\n      4 => 9\n      5 => 13\n      6 => 30\n    ]\n  ]\n  \"name\" => \"role-form\"\n  \"component\" => \"App\\Livewire\\RoleForm\"\n  \"id\" => \"Db4Ncu1V4v7jdJsc8pkU\"\n]", "role-form #7Inw3UNBur0NXkXX32FH": "array:4 [\n  \"data\" => array:5 [\n    \"show_role_form\" => false\n    \"role\" => Spatie\\Permission\\Models\\Role {#1725\n      #connection: \"mysql\"\n      #table: \"roles\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 5\n        \"name\" => \"viewer\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"permissions_count\" => 5\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 5\n        \"name\" => \"viewer\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"permissions_count\" => 5\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"permissions\" => Illuminate\\Database\\Eloquent\\Collection {#2240\n          #items: array:5 [\n            0 => Spatie\\Permission\\Models\\Permission {#2313\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 1\n                \"name\" => \"view transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 1\n                \"name\" => \"view transactions\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 5\n                \"pivot_permission_id\" => 1\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2384\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 5\n                    \"permission_id\" => 1\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 5\n                    \"permission_id\" => 1\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1725}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2403\n                    #connection: \"mysql\"\n                    #table: \"permissions\"\n                    #primaryKey: \"id\"\n                    #keyType: \"int\"\n                    +incrementing: true\n                    #with: []\n                    #withCount: []\n                    +preventsLazyLoading: false\n                    #perPage: 15\n                    +exists: false\n                    +wasRecentlyCreated: false\n                    #escapeWhenCastingToString: false\n                    #attributes: array:1 [\n                      \"guard_name\" => \"web\"\n                    ]\n                    #original: []\n                    #changes: []\n                    #previous: []\n                    #casts: []\n                    #classCastCache: []\n                    #attributeCastCache: []\n                    #dateFormat: null\n                    #appends: []\n                    #dispatchesEvents: []\n                    #observables: []\n                    #relations: []\n                    #touches: []\n                    #relationAutoloadCallback: null\n                    #relationAutoloadContext: null\n                    +timestamps: true\n                    +usesUniqueIds: false\n                    #hidden: []\n                    #visible: []\n                    #fillable: []\n                    #guarded: array:1 [\n                      0 => \"id\"\n                    ]\n                    -roleClass: null\n                    -permissionClass: null\n                    -wildcardClass: null\n                    -wildcardPermissionsIndex: ? array\n                  }\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            1 => Spatie\\Permission\\Models\\Permission {#2453\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 5\n                \"name\" => \"view accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 5\n                \"name\" => \"view accounts\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 5\n                \"pivot_permission_id\" => 5\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2369\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 5\n                    \"permission_id\" => 5\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 5\n                    \"permission_id\" => 5\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1725}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2403}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            2 => Spatie\\Permission\\Models\\Permission {#2091\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 9\n                \"name\" => \"view categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 9\n                \"name\" => \"view categories\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 5\n                \"pivot_permission_id\" => 9\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2386\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 5\n                    \"permission_id\" => 9\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 5\n                    \"permission_id\" => 9\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1725}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2403}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            3 => Spatie\\Permission\\Models\\Permission {#2096\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 13\n                \"name\" => \"view tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n              ]\n              #original: array:7 [\n                \"id\" => 13\n                \"name\" => \"view tags\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:23\"\n                \"updated_at\" => \"2025-05-25 10:32:23\"\n                \"pivot_role_id\" => 5\n                \"pivot_permission_id\" => 13\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2411\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 5\n                    \"permission_id\" => 13\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 5\n                    \"permission_id\" => 13\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1725}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2403}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            4 => Spatie\\Permission\\Models\\Permission {#2314\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 30\n                \"name\" => \"view settings\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n              ]\n              #original: array:7 [\n                \"id\" => 30\n                \"name\" => \"view settings\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-25 10:32:24\"\n                \"updated_at\" => \"2025-05-25 10:32:24\"\n                \"pivot_role_id\" => 5\n                \"pivot_permission_id\" => 30\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2392\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"role_id\" => 5\n                    \"permission_id\" => 30\n                  ]\n                  #original: array:2 [\n                    \"role_id\" => 5\n                    \"permission_id\" => 30\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: Spatie\\Permission\\Models\\Role {#1725}\n                  +pivotRelated: Spatie\\Permission\\Models\\Permission {#2403}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"viewer\"\n    \"guard_name\" => \"web\"\n    \"permissions\" => array:5 [\n      0 => 1\n      1 => 5\n      2 => 9\n      3 => 13\n      4 => 30\n    ]\n  ]\n  \"name\" => \"role-form\"\n  \"component\" => \"App\\Livewire\\RoleForm\"\n  \"id\" => \"7Inw3UNBur0NXkXX32FH\"\n]"}, "count": 6}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/roles", "action_name": "roles", "controller_action": "Closure", "uri": "GET roles", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Froutes%2Fweb.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">routes/web.php:79-81</a>", "middleware": "web, auth", "duration": "7.06s", "peak_memory": "34MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-263396685 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-263396685\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-590641890 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-590641890\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-707568805 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire-navigate</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/tags</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InFPeERqUU1Pb2pLZUl6VlpaWkVmUmc9PSIsInZhbHVlIjoiYjNaNys3MWNCN3NTUkZ5cjJDNXRPOXo3TFdpaDZvVGFvVmFIcVVmcW5sd1JjQWZTdFBTaGR5NzBsOXBCMUFzRDZzdGFLaEQ5NHh2NzhPR2FZa0I2bVRjblVLMytjK2R6KzBETFdCT2NmZG1YdGYwWXd3a1drLzNPYXVVTU9ET2IiLCJtYWMiOiJjOWQxMDk5ODNiY2MwOTQ2NWU5YmMzNjUzNzY0ZTg1ZmRkOWVkNThkZDBjNmMzZGFmYjViOTg1MmM2MzljZTZiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkJ3QW5wUFJnUHI5cExQd242elh3QWc9PSIsInZhbHVlIjoiN2w4K3pRSlJhTFJMSlBObnNVWXcvNnFXZFo5dmxVVlUvZ2JpOGcvVUg1YmlraGsyUGV6b1liUDZCZW94ZGhQMWluUzVwaGdTSzFsQXl1Qm82QmpGaXM1Si9GSS91QVpxazBnTStUUWJwd2JZNWtQRUcvSHJCeWU4Y1plcFhiYVciLCJtYWMiOiIyYWFjYjk2YTY4MTM2YjZiYWM0YmYxOTdjODk4OTgzMDY0ZWY3YTI2ZDIxNWMxMDc2ZWU4ZTgwNGU0MDAzNDVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-707568805\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1423110641 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1423110641\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1726168573 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 26 May 2025 08:47:54 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1726168573\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-73514828 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/tags</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-73514828\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/roles", "action_name": "roles", "controller_action": "Closure"}, "badge": null}}