<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'variant' => 'default',
    'align' => 'left',
    'class' => '',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'variant' => 'default',
    'align' => 'left',
    'class' => '',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$variantClasses = [
    'default' => 'text-zinc-900 dark:text-zinc-100',
    'strong' => 'font-medium text-zinc-900 dark:text-zinc-100',
    'muted' => 'text-zinc-500 dark:text-zinc-400',
];

$alignClasses = [
    'left' => 'text-left',
    'center' => 'text-center',
    'right' => 'text-right',
    'end' => 'text-right',
];

$classes = 'px-3 py-4 text-sm border-b border-zinc-200 dark:border-white/20 ' . 
           ($variantClasses[$variant] ?? $variantClasses['default']) . ' ' . 
           ($alignClasses[$align] ?? 'text-left') . ' ' . 
           $class;
?>

<td <?php echo e($attributes->merge(['class' => $classes])); ?>>
    <?php echo e($slot); ?>

</td>
<?php /**PATH C:\laragon\www\pure-finance\resources\views/components/table/cell.blade.php ENDPATH**/ ?>