<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Models\User;
use Flux\Flux;
use Livewire\Component;
use Livewire\Attributes\On;
use Spatie\Permission\Models\Role;
use Illuminate\Contracts\View\View;

class UserRoleForm extends Component
{
    public ?array $user = null;

    public array $roles = [];

    protected function rules(): array
    {
        return [
            'roles' => ['array'],
            'roles.*' => ['exists:roles,id'],
        ];
    }

    protected function messages(): array
    {
        return [
            'roles.*.exists' => 'One or more selected roles are invalid.',
        ];
    }

    #[On('load-user-roles')]
    public function loadUserRoles(array $user): void
    {
        $this->user = $user;

        // Load user roles
        $userModel = User::find($this->user['id']);
        $this->roles = $userModel ? $userModel->roles->pluck('id')->toArray() : [];
    }

    public function resetForm(): void
    {
        $this->reset(['user', 'roles']);
        $this->resetErrorBag();
        $this->resetValidation();
    }

    public function submit(): void
    {
        $validated_data = $this->validate();

        if ($this->user) {
            $userModel = User::find($this->user['id']);

            if ($userModel) {
                // Sync roles
                $userModel->syncRoles($validated_data['roles']);
            }
        }

        $this->dispatch('user-roles-updated');

        Flux::toast(
            variant: 'success',
            text: 'User roles successfully updated',
        );

        $this->resetForm();
        Flux::modal('user-role-form')->close();
    }

    public function render(): View
    {
        return view('livewire.user-role-form', [
            'availableRoles' => Role::orderBy('name')->get(),
        ]);
    }
}
