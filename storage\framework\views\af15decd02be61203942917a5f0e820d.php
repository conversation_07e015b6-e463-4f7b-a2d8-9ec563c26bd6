<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'sortable' => false,
    'sorted' => false,
    'direction' => 'asc',
    'align' => 'left',
    'class' => '',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'sortable' => false,
    'sorted' => false,
    'direction' => 'asc',
    'align' => 'left',
    'class' => '',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$alignClasses = [
    'left' => 'text-left',
    'center' => 'text-center',
    'right' => 'text-right',
    'end' => 'text-right',
];

$classes = 'px-3 py-3 text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider border-b border-zinc-200 dark:border-white/20 ' . 
           ($alignClasses[$align] ?? 'text-left') . ' ' . 
           ($sortable ? 'cursor-pointer hover:bg-zinc-100 dark:hover:bg-zinc-700 select-none' : '') . ' ' . 
           $class;
?>

<th <?php echo e($attributes->merge(['class' => $classes])); ?>>
    <div class="flex items-center <?php echo e($align === 'end' || $align === 'right' ? 'justify-end' : ($align === 'center' ? 'justify-center' : 'justify-start')); ?>">
        <?php echo e($slot); ?>

        
        <!--[if BLOCK]><![endif]--><?php if($sortable): ?>
            <div class="ml-2 flex flex-col">
                <!--[if BLOCK]><![endif]--><?php if($sorted && $direction === 'asc'): ?>
                    <svg class="w-3 h-3 text-zinc-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                    </svg>
                <?php elseif($sorted && $direction === 'desc'): ?>
                    <svg class="w-3 h-3 text-zinc-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                <?php else: ?>
                    <svg class="w-3 h-3 text-zinc-300 dark:text-zinc-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5 12l5-5 5 5H5z"></path>
                    </svg>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</th>
<?php /**PATH C:\laragon\www\pure-finance\resources\views/components/table/column.blade.php ENDPATH**/ ?>