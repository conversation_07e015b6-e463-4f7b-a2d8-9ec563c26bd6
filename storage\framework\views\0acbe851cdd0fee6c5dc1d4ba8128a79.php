<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name' => $attributes->whereStartsWith('wire:model')->first(),
    'placeholder' => null,
    'invalid' => null,
    'size' => null,
    'multiple' => false,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name' => $attributes->whereStartsWith('wire:model')->first(),
    'placeholder' => null,
    'invalid' => null,
    'size' => null,
    'multiple' => false,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$invalid ??= ($name && $errors->has($name));

// Use very basic styling to avoid conflicts
$baseClasses = 'block w-full px-3 py-2 text-base border rounded-lg shadow-sm focus:outline-none focus:ring-2';
$colorClasses = $invalid
    ? 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500'
    : 'border-gray-300 text-gray-900 placeholder-gray-400 focus:border-indigo-500 focus:ring-indigo-500';
$sizeClasses = match ($size) {
    'sm' => 'h-8 text-sm',
    'xs' => 'h-6 text-xs',
    default => 'h-10',
};
$multipleClasses = $multiple ? 'min-h-[80px]' : '';

$classes = "{$baseClasses} {$colorClasses} {$sizeClasses} {$multipleClasses}";
?>

<select
    <?php echo e($attributes->class($classes)); ?>

    <?php if($invalid): ?> aria-invalid="true" <?php endif; ?>
    <?php if($name): ?> name="<?php echo e($name); ?>" <?php endif; ?>
    <?php if($multiple): ?> multiple <?php endif; ?>
    style="background-image: none !important; -webkit-appearance: menulist !important; -moz-appearance: menulist !important; appearance: menulist !important;"
>
    <!--[if BLOCK]><![endif]--><?php if($placeholder && !$multiple): ?>
        <option value="" disabled selected><?php echo e($placeholder); ?></option>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <?php echo e($slot); ?>

</select>
<?php /**PATH C:\laragon\www\pure-finance\resources\views/components/select.blade.php ENDPATH**/ ?>