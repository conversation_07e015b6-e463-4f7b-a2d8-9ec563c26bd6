<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Transaction permissions
            'view transactions',
            'create transactions',
            'edit transactions',
            'delete transactions',
            
            // Account permissions
            'view accounts',
            'create accounts',
            'edit accounts',
            'delete accounts',
            
            // Category permissions
            'view categories',
            'create categories',
            'edit categories',
            'delete categories',
            
            // Tag permissions
            'view tags',
            'create tags',
            'edit tags',
            'delete tags',
            
            // User management permissions
            'view users',
            'create users',
            'edit users',
            'delete users',
            
            // Role & Permission management
            'view roles',
            'create roles',
            'edit roles',
            'delete roles',
            'view permissions',
            'create permissions',
            'edit permissions',
            'delete permissions',
            'assign roles',
            
            // Settings permissions
            'view settings',
            'edit settings',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        
        // Super Admin - has all permissions
        $superAdmin = Role::create(['name' => 'super-admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Admin - has most permissions except super admin functions
        $admin = Role::create(['name' => 'admin']);
        $admin->givePermissionTo([
            'view transactions', 'create transactions', 'edit transactions', 'delete transactions',
            'view accounts', 'create accounts', 'edit accounts', 'delete accounts',
            'view categories', 'create categories', 'edit categories', 'delete categories',
            'view tags', 'create tags', 'edit tags', 'delete tags',
            'view users', 'edit users',
            'view settings', 'edit settings',
        ]);

        // Manager - can manage transactions and basic data
        $manager = Role::create(['name' => 'manager']);
        $manager->givePermissionTo([
            'view transactions', 'create transactions', 'edit transactions',
            'view accounts', 'create accounts', 'edit accounts',
            'view categories', 'create categories', 'edit categories',
            'view tags', 'create tags', 'edit tags',
            'view settings',
        ]);

        // User - basic permissions
        $user = Role::create(['name' => 'user']);
        $user->givePermissionTo([
            'view transactions', 'create transactions', 'edit transactions',
            'view accounts',
            'view categories',
            'view tags',
            'view settings',
        ]);

        // Viewer - read-only access
        $viewer = Role::create(['name' => 'viewer']);
        $viewer->givePermissionTo([
            'view transactions',
            'view accounts',
            'view categories',
            'view tags',
            'view settings',
        ]);
    }
}
