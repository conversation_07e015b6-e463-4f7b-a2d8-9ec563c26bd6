<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;

class CleanupOrphanedPermissions extends Command
{
    protected $signature = 'permissions:cleanup';
    protected $description = 'Clean up orphaned permission relationships';

    public function handle()
    {
        $this->info('Cleaning up orphaned permission relationships...');

        // Get all valid permission IDs
        $validPermissionIds = Permission::pluck('id')->toArray();
        
        if (empty($validPermissionIds)) {
            $this->error('No permissions found in the database!');
            return 1;
        }

        $this->info('Valid permission IDs: ' . implode(', ', $validPermissionIds));

        // Clean up role_has_permissions table
        $orphanedRolePermissions = DB::table('role_has_permissions')
            ->whereNotIn('permission_id', $validPermissionIds)
            ->count();

        if ($orphanedRolePermissions > 0) {
            $this->warn("Found {$orphanedRolePermissions} orphaned role-permission relationships");
            DB::table('role_has_permissions')
                ->whereNotIn('permission_id', $validPermissionIds)
                ->delete();
            $this->info("Deleted {$orphanedRolePermissions} orphaned role-permission relationships");
        }

        // Clean up model_has_permissions table
        $orphanedModelPermissions = DB::table('model_has_permissions')
            ->whereNotIn('permission_id', $validPermissionIds)
            ->count();

        if ($orphanedModelPermissions > 0) {
            $this->warn("Found {$orphanedModelPermissions} orphaned model-permission relationships");
            DB::table('model_has_permissions')
                ->whereNotIn('permission_id', $validPermissionIds)
                ->delete();
            $this->info("Deleted {$orphanedModelPermissions} orphaned model-permission relationships");
        }

        $this->info('Cleanup completed successfully!');
        return 0;
    }
}
