{"__meta": {"id": "01JW3ENZ64299J9E0QNZT18TT4", "datetime": "2025-05-25 10:09:12", "utime": **********.901685, "method": "GET", "uri": "/planned-spending", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748167750.846232, "end": **********.901699, "duration": 2.055467128753662, "duration_str": "2.06s", "measures": [{"label": "Booting", "start": 1748167750.846232, "relative_start": 0, "end": **********.221485, "relative_end": **********.221485, "duration": 0.*****************, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.221497, "relative_start": 0.*****************, "end": **********.9017, "relative_end": 9.5367431640625e-07, "duration": 1.****************, "duration_str": "1.68s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.250757, "relative_start": 0.****************, "end": **********.254907, "relative_end": **********.254907, "duration": 0.004149913787841797, "duration_str": "4.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.planned-spending", "start": **********.352573, "relative_start": 0.****************, "end": **********.352573, "relative_end": **********.352573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.35781, "relative_start": 0.****************, "end": **********.35781, "relative_end": **********.35781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.363356, "relative_start": 0.5171241760253906, "end": **********.363356, "relative_end": **********.363356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.plus", "start": **********.364896, "relative_start": 0.5186641216278076, "end": **********.364896, "relative_end": **********.364896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.366377, "relative_start": 0.5201451778411865, "end": **********.366377, "relative_end": **********.366377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.367689, "relative_start": 0.5214569568634033, "end": **********.367689, "relative_end": **********.367689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.trigger", "start": **********.368808, "relative_start": 0.522576093673706, "end": **********.368808, "relative_end": **********.368808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.planned-spending-form", "start": **********.39287, "relative_start": 0.546638011932373, "end": **********.39287, "relative_end": **********.39287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.396008, "relative_start": 0.5497760772705078, "end": **********.396008, "relative_end": **********.396008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.39791, "relative_start": 0.5516781806945801, "end": **********.39791, "relative_end": **********.39791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.399549, "relative_start": 0.5533170700073242, "end": **********.399549, "relative_end": **********.399549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.402955, "relative_start": 0.5567231178283691, "end": **********.402955, "relative_end": **********.402955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.404526, "relative_start": 0.5582940578460693, "end": **********.404526, "relative_end": **********.404526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.405639, "relative_start": 0.5594069957733154, "end": **********.405639, "relative_end": **********.405639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.categories", "start": **********.407109, "relative_start": 0.5608770847320557, "end": **********.407109, "relative_end": **********.407109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.52084, "relative_start": 1.6746079921722412, "end": **********.52084, "relative_end": **********.52084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.521682, "relative_start": 1.675450086593628, "end": **********.521682, "relative_end": **********.521682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.522344, "relative_start": 1.676112174987793, "end": **********.522344, "relative_end": **********.522344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.522767, "relative_start": 1.6765351295471191, "end": **********.522767, "relative_end": **********.522767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.523179, "relative_start": 1.6769471168518066, "end": **********.523179, "relative_end": **********.523179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.523553, "relative_start": 1.677320957183838, "end": **********.523553, "relative_end": **********.523553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.523946, "relative_start": 1.6777141094207764, "end": **********.523946, "relative_end": **********.523946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.524342, "relative_start": 1.678110122680664, "end": **********.524342, "relative_end": **********.524342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.524738, "relative_start": 1.6785061359405518, "end": **********.524738, "relative_end": **********.524738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.525133, "relative_start": 1.678900957107544, "end": **********.525133, "relative_end": **********.525133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.52551, "relative_start": 1.6792781352996826, "end": **********.52551, "relative_end": **********.52551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.525897, "relative_start": 1.6796650886535645, "end": **********.525897, "relative_end": **********.525897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.526555, "relative_start": 1.6803231239318848, "end": **********.526555, "relative_end": **********.526555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.527333, "relative_start": 1.6811010837554932, "end": **********.527333, "relative_end": **********.527333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.52811, "relative_start": 1.6818780899047852, "end": **********.52811, "relative_end": **********.52811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.528857, "relative_start": 1.6826250553131104, "end": **********.528857, "relative_end": **********.528857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.529398, "relative_start": 1.6831660270690918, "end": **********.529398, "relative_end": **********.529398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.530292, "relative_start": 1.6840600967407227, "end": **********.530292, "relative_end": **********.530292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.530815, "relative_start": 1.6845829486846924, "end": **********.530815, "relative_end": **********.530815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.531212, "relative_start": 1.6849801540374756, "end": **********.531212, "relative_end": **********.531212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.531578, "relative_start": 1.6853461265563965, "end": **********.531578, "relative_end": **********.531578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.531976, "relative_start": 1.685744047164917, "end": **********.531976, "relative_end": **********.531976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.532349, "relative_start": 1.686117172241211, "end": **********.532349, "relative_end": **********.532349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.532713, "relative_start": 1.68648099899292, "end": **********.532713, "relative_end": **********.532713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.533073, "relative_start": 1.6868410110473633, "end": **********.533073, "relative_end": **********.533073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.533433, "relative_start": 1.6872010231018066, "end": **********.533433, "relative_end": **********.533433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.533829, "relative_start": 1.6875970363616943, "end": **********.533829, "relative_end": **********.533829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.534226, "relative_start": 1.6879940032958984, "end": **********.534226, "relative_end": **********.534226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.534589, "relative_start": 1.6883571147918701, "end": **********.534589, "relative_end": **********.534589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.53501, "relative_start": 1.6887781620025635, "end": **********.53501, "relative_end": **********.53501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.53535, "relative_start": 1.6891181468963623, "end": **********.53535, "relative_end": **********.53535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.535762, "relative_start": 1.6895301342010498, "end": **********.535762, "relative_end": **********.535762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.plus", "start": **********.600782, "relative_start": 1.7545499801635742, "end": **********.600782, "relative_end": **********.600782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.601241, "relative_start": 1.7550091743469238, "end": **********.601241, "relative_end": **********.601241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.602436, "relative_start": 1.7562041282653809, "end": **********.602436, "relative_end": **********.602436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.603001, "relative_start": 1.7567691802978516, "end": **********.603001, "relative_end": **********.603001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.trigger", "start": **********.603496, "relative_start": 1.7572641372680664, "end": **********.603496, "relative_end": **********.603496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.603912, "relative_start": 1.7576801776885986, "end": **********.603912, "relative_end": **********.603912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.category-form", "start": **********.606529, "relative_start": 1.7602970600128174, "end": **********.606529, "relative_end": **********.606529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.608326, "relative_start": 1.7620940208435059, "end": **********.608326, "relative_end": **********.608326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.609123, "relative_start": 1.7628910541534424, "end": **********.609123, "relative_end": **********.609123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.610311, "relative_start": 1.7640790939331055, "end": **********.610311, "relative_end": **********.610311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.616812, "relative_start": 1.7705800533294678, "end": **********.616812, "relative_end": **********.616812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.618046, "relative_start": 1.7718141078948975, "end": **********.618046, "relative_end": **********.618046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.618854, "relative_start": 1.7726221084594727, "end": **********.618854, "relative_end": **********.618854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.619594, "relative_start": 1.773362159729004, "end": **********.619594, "relative_end": **********.619594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.625753, "relative_start": 1.7795209884643555, "end": **********.625753, "relative_end": **********.625753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.628854, "relative_start": 1.7826220989227295, "end": **********.628854, "relative_end": **********.628854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.631249, "relative_start": 1.7850170135498047, "end": **********.631249, "relative_end": **********.631249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.632972, "relative_start": 1.7867400646209717, "end": **********.632972, "relative_end": **********.632972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.633961, "relative_start": 1.787729024887085, "end": **********.633961, "relative_end": **********.633961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.635389, "relative_start": 1.7891571521759033, "end": **********.635389, "relative_end": **********.635389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.636627, "relative_start": 1.7903950214385986, "end": **********.636627, "relative_end": **********.636627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.638003, "relative_start": 1.7917711734771729, "end": **********.638003, "relative_end": **********.638003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.63879, "relative_start": 1.792557954788208, "end": **********.63879, "relative_end": **********.63879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.639613, "relative_start": 1.7933809757232666, "end": **********.639613, "relative_end": **********.639613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.640492, "relative_start": 1.794260025024414, "end": **********.640492, "relative_end": **********.640492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.641154, "relative_start": 1.794922113418579, "end": **********.641154, "relative_end": **********.641154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.642453, "relative_start": 1.7962210178375244, "end": **********.642453, "relative_end": **********.642453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.643518, "relative_start": 1.797286033630371, "end": **********.643518, "relative_end": **********.643518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.645364, "relative_start": 1.7991321086883545, "end": **********.645364, "relative_end": **********.645364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.646149, "relative_start": 1.7999169826507568, "end": **********.646149, "relative_end": **********.646149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.647182, "relative_start": 1.800950050354004, "end": **********.647182, "relative_end": **********.647182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.647958, "relative_start": 1.8017261028289795, "end": **********.647958, "relative_end": **********.647958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.648626, "relative_start": 1.802394151687622, "end": **********.648626, "relative_end": **********.648626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.649436, "relative_start": 1.80320405960083, "end": **********.649436, "relative_end": **********.649436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.650224, "relative_start": 1.8039920330047607, "end": **********.650224, "relative_end": **********.650224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.651461, "relative_start": 1.8052289485931396, "end": **********.651461, "relative_end": **********.651461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.65219, "relative_start": 1.8059580326080322, "end": **********.65219, "relative_end": **********.65219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.65302, "relative_start": 1.8067879676818848, "end": **********.65302, "relative_end": **********.65302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.653615, "relative_start": 1.8073830604553223, "end": **********.653615, "relative_end": **********.653615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.654038, "relative_start": 1.8078060150146484, "end": **********.654038, "relative_end": **********.654038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.65476, "relative_start": 1.808527946472168, "end": **********.65476, "relative_end": **********.65476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.655485, "relative_start": 1.8092529773712158, "end": **********.655485, "relative_end": **********.655485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.656801, "relative_start": 1.8105690479278564, "end": **********.656801, "relative_end": **********.656801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.657813, "relative_start": 1.8115811347961426, "end": **********.657813, "relative_end": **********.657813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.658963, "relative_start": 1.8127310276031494, "end": **********.658963, "relative_end": **********.658963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.659962, "relative_start": 1.813730001449585, "end": **********.659962, "relative_end": **********.659962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.660564, "relative_start": 1.8143320083618164, "end": **********.660564, "relative_end": **********.660564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.661429, "relative_start": 1.8151969909667969, "end": **********.661429, "relative_end": **********.661429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.66204, "relative_start": 1.8158080577850342, "end": **********.66204, "relative_end": **********.66204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.662932, "relative_start": 1.8166999816894531, "end": **********.662932, "relative_end": **********.662932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.663602, "relative_start": 1.8173701763153076, "end": **********.663602, "relative_end": **********.663602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.664282, "relative_start": 1.8180501461029053, "end": **********.664282, "relative_end": **********.664282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.664801, "relative_start": 1.8185689449310303, "end": **********.664801, "relative_end": **********.664801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.665176, "relative_start": 1.818943977355957, "end": **********.665176, "relative_end": **********.665176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.665702, "relative_start": 1.8194701671600342, "end": **********.665702, "relative_end": **********.665702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.666185, "relative_start": 1.8199529647827148, "end": **********.666185, "relative_end": **********.666185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.66702, "relative_start": 1.8207881450653076, "end": **********.66702, "relative_end": **********.66702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.667659, "relative_start": 1.8214271068572998, "end": **********.667659, "relative_end": **********.667659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.668293, "relative_start": 1.8220610618591309, "end": **********.668293, "relative_end": **********.668293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.668809, "relative_start": 1.8225769996643066, "end": **********.668809, "relative_end": **********.668809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.669177, "relative_start": 1.8229451179504395, "end": **********.669177, "relative_end": **********.669177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.669661, "relative_start": 1.8234291076660156, "end": **********.669661, "relative_end": **********.669661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.670115, "relative_start": 1.823883056640625, "end": **********.670115, "relative_end": **********.670115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.670942, "relative_start": 1.8247101306915283, "end": **********.670942, "relative_end": **********.670942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.671558, "relative_start": 1.8253259658813477, "end": **********.671558, "relative_end": **********.671558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.672205, "relative_start": 1.8259730339050293, "end": **********.672205, "relative_end": **********.672205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.673061, "relative_start": 1.826828956604004, "end": **********.673061, "relative_end": **********.673061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.673494, "relative_start": 1.8272621631622314, "end": **********.673494, "relative_end": **********.673494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.67401, "relative_start": 1.8277781009674072, "end": **********.67401, "relative_end": **********.67401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.674465, "relative_start": 1.828233003616333, "end": **********.674465, "relative_end": **********.674465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.675262, "relative_start": 1.8290300369262695, "end": **********.675262, "relative_end": **********.675262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.675805, "relative_start": 1.829573154449463, "end": **********.675805, "relative_end": **********.675805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.676698, "relative_start": 1.8304660320281982, "end": **********.676698, "relative_end": **********.676698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.67749, "relative_start": 1.8312580585479736, "end": **********.67749, "relative_end": **********.67749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.678242, "relative_start": 1.83201003074646, "end": **********.678242, "relative_end": **********.678242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.678788, "relative_start": 1.8325560092926025, "end": **********.678788, "relative_end": **********.678788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.679279, "relative_start": 1.8330471515655518, "end": **********.679279, "relative_end": **********.679279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.68011, "relative_start": 1.8338780403137207, "end": **********.68011, "relative_end": **********.68011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.680726, "relative_start": 1.8344941139221191, "end": **********.680726, "relative_end": **********.680726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.68134, "relative_start": 1.8351080417633057, "end": **********.68134, "relative_end": **********.68134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.681811, "relative_start": 1.8355791568756104, "end": **********.681811, "relative_end": **********.681811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.682212, "relative_start": 1.8359801769256592, "end": **********.682212, "relative_end": **********.682212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.682705, "relative_start": 1.836472988128662, "end": **********.682705, "relative_end": **********.682705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.683178, "relative_start": 1.8369460105895996, "end": **********.683178, "relative_end": **********.683178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.index", "start": **********.684089, "relative_start": 1.8378570079803467, "end": **********.684089, "relative_end": **********.684089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.variants.listbox", "start": **********.685436, "relative_start": 1.8392040729522705, "end": **********.685436, "relative_end": **********.685436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.button", "start": **********.686835, "relative_start": 1.8406031131744385, "end": **********.686835, "relative_end": **********.686835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.selected", "start": **********.691608, "relative_start": 1.8453760147094727, "end": **********.691608, "relative_end": **********.691608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.6969, "relative_start": 1.850667953491211, "end": **********.6969, "relative_end": **********.6969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.698188, "relative_start": 1.8519561290740967, "end": **********.698188, "relative_end": **********.698188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.700395, "relative_start": 1.8541631698608398, "end": **********.700395, "relative_end": **********.700395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.701248, "relative_start": 1.8550159931182861, "end": **********.701248, "relative_end": **********.701248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-down", "start": **********.702724, "relative_start": 1.856492042541504, "end": **********.702724, "relative_end": **********.702724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.options", "start": **********.70427, "relative_start": 1.8580379486083984, "end": **********.70427, "relative_end": **********.70427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.705694, "relative_start": 1.859462022781372, "end": **********.705694, "relative_end": **********.705694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.706884, "relative_start": 1.860651969909668, "end": **********.706884, "relative_end": **********.706884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.707675, "relative_start": 1.861443042755127, "end": **********.707675, "relative_end": **********.707675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.708875, "relative_start": 1.8626430034637451, "end": **********.708875, "relative_end": **********.708875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.710019, "relative_start": 1.8637871742248535, "end": **********.710019, "relative_end": **********.710019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.712072, "relative_start": 1.865839958190918, "end": **********.712072, "relative_end": **********.712072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.712771, "relative_start": 1.8665390014648438, "end": **********.712771, "relative_end": **********.712771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.713692, "relative_start": 1.867460012435913, "end": **********.713692, "relative_end": **********.713692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.714567, "relative_start": 1.8683350086212158, "end": **********.714567, "relative_end": **********.714567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.717734, "relative_start": 1.871502161026001, "end": **********.717734, "relative_end": **********.717734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.719469, "relative_start": 1.873237133026123, "end": **********.719469, "relative_end": **********.719469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.720405, "relative_start": 1.8741731643676758, "end": **********.720405, "relative_end": **********.720405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.721031, "relative_start": 1.8747990131378174, "end": **********.721031, "relative_end": **********.721031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.721588, "relative_start": 1.8753559589385986, "end": **********.721588, "relative_end": **********.721588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": **********.722251, "relative_start": 1.87601900100708, "end": **********.722251, "relative_end": **********.722251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.723978, "relative_start": 1.8777461051940918, "end": **********.723978, "relative_end": **********.723978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.729186, "relative_start": 1.8829541206359863, "end": **********.729186, "relative_end": **********.729186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.72979, "relative_start": 1.8835580348968506, "end": **********.72979, "relative_end": **********.72979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.730385, "relative_start": 1.884153127670288, "end": **********.730385, "relative_end": **********.730385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.730984, "relative_start": 1.8847520351409912, "end": **********.730984, "relative_end": **********.730984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.731538, "relative_start": 1.8853061199188232, "end": **********.731538, "relative_end": **********.731538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.736485, "relative_start": 1.8902530670166016, "end": **********.736485, "relative_end": **********.736485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.73906, "relative_start": 1.8928279876708984, "end": **********.73906, "relative_end": **********.73906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.739756, "relative_start": 1.893524169921875, "end": **********.739756, "relative_end": **********.739756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.742404, "relative_start": 1.896172046661377, "end": **********.742404, "relative_end": **********.742404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.744253, "relative_start": 1.8980209827423096, "end": **********.744253, "relative_end": **********.744253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.745029, "relative_start": 1.8987970352172852, "end": **********.745029, "relative_end": **********.745029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.745569, "relative_start": 1.8993370532989502, "end": **********.745569, "relative_end": **********.745569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.74678, "relative_start": 1.900547981262207, "end": **********.74678, "relative_end": **********.74678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.747328, "relative_start": 1.9010961055755615, "end": **********.747328, "relative_end": **********.747328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.747868, "relative_start": 1.9016361236572266, "end": **********.747868, "relative_end": **********.747868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.748175, "relative_start": 1.9019429683685303, "end": **********.748175, "relative_end": **********.748175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.749364, "relative_start": 1.9031319618225098, "end": **********.749364, "relative_end": **********.749364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.749983, "relative_start": 1.9037511348724365, "end": **********.749983, "relative_end": **********.749983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.75071, "relative_start": 1.9044780731201172, "end": **********.75071, "relative_end": **********.75071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.751322, "relative_start": 1.905090093612671, "end": **********.751322, "relative_end": **********.751322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": **********.751817, "relative_start": 1.9055850505828857, "end": **********.751817, "relative_end": **********.751817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.752553, "relative_start": 1.9063210487365723, "end": **********.752553, "relative_end": **********.752553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.753643, "relative_start": 1.9074110984802246, "end": **********.753643, "relative_end": **********.753643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.754072, "relative_start": 1.9078400135040283, "end": **********.754072, "relative_end": **********.754072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.75446, "relative_start": 1.9082281589508057, "end": **********.75446, "relative_end": **********.75446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.754969, "relative_start": 1.9087369441986084, "end": **********.754969, "relative_end": **********.754969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.755407, "relative_start": 1.909175157546997, "end": **********.755407, "relative_end": **********.755407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.765258, "relative_start": 1.9190261363983154, "end": **********.765258, "relative_end": **********.765258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.766818, "relative_start": 1.920586109161377, "end": **********.766818, "relative_end": **********.766818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.767758, "relative_start": 1.9215259552001953, "end": **********.767758, "relative_end": **********.767758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.77476, "relative_start": 1.928528070449829, "end": **********.77476, "relative_end": **********.77476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app", "start": **********.777346, "relative_start": 1.9311139583587646, "end": **********.777346, "relative_end": **********.777346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::main", "start": **********.77897, "relative_start": 1.9327380657196045, "end": **********.77897, "relative_end": **********.77897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app.sidebar", "start": **********.77985, "relative_start": 1.9336180686950684, "end": **********.77985, "relative_end": **********.77985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.head", "start": **********.786943, "relative_start": 1.9407110214233398, "end": **********.786943, "relative_end": **********.786943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.toggle", "start": **********.789787, "relative_start": 1.9435551166534424, "end": **********.789787, "relative_end": **********.789787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.791059, "relative_start": 1.9448270797729492, "end": **********.791059, "relative_end": **********.791059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.796754, "relative_start": 1.9505219459533691, "end": **********.796754, "relative_end": **********.796754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.797409, "relative_start": 1.9511771202087402, "end": **********.797409, "relative_end": **********.797409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.797935, "relative_start": 1.9517030715942383, "end": **********.797935, "relative_end": **********.797935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.799415, "relative_start": 1.9531831741333008, "end": **********.799415, "relative_end": **********.799415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.app-logo", "start": **********.80315, "relative_start": 1.9569180011749268, "end": **********.80315, "relative_end": **********.80315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.804871, "relative_start": 1.958639144897461, "end": **********.804871, "relative_end": **********.804871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.806186, "relative_start": 1.959954023361206, "end": **********.806186, "relative_end": **********.806186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.layout-dashboard", "start": **********.807075, "relative_start": 1.9608430862426758, "end": **********.807075, "relative_end": **********.807075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.807662, "relative_start": 1.9614300727844238, "end": **********.807662, "relative_end": **********.807662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.808479, "relative_start": 1.9622471332550049, "end": **********.808479, "relative_end": **********.808479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.809338, "relative_start": 1.9631061553955078, "end": **********.809338, "relative_end": **********.809338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.user", "start": **********.810667, "relative_start": 1.96443510055542, "end": **********.810667, "relative_end": **********.810667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.811871, "relative_start": 1.9656391143798828, "end": **********.811871, "relative_end": **********.811871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.813159, "relative_start": 1.9669270515441895, "end": **********.813159, "relative_end": **********.813159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.814336, "relative_start": 1.9681041240692139, "end": **********.814336, "relative_end": **********.814336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.currency-dollar", "start": **********.815301, "relative_start": 1.969069004058838, "end": **********.815301, "relative_end": **********.815301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.81595, "relative_start": 1.9697179794311523, "end": **********.81595, "relative_end": **********.81595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.816774, "relative_start": 1.9705419540405273, "end": **********.816774, "relative_end": **********.816774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.817663, "relative_start": 1.971431016921997, "end": **********.817663, "relative_end": **********.817663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.scroll-text", "start": **********.818388, "relative_start": 1.972156047821045, "end": **********.818388, "relative_end": **********.818388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.819034, "relative_start": 1.9728021621704102, "end": **********.819034, "relative_end": **********.819034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.819852, "relative_start": 1.9736201763153076, "end": **********.819852, "relative_end": **********.819852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.820791, "relative_start": 1.9745590686798096, "end": **********.820791, "relative_end": **********.820791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.queue-list", "start": **********.821692, "relative_start": 1.9754600524902344, "end": **********.821692, "relative_end": **********.821692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.822335, "relative_start": 1.9761030673980713, "end": **********.822335, "relative_end": **********.822335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.823157, "relative_start": 1.9769251346588135, "end": **********.823157, "relative_end": **********.823157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.824126, "relative_start": 1.9778940677642822, "end": **********.824126, "relative_end": **********.824126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.tags", "start": **********.824799, "relative_start": 1.978567123413086, "end": **********.824799, "relative_end": **********.824799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.8254, "relative_start": 1.979168176651001, "end": **********.8254, "relative_end": **********.8254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.group", "start": **********.826236, "relative_start": 1.980004072189331, "end": **********.826236, "relative_end": **********.826236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.index", "start": **********.82795, "relative_start": 1.9817180633544922, "end": **********.82795, "relative_end": **********.82795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.828659, "relative_start": 1.9824271202087402, "end": **********.828659, "relative_end": **********.828659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.829192, "relative_start": 1.9829599857330322, "end": **********.829192, "relative_end": **********.829192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.830153, "relative_start": 1.9839210510253906, "end": **********.830153, "relative_end": **********.830153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.folder-git-2", "start": **********.830873, "relative_start": 1.9846410751342773, "end": **********.830873, "relative_end": **********.830873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.831518, "relative_start": 1.985285997390747, "end": **********.831518, "relative_end": **********.831518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.index", "start": **********.832122, "relative_start": 1.9858901500701904, "end": **********.832122, "relative_end": **********.832122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::profile", "start": **********.833375, "relative_start": 1.9871430397033691, "end": **********.833375, "relative_end": **********.833375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::avatar.index", "start": **********.834633, "relative_start": 1.988401174545288, "end": **********.834633, "relative_end": **********.834633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.835912, "relative_start": 1.9896800518035889, "end": **********.835912, "relative_end": **********.835912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.836467, "relative_start": 1.9902350902557373, "end": **********.836467, "relative_end": **********.836467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.83703, "relative_start": 1.990797996520996, "end": **********.83703, "relative_end": **********.83703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevrons-up-down", "start": **********.837733, "relative_start": 1.9915010929107666, "end": **********.837733, "relative_end": **********.837733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.83914, "relative_start": 1.992908000946045, "end": **********.83914, "relative_end": **********.83914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.839834, "relative_start": 1.9936020374298096, "end": **********.839834, "relative_end": **********.839834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.840739, "relative_start": 1.994507074356079, "end": **********.840739, "relative_end": **********.840739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.841972, "relative_start": 1.9957401752471924, "end": **********.841972, "relative_end": **********.841972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.843965, "relative_start": 1.9977331161499023, "end": **********.843965, "relative_end": **********.843965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.845127, "relative_start": 1.9988951683044434, "end": **********.845127, "relative_end": **********.845127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.sun", "start": **********.846043, "relative_start": 1.9998111724853516, "end": **********.846043, "relative_end": **********.846043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.846659, "relative_start": 2.000427007675171, "end": **********.846659, "relative_end": **********.846659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.84713, "relative_start": 2.0008981227874756, "end": **********.84713, "relative_end": **********.84713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.847767, "relative_start": 2.001535177230835, "end": **********.847767, "relative_end": **********.847767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.moon", "start": **********.848538, "relative_start": 2.0023059844970703, "end": **********.848538, "relative_end": **********.848538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.849123, "relative_start": 2.0028910636901855, "end": **********.849123, "relative_end": **********.849123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.849618, "relative_start": 2.0033860206604004, "end": **********.849618, "relative_end": **********.849618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.850225, "relative_start": 2.003993034362793, "end": **********.850225, "relative_end": **********.850225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.computer-desktop", "start": **********.850993, "relative_start": 2.004760980606079, "end": **********.850993, "relative_end": **********.850993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.index", "start": **********.8517, "relative_start": 2.0054681301116943, "end": **********.8517, "relative_end": **********.8517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.variants.segmented", "start": **********.852609, "relative_start": 2.0063769817352295, "end": **********.852609, "relative_end": **********.852609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.853217, "relative_start": 2.0069849491119385, "end": **********.853217, "relative_end": **********.853217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.853936, "relative_start": 2.007704019546509, "end": **********.853936, "relative_end": **********.853936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.854311, "relative_start": 2.0080790519714355, "end": **********.854311, "relative_end": **********.854311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.855123, "relative_start": 2.0088911056518555, "end": **********.855123, "relative_end": **********.855123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.856025, "relative_start": 2.0097930431365967, "end": **********.856025, "relative_end": **********.856025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.cog", "start": **********.856819, "relative_start": 2.010586977005005, "end": **********.856819, "relative_end": **********.856819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.8574, "relative_start": 2.0111680030822754, "end": **********.8574, "relative_end": **********.8574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.857923, "relative_start": 2.011691093444824, "end": **********.857923, "relative_end": **********.857923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.85821, "relative_start": 2.0119781494140625, "end": **********.85821, "relative_end": **********.85821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.858865, "relative_start": 2.0126330852508545, "end": **********.858865, "relative_end": **********.858865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.85982, "relative_start": 2.0135879516601562, "end": **********.85982, "relative_end": **********.85982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.861043, "relative_start": 2.0148110389709473, "end": **********.861043, "relative_end": **********.861043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.arrow-right-start-on-rectangle", "start": **********.861964, "relative_start": 2.0157320499420166, "end": **********.861964, "relative_end": **********.861964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.862576, "relative_start": 2.0163440704345703, "end": **********.862576, "relative_end": **********.862576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.index", "start": **********.863159, "relative_start": 2.0169270038604736, "end": **********.863159, "relative_end": **********.863159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown", "start": **********.863665, "relative_start": 2.0174331665039062, "end": **********.863665, "relative_end": **********.863665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.index", "start": **********.864243, "relative_start": 2.0180110931396484, "end": **********.864243, "relative_end": **********.864243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.backdrop", "start": **********.865073, "relative_start": 2.018841028213501, "end": **********.865073, "relative_end": **********.865073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.toggle", "start": **********.865811, "relative_start": 2.0195791721343994, "end": **********.865811, "relative_end": **********.865811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.866299, "relative_start": 2.020066976547241, "end": **********.866299, "relative_end": **********.866299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.867469, "relative_start": 2.0212371349334717, "end": **********.867469, "relative_end": **********.867469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.panel-left", "start": **********.868105, "relative_start": 2.0218729972839355, "end": **********.868105, "relative_end": **********.868105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.868655, "relative_start": 2.022423028945923, "end": **********.868655, "relative_end": **********.868655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.869162, "relative_start": 2.022930145263672, "end": **********.869162, "relative_end": **********.869162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.869656, "relative_start": 2.0234241485595703, "end": **********.869656, "relative_end": **********.869656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::profile", "start": **********.87009, "relative_start": 2.023858070373535, "end": **********.87009, "relative_end": **********.87009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::avatar.index", "start": **********.870776, "relative_start": 2.0245440006256104, "end": **********.870776, "relative_end": **********.870776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.871817, "relative_start": 2.025585174560547, "end": **********.871817, "relative_end": **********.871817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.872328, "relative_start": 2.0260961055755615, "end": **********.872328, "relative_end": **********.872328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.87284, "relative_start": 2.0266079902648926, "end": **********.87284, "relative_end": **********.87284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevrons-up-down", "start": **********.873324, "relative_start": 2.0270919799804688, "end": **********.873324, "relative_end": **********.873324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.873866, "relative_start": 2.0276341438293457, "end": **********.873866, "relative_end": **********.873866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.874167, "relative_start": 2.027935028076172, "end": **********.874167, "relative_end": **********.874167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.874824, "relative_start": 2.028592109680176, "end": **********.874824, "relative_end": **********.874824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.875438, "relative_start": 2.0292060375213623, "end": **********.875438, "relative_end": **********.875438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.87597, "relative_start": 2.029737949371338, "end": **********.87597, "relative_end": **********.87597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.877035, "relative_start": 2.0308029651641846, "end": **********.877035, "relative_end": **********.877035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.sun", "start": **********.877943, "relative_start": 2.0317111015319824, "end": **********.877943, "relative_end": **********.877943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.878485, "relative_start": 2.0322530269622803, "end": **********.878485, "relative_end": **********.878485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.879011, "relative_start": 2.0327789783477783, "end": **********.879011, "relative_end": **********.879011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.879665, "relative_start": 2.033432960510254, "end": **********.879665, "relative_end": **********.879665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.moon", "start": **********.880116, "relative_start": 2.033884048461914, "end": **********.880116, "relative_end": **********.880116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.880557, "relative_start": 2.034325122833252, "end": **********.880557, "relative_end": **********.880557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.881007, "relative_start": 2.0347750186920166, "end": **********.881007, "relative_end": **********.881007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.881613, "relative_start": 2.0353810787200928, "end": **********.881613, "relative_end": **********.881613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.computer-desktop", "start": **********.882075, "relative_start": 2.0358431339263916, "end": **********.882075, "relative_end": **********.882075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.index", "start": **********.882524, "relative_start": 2.03629207611084, "end": **********.882524, "relative_end": **********.882524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.variants.segmented", "start": **********.882956, "relative_start": 2.036724090576172, "end": **********.882956, "relative_end": **********.882956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.883458, "relative_start": 2.0372259616851807, "end": **********.883458, "relative_end": **********.883458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.884227, "relative_start": 2.0379951000213623, "end": **********.884227, "relative_end": **********.884227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.884581, "relative_start": 2.038349151611328, "end": **********.884581, "relative_end": **********.884581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.885036, "relative_start": 2.038804054260254, "end": **********.885036, "relative_end": **********.885036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.885789, "relative_start": 2.0395569801330566, "end": **********.885789, "relative_end": **********.885789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.cog", "start": **********.886256, "relative_start": 2.0400240421295166, "end": **********.886256, "relative_end": **********.886256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.886719, "relative_start": 2.040487051010132, "end": **********.886719, "relative_end": **********.886719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.88725, "relative_start": 2.041018009185791, "end": **********.88725, "relative_end": **********.88725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.887551, "relative_start": 2.0413191318511963, "end": **********.887551, "relative_end": **********.887551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.887902, "relative_start": 2.041670083999634, "end": **********.887902, "relative_end": **********.887902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.888468, "relative_start": 2.042236089706421, "end": **********.888468, "relative_end": **********.888468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.889239, "relative_start": 2.0430071353912354, "end": **********.889239, "relative_end": **********.889239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.arrow-right-start-on-rectangle", "start": **********.889705, "relative_start": 2.0434730052948, "end": **********.889705, "relative_end": **********.889705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.890154, "relative_start": 2.043921947479248, "end": **********.890154, "relative_end": **********.890154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.index", "start": **********.891006, "relative_start": 2.044774055480957, "end": **********.891006, "relative_end": **********.891006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown", "start": **********.891438, "relative_start": 2.045206069946289, "end": **********.891438, "relative_end": **********.891438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::header", "start": **********.891956, "relative_start": 2.0457241535186768, "end": **********.891956, "relative_end": **********.891956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::toast.index", "start": **********.893003, "relative_start": 2.0467710494995117, "end": **********.893003, "relative_end": **********.893003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.898833, "relative_start": 2.0526010990142822, "end": **********.899041, "relative_end": **********.899041, "duration": 0.0002079010009765625, "duration_str": "208μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 30301192, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 320, "nb_templates": 320, "templates": [{"name": "1x livewire.planned-spending", "param_count": null, "params": [], "start": **********.352508, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/planned-spending.blade.phplivewire.planned-spending", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fplanned-spending.blade.php&line=1", "ajax": false, "filename": "planned-spending.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.planned-spending"}, {"name": "11x ********************************::button.index", "param_count": null, "params": [], "start": **********.357713, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.php********************************::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 11, "name_original": "********************************::button.index"}, {"name": "36x ********************************::icon.index", "param_count": null, "params": [], "start": **********.363296, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.php********************************::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 36, "name_original": "********************************::icon.index"}, {"name": "2x ********************************::icon.plus", "param_count": null, "params": [], "start": **********.36484, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/plus.blade.php********************************::icon.plus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fplus.blade.php&line=1", "ajax": false, "filename": "plus.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.plus"}, {"name": "24x ********************************::button-or-link", "param_count": null, "params": [], "start": **********.366277, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.php********************************::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}, "render_count": 24, "name_original": "********************************::button-or-link"}, {"name": "13x ********************************::with-tooltip", "param_count": null, "params": [], "start": **********.367628, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.php********************************::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}, "render_count": 13, "name_original": "********************************::with-tooltip"}, {"name": "2x ********************************::modal.trigger", "param_count": null, "params": [], "start": **********.36875, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/trigger.blade.php********************************::modal.trigger", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Ftrigger.blade.php&line=1", "ajax": false, "filename": "trigger.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.trigger"}, {"name": "1x livewire.planned-spending-form", "param_count": null, "params": [], "start": **********.392813, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/planned-spending-form.blade.phplivewire.planned-spending-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fplanned-spending-form.blade.php&line=1", "ajax": false, "filename": "planned-spending-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.planned-spending-form"}, {"name": "3x ********************************::heading", "param_count": null, "params": [], "start": **********.395917, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.php********************************::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::heading"}, {"name": "5x ********************************::label", "param_count": null, "params": [], "start": **********.397812, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.php********************************::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::label"}, {"name": "3x ********************************::input.index", "param_count": null, "params": [], "start": **********.399458, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.php********************************::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::input.index"}, {"name": "16x ********************************::with-field", "param_count": null, "params": [], "start": **********.402897, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.php********************************::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}, "render_count": 16, "name_original": "********************************::with-field"}, {"name": "5x ********************************::error", "param_count": null, "params": [], "start": **********.404473, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.php********************************::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::error"}, {"name": "5x ********************************::field", "param_count": null, "params": [], "start": **********.405583, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.php********************************::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::field"}, {"name": "1x components.categories", "param_count": null, "params": [], "start": **********.407019, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/categories.blade.phpcomponents.categories", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcategories.blade.php&line=1", "ajax": false, "filename": "categories.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.categories"}, {"name": "30x components.option", "param_count": null, "params": [], "start": **********.521626, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/option.blade.phpcomponents.option", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Foption.blade.php&line=1", "ajax": false, "filename": "option.blade.php", "line": "?"}, "render_count": 30, "name_original": "components.option"}, {"name": "1x components.select", "param_count": null, "params": [], "start": **********.535712, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/select.blade.phpcomponents.select", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.select"}, {"name": "1x livewire.category-form", "param_count": null, "params": [], "start": **********.606474, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/category-form.blade.phplivewire.category-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fcategory-form.blade.php&line=1", "ajax": false, "filename": "category-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.category-form"}, {"name": "10x ********************************::select.option.index", "param_count": null, "params": [], "start": **********.625667, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/option/index.blade.php********************************::select.option.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foption%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::select.option.index"}, {"name": "10x ********************************::select.option.variants.custom", "param_count": null, "params": [], "start": **********.628758, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/option/variants/custom.blade.php********************************::select.option.variants.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foption%2Fvariants%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::select.option.variants.custom"}, {"name": "10x ********************************::select.indicator.index", "param_count": null, "params": [], "start": **********.631151, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/indicator/index.blade.php********************************::select.indicator.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findicator%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::select.indicator.index"}, {"name": "10x ********************************::select.indicator.variants.check", "param_count": null, "params": [], "start": **********.632871, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/indicator/variants/check.blade.php********************************::select.indicator.variants.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findicator%2Fvariants%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::select.indicator.variants.check"}, {"name": "10x ********************************::icon.check", "param_count": null, "params": [], "start": **********.635275, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/check.blade.php********************************::icon.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::icon.check"}, {"name": "1x ********************************::select.index", "param_count": null, "params": [], "start": **********.684033, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/index.blade.php********************************::select.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.index"}, {"name": "1x ********************************::select.variants.listbox", "param_count": null, "params": [], "start": **********.685367, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/variants/listbox.blade.php********************************::select.variants.listbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fvariants%2Flistbox.blade.php&line=1", "ajax": false, "filename": "listbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.variants.listbox"}, {"name": "1x ********************************::select.button", "param_count": null, "params": [], "start": **********.686774, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/button.blade.php********************************::select.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.button"}, {"name": "1x ********************************::select.selected", "param_count": null, "params": [], "start": **********.691548, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/selected.blade.php********************************::select.selected", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fselected.blade.php&line=1", "ajax": false, "filename": "selected.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.selected"}, {"name": "4x ********************************::icon.x-mark", "param_count": null, "params": [], "start": **********.696807, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/x-mark.blade.php********************************::icon.x-mark", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fx-mark.blade.php&line=1", "ajax": false, "filename": "x-mark.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::icon.x-mark"}, {"name": "1x ********************************::icon.chevron-down", "param_count": null, "params": [], "start": **********.702627, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-down.blade.php********************************::icon.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.chevron-down"}, {"name": "1x ********************************::select.options", "param_count": null, "params": [], "start": **********.704179, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/options.blade.php********************************::select.options", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foptions.blade.php&line=1", "ajax": false, "filename": "options.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.options"}, {"name": "3x ********************************::spacer", "param_count": null, "params": [], "start": **********.708793, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/spacer.blade.php********************************::spacer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fspacer.blade.php&line=1", "ajax": false, "filename": "spacer.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::spacer"}, {"name": "4x ********************************::modal.close", "param_count": null, "params": [], "start": **********.713598, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/close.blade.php********************************::modal.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::modal.close"}, {"name": "3x ********************************::icon.loading", "param_count": null, "params": [], "start": **********.719407, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.php********************************::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::icon.loading"}, {"name": "2x ********************************::modal.index", "param_count": null, "params": [], "start": **********.722162, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/index.blade.php********************************::modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.index"}, {"name": "1x components.card", "param_count": null, "params": [], "start": **********.765119, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.card"}, {"name": "1x ********************************::card.index", "param_count": null, "params": [], "start": **********.767701, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/card/index.blade.php********************************::card.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::card.index"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.7747, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x components.layouts.app", "param_count": null, "params": [], "start": **********.777252, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app.blade.phpcomponents.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app"}, {"name": "1x ********************************::main", "param_count": null, "params": [], "start": **********.778907, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/main.blade.php********************************::main", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmain.blade.php&line=1", "ajax": false, "filename": "main.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::main"}, {"name": "1x components.layouts.app.sidebar", "param_count": null, "params": [], "start": **********.779773, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app/sidebar.blade.phpcomponents.layouts.app.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app.sidebar"}, {"name": "1x partials.head", "param_count": null, "params": [], "start": **********.786881, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/partials/head.blade.phppartials.head", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fpartials%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.head"}, {"name": "2x ********************************::sidebar.toggle", "param_count": null, "params": [], "start": **********.789675, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/toggle.blade.php********************************::sidebar.toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Ftoggle.blade.php&line=1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::sidebar.toggle"}, {"name": "1x components.app-logo", "param_count": null, "params": [], "start": **********.803087, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/app-logo.blade.phpcomponents.app-logo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fapp-logo.blade.php&line=1", "ajax": false, "filename": "app-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.app-logo"}, {"name": "7x ********************************::navlist.item", "param_count": null, "params": [], "start": **********.804812, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/item.blade.php********************************::navlist.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 7, "name_original": "********************************::navlist.item"}, {"name": "1x ********************************::icon.layout-dashboard", "param_count": null, "params": [], "start": **********.806997, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/layout-dashboard.blade.php********************************::icon.layout-dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Flayout-dashboard.blade.php&line=1", "ajax": false, "filename": "layout-dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.layout-dashboard"}, {"name": "1x ********************************::icon.user", "param_count": null, "params": [], "start": **********.810602, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/user.blade.php********************************::icon.user", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.user"}, {"name": "1x ********************************::icon.currency-dollar", "param_count": null, "params": [], "start": **********.815245, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/currency-dollar.blade.php********************************::icon.currency-dollar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcurrency-dollar.blade.php&line=1", "ajax": false, "filename": "currency-dollar.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.currency-dollar"}, {"name": "1x ********************************::icon.scroll-text", "param_count": null, "params": [], "start": **********.818331, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/scroll-text.blade.php********************************::icon.scroll-text", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fscroll-text.blade.php&line=1", "ajax": false, "filename": "scroll-text.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.scroll-text"}, {"name": "1x ********************************::icon.queue-list", "param_count": null, "params": [], "start": **********.821634, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/queue-list.blade.php********************************::icon.queue-list", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fqueue-list.blade.php&line=1", "ajax": false, "filename": "queue-list.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.queue-list"}, {"name": "1x ********************************::icon.tags", "param_count": null, "params": [], "start": **********.824745, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/tags.blade.php********************************::icon.tags", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ftags.blade.php&line=1", "ajax": false, "filename": "tags.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.tags"}, {"name": "1x ********************************::navlist.group", "param_count": null, "params": [], "start": **********.826088, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/navlist/group.blade.php********************************::navlist.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::navlist.group"}, {"name": "2x ********************************::navlist.index", "param_count": null, "params": [], "start": **********.827891, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/index.blade.php********************************::navlist.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::navlist.index"}, {"name": "1x ********************************::icon.folder-git-2", "param_count": null, "params": [], "start": **********.830817, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/folder-git-2.blade.php********************************::icon.folder-git-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ffolder-git-2.blade.php&line=1", "ajax": false, "filename": "folder-git-2.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.folder-git-2"}, {"name": "2x ********************************::profile", "param_count": null, "params": [], "start": **********.833318, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/profile.blade.php********************************::profile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fprofile.blade.php&line=1", "ajax": false, "filename": "profile.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::profile"}, {"name": "2x ********************************::avatar.index", "param_count": null, "params": [], "start": **********.834562, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/avatar/index.blade.php********************************::avatar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Favatar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::avatar.index"}, {"name": "2x ********************************::icon.chevrons-up-down", "param_count": null, "params": [], "start": **********.837677, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/chevrons-up-down.blade.php********************************::icon.chevrons-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevrons-up-down.blade.php&line=1", "ajax": false, "filename": "chevrons-up-down.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.chevrons-up-down"}, {"name": "4x ********************************::menu.radio.group", "param_count": null, "params": [], "start": **********.839082, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/radio/group.blade.php********************************::menu.radio.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fradio%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::menu.radio.group"}, {"name": "6x ********************************::menu.separator", "param_count": null, "params": [], "start": **********.839779, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/separator.blade.php********************************::menu.separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::menu.separator"}, {"name": "6x ********************************::separator", "param_count": null, "params": [], "start": **********.840682, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/separator.blade.php********************************::separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::separator"}, {"name": "6x ********************************::radio.index", "param_count": null, "params": [], "start": **********.841911, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/index.blade.php********************************::radio.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::radio.index"}, {"name": "6x ********************************::radio.variants.segmented", "param_count": null, "params": [], "start": **********.84387, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/variants/segmented.blade.php********************************::radio.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::radio.variants.segmented"}, {"name": "2x ********************************::icon.sun", "param_count": null, "params": [], "start": **********.845977, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/sun.blade.php********************************::icon.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.sun"}, {"name": "2x ********************************::icon.moon", "param_count": null, "params": [], "start": **********.848488, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/moon.blade.php********************************::icon.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.moon"}, {"name": "2x ********************************::icon.computer-desktop", "param_count": null, "params": [], "start": **********.850925, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/computer-desktop.blade.php********************************::icon.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.computer-desktop"}, {"name": "2x ********************************::radio.group.index", "param_count": null, "params": [], "start": **********.851648, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/index.blade.php********************************::radio.group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::radio.group.index"}, {"name": "2x ********************************::radio.group.variants.segmented", "param_count": null, "params": [], "start": **********.852558, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/variants/segmented.blade.php********************************::radio.group.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::radio.group.variants.segmented"}, {"name": "4x ********************************::menu.item", "param_count": null, "params": [], "start": **********.855071, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/item.blade.php********************************::menu.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::menu.item"}, {"name": "2x ********************************::icon.cog", "param_count": null, "params": [], "start": **********.856767, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/cog.blade.php********************************::icon.cog", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcog.blade.php&line=1", "ajax": false, "filename": "cog.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.cog"}, {"name": "2x ********************************::icon.arrow-right-start-on-rectangle", "param_count": null, "params": [], "start": **********.861913, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/arrow-right-start-on-rectangle.blade.php********************************::icon.arrow-right-start-on-rectangle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Farrow-right-start-on-rectangle.blade.php&line=1", "ajax": false, "filename": "arrow-right-start-on-rectangle.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.arrow-right-start-on-rectangle"}, {"name": "2x ********************************::menu.index", "param_count": null, "params": [], "start": **********.863108, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/index.blade.php********************************::menu.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::menu.index"}, {"name": "2x ********************************::dropdown", "param_count": null, "params": [], "start": **********.863614, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/dropdown.blade.php********************************::dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::dropdown"}, {"name": "1x ********************************::sidebar.index", "param_count": null, "params": [], "start": **********.864192, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/index.blade.php********************************::sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::sidebar.index"}, {"name": "1x ********************************::sidebar.backdrop", "param_count": null, "params": [], "start": **********.865021, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/backdrop.blade.php********************************::sidebar.backdrop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Fbackdrop.blade.php&line=1", "ajax": false, "filename": "backdrop.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::sidebar.backdrop"}, {"name": "1x ********************************::icon.panel-left", "param_count": null, "params": [], "start": **********.868054, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/panel-left.blade.php********************************::icon.panel-left", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fpanel-left.blade.php&line=1", "ajax": false, "filename": "panel-left.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.panel-left"}, {"name": "1x ********************************::header", "param_count": null, "params": [], "start": **********.891902, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/header.blade.php********************************::header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::header"}, {"name": "1x ********************************::toast.index", "param_count": null, "params": [], "start": **********.892917, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/toast/index.blade.php********************************::toast.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ftoast%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::toast.index"}]}, "queries": {"count": 8, "nb_statements": 7, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0091, "accumulated_duration_str": "9.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.282062, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr' limit 1", "type": "query", "params": [], "bindings": ["kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.2910821, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 38.352}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.318479, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 38.352, "width_percent": 7.582}, {"sql": "select `planned_expenses`.*, `categories`.`user_id` as `laravel_through_key` from `planned_expenses` inner join `categories` on `categories`.`id` = `planned_expenses`.`category_id` where `categories`.`user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/PlannedSpending.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpending.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.338409, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "PlannedSpending.php:19", "source": {"index": 20, "namespace": null, "name": "app/Livewire/PlannedSpending.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpending.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpending.php&line=19", "ajax": false, "filename": "PlannedSpending.php", "line": "19"}, "connection": "daily", "explain": null, "start_percent": 45.934, "width_percent": 8.352}, {"sql": "select\ntransactions.category_id,\ncategories.parent_id,\nSUM(\nCASE\nWHEN transactions.type IN (\"credit\", \"deposit\") THEN -transactions.amount\nELSE transactions.amount\nEND\n) as total_spent\nfrom `transactions` inner join `categories` on `transactions`.`category_id` = `categories`.`id` where (`transactions`.`category_id` in (1) or `categories`.`parent_id` in (1)) and `transactions`.`date` between '2025-05-01' and '2025-05-31' group by `transactions`.`category_id`, `categories`.`parent_id`", "type": "query", "params": [], "bindings": [1, 1, "2025-05-01", "2025-05-31"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/PlannedSpending.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpending.php", "line": 47}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.343774, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "PlannedSpending.php:47", "source": {"index": 15, "namespace": null, "name": "app/Livewire/PlannedSpending.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpending.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpending.php&line=47", "ajax": false, "filename": "PlannedSpending.php", "line": "47"}, "connection": "daily", "explain": null, "start_percent": 54.286, "width_percent": 16.484}, {"sql": "select `id`, `name`, `parent_id` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null and `parent_id` is null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/PlannedSpendingForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpendingForm.php", "line": 57}, {"index": 17, "namespace": null, "name": "app/Livewire/PlannedSpendingForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpendingForm.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.375113, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "PlannedSpendingForm.php:57", "source": {"index": 16, "namespace": null, "name": "app/Livewire/PlannedSpendingForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpendingForm.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpendingForm.php&line=57", "ajax": false, "filename": "PlannedSpendingForm.php", "line": "57"}, "connection": "daily", "explain": null, "start_percent": 70.769, "width_percent": 10.11}, {"sql": "select * from `categories` where `categories`.`parent_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Livewire/PlannedSpendingForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpendingForm.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Livewire/PlannedSpendingForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpendingForm.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.380355, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "PlannedSpendingForm.php:57", "source": {"index": 21, "namespace": null, "name": "app/Livewire/PlannedSpendingForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PlannedSpendingForm.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpendingForm.php&line=57", "ajax": false, "filename": "PlannedSpendingForm.php", "line": "57"}, "connection": "daily", "explain": null, "start_percent": 80.879, "width_percent": 10.44}, {"sql": "select `id`, `name` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null and `parent_id` is null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/CategoryForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryForm.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/helpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\helpers.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportComputed/BaseComputed.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\BaseComputed.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportComputed/BaseComputed.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\BaseComputed.php", "line": 60}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/EventBus.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\EventBus.php", "line": 60}], "start": **********.6206079, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "CategoryForm.php:74", "source": {"index": 16, "namespace": null, "name": "app/Livewire/CategoryForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryForm.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryForm.php&line=74", "ajax": false, "filename": "CategoryForm.php", "line": "74"}, "connection": "daily", "explain": null, "start_percent": 91.319, "width_percent": 8.681}]}, "models": {"data": {"App\\Models\\Category": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PlannedExpense": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FPlannedExpense.php&line=1", "ajax": false, "filename": "PlannedExpense.php", "line": "?"}}}, "count": 42, "is_counter": true}, "livewire": {"data": {"planned-spending #9rWf1py6IRCE6IxabsGJ": "array:4 [\n  \"data\" => []\n  \"name\" => \"planned-spending\"\n  \"component\" => \"App\\Livewire\\PlannedSpending\"\n  \"id\" => \"9rWf1py6IRCE6IxabsGJ\"\n]", "planned-spending-form #lBl8Qzs1hztC0B3W0Wdc": "array:4 [\n  \"data\" => array:5 [\n    \"name\" => \"\"\n    \"category_id\" => null\n    \"monthly_amount\" => null\n    \"expense\" => null\n    \"categories\" => array:10 [\n      0 => array:4 [\n        \"id\" => 1\n        \"name\" => \"Auto & Transport 2\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 11\n            \"user_id\" => 1\n            \"name\" => \"Car Insurance\"\n            \"parent_id\" => 1\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 12\n            \"user_id\" => 1\n            \"name\" => \"Car Payment\"\n            \"parent_id\" => 1\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      1 => array:4 [\n        \"id\" => 2\n        \"name\" => \"Food\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 13\n            \"user_id\" => 1\n            \"name\" => \"Fast Food\"\n            \"parent_id\" => 2\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 14\n            \"user_id\" => 1\n            \"name\" => \"Restaurants\"\n            \"parent_id\" => 2\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      2 => array:4 [\n        \"id\" => 4\n        \"name\" => \"Health\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 17\n            \"user_id\" => 1\n            \"name\" => \"Doctor\"\n            \"parent_id\" => 4\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 18\n            \"user_id\" => 1\n            \"name\" => \"Pharmacy\"\n            \"parent_id\" => 4\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      3 => array:4 [\n        \"id\" => 3\n        \"name\" => \"Home\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 15\n            \"user_id\" => 1\n            \"name\" => \"Mortgage\"\n            \"parent_id\" => 3\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 16\n            \"user_id\" => 1\n            \"name\" => \"Rent\"\n            \"parent_id\" => 3\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      4 => array:4 [\n        \"id\" => 5\n        \"name\" => \"Personal Care\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 19\n            \"user_id\" => 1\n            \"name\" => \"Haircut\"\n            \"parent_id\" => 5\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 20\n            \"user_id\" => 1\n            \"name\" => \"Laundry\"\n            \"parent_id\" => 5\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      5 => array:4 [\n        \"id\" => 6\n        \"name\" => \"Personal Income\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 22\n            \"user_id\" => 1\n            \"name\" => \"Bonus\"\n            \"parent_id\" => 6\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 21\n            \"user_id\" => 1\n            \"name\" => \"Paycheck\"\n            \"parent_id\" => 6\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      6 => array:4 [\n        \"id\" => 7\n        \"name\" => \"Pets\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 23\n            \"user_id\" => 1\n            \"name\" => \"Pet Food\"\n            \"parent_id\" => 7\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 24\n            \"user_id\" => 1\n            \"name\" => \"Veterinary\"\n            \"parent_id\" => 7\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      7 => array:4 [\n        \"id\" => 8\n        \"name\" => \"Shopping\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 25\n            \"user_id\" => 1\n            \"name\" => \"Clothing\"\n            \"parent_id\" => 8\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 26\n            \"user_id\" => 1\n            \"name\" => \"Gifts\"\n            \"parent_id\" => 8\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      8 => array:4 [\n        \"id\" => 9\n        \"name\" => \"Travel\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 28\n            \"user_id\" => 1\n            \"name\" => \"Airfare\"\n            \"parent_id\" => 9\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 27\n            \"user_id\" => 1\n            \"name\" => \"Hotel\"\n            \"parent_id\" => 9\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      9 => array:4 [\n        \"id\" => 10\n        \"name\" => \"Utilities\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 30\n            \"user_id\" => 1\n            \"name\" => \"Electric\"\n            \"parent_id\" => 10\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 29\n            \"user_id\" => 1\n            \"name\" => \"Gas\"\n            \"parent_id\" => 10\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n    ]\n  ]\n  \"name\" => \"planned-spending-form\"\n  \"component\" => \"App\\Livewire\\PlannedSpendingForm\"\n  \"id\" => \"lBl8Qzs1hztC0B3W0Wdc\"\n]", "category-form #NcVigIlaopdnYAFKDKTl": "array:4 [\n  \"data\" => array:4 [\n    \"show_category_form\" => false\n    \"category\" => null\n    \"parent_id\" => null\n    \"name\" => \"\"\n  ]\n  \"name\" => \"category-form\"\n  \"component\" => \"App\\Livewire\\CategoryForm\"\n  \"id\" => \"NcVigIlaopdnYAFKDKTl\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/planned-spending", "action_name": "planned-spending", "controller_action": "App\\Livewire\\PlannedSpending", "uri": "GET planned-spending", "controller": "App\\Livewire\\PlannedSpending@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpending.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPlannedSpending.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/PlannedSpending.php:17-58</a>", "middleware": "web, auth", "duration": "2.06s", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-589045272 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-589045272\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1107717028 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1107717028\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-574574500 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire-navigate</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/categories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"751 characters\">PHPSESSID=aa0mh29tpvppc77i3l7peen7g0; XSRF-TOKEN=eyJpdiI6InozellRbjc5UzVQdmY3YjJpM1dCVEE9PSIsInZhbHVlIjoidHVUUlIrMklGSjJsc28vRXM1Lyt6K2JhbU9sM0ZOK2tud1JpYU8wSzRVTHI2dHNCQjlGbUFZZWlUVE5veHQySXExQkpZY1QyYzFCSGtCTGZkbzhLQndpbXlVSnlZSE5UM3R2OGRsV09ndXpYWDU1MFZsMlFSOTVhUGlkeXM4T0MiLCJtYWMiOiI0OGFlNjI1YWQ5ZmZlMjQzY2ZlMzdmZWZkNWMzMTc1M2NlODExYTlmMDk3ZWJmNjZkY2IyYTViNDAzYTY5N2YxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IklBM3BkaGhLeTJkWTJ2RVF0cUx1SkE9PSIsInZhbHVlIjoiaXpjRlZ5ZHVzUWVsQkRLTXZEbUVDMjFRcFI3Z0R0R0tKZ3hBdnlvQ1J1QllXSk1iNXhPMmxaUVEyOW1QT29uZm51NTkyeDhJYjVML29wSG1tWEFEUXI0K2crM1FobUZqbWc3eWRnUEVkSnVHWERwRVRZM1gzRnFIbHpINVBGTTEiLCJtYWMiOiJmYzk2NzU5MTU4NzQzMTMzNDA4YmI2OGU5ZWJiMTRlMjRiNmE5MTA5MjVjMGRkODMzYzExODE5YzI0OTgyMDAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574574500\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-81445336 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81445336\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-80184084 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 25 May 2025 10:09:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-80184084\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-976810326 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/categories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976810326\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/planned-spending", "action_name": "planned-spending", "controller_action": "App\\Livewire\\PlannedSpending"}, "badge": null}}