<?php

declare(strict_types=1);

namespace App\Livewire;

use Flux\Flux;
use Livewire\Component;
use Livewire\Attributes\On;
use Illuminate\Contracts\View\View;
use Spatie\Permission\Models\Permission;

class PermissionForm extends Component
{
    public ?array $permission = null;

    public string $name = '';

    public string $guard_name = 'web';

    protected function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                'unique:permissions,name' . ($this->permission ? ','. $this->permission['id'] : ''),
            ],
            'guard_name' => ['required', 'string', 'max:255'],
        ];
    }

    protected function messages(): array
    {
        return [
            'name.unique' => 'The permission name has already been taken.',
        ];
    }

    #[On('load-permission')]
    public function loadPermission(array $permission): void
    {
        $this->permission = $permission;
        $this->name = $this->permission['name'];
        $this->guard_name = $this->permission['guard_name'];
    }

    public function resetForm(): void
    {
        $this->reset(['permission', 'name', 'guard_name']);
        $this->resetErrorBag();
        $this->resetValidation();
    }

    public function submit(): void
    {
        $validated_data = $this->validate();

        if ($this->permission) {
            Permission::query()
                ->where('id', $this->permission['id'])
                ->update($validated_data);
        } else {
            Permission::create($validated_data);
        }

        $this->dispatch('permission-saved');

        if (!$this->permission) {
            $this->reset(['name', 'guard_name']);
        }

        Flux::toast(
            variant: 'success',
            text: 'Permission successfully ' . ($this->permission ? 'updated' : 'created'),
        );

        $this->resetForm();
        Flux::modal($this->getId())->close();
    }

    public function render(): View
    {
        return view('livewire.permission-form');
    }
}
