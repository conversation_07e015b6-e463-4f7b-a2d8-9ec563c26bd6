<?php
$classes = Flux::classes()
    ->add('rounded-[12px]')
    ->add('bg-zinc-50/80 dark:bg-zinc-900')
    ->add('border border-zinc-200 dark:border-zinc-700')
    ->add('-space-y-1')
    ->add('shadow-xs dark:shadow-lg')
?>

<div <?php echo e($attributes->class($classes)); ?> data-flux-card>
    <?php echo e($slot); ?>

</div>
<?php /**PATH C:\laragon\www\pure-finance\resources\views/flux/card/index.blade.php ENDPATH**/ ?>