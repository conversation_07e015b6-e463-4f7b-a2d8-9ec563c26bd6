@import 'tailwindcss';
@import '../../vendor/livewire/flux/dist/flux.css';

@source "../views";
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../vendor/livewire/flux-pro/stubs/**/*.blade.php';
@source '../../vendor/livewire/flux/stubs/**/*.blade.php';

@custom-variant dark (&:where(.dark, .dark *));

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --color-zinc-50: #fafafa;
    --color-zinc-100: #f5f5f5;
    --color-zinc-200: #e5e5e5;
    --color-zinc-300: #d4d4d4;
    --color-zinc-400: #a3a3a3;
    --color-zinc-500: #737373;
    --color-zinc-600: #525252;
    --color-zinc-700: #404040;
    --color-zinc-800: #262626;
    --color-zinc-900: #171717;
    --color-zinc-950: #0a0a0a;

    --color-accent: var(--color-emerald-600);
    --color-accent-content: var(--color-emerald-600);
    --color-accent-foreground: var(--color-white);
}

@layer theme {
    .dark {
        --color-accent: var(--color-emerald-600);
        --color-accent-content: var(--color-emerald-400);
        --color-accent-foreground: var(--color-white);
    }
}

@layer base {

    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

[data-flux-field] {
    @apply grid gap-2;
}

[data-flux-label] {
    @apply  !mb-0 !leading-tight;
}

[data-flux-select]>button,
[data-flux-input]>input,
[data-flux-date-picker]>button {
    @apply h-[38px];
}

input:focus[data-flux-control],
textarea:focus[data-flux-control],
select:focus[data-flux-control] {
    @apply outline-hidden ring-2 ring-accent ring-offset-2 ring-offset-accent-foreground;
}

[data-flux-pagination] {
    @apply  !p-3 border-zinc-200 rounded-b-[8px];
}

.dark [data-flux-pagination] {
    @apply  !p-3 border-zinc-700 rounded-b-[8px];
}

[data-flux-radio] {
    @apply -mt-[1px];
}

[data-flux-checkbox] {
    @apply -mt-[1px];
}

[x-cloak] {
    display: none !important;
}

/* \[:where(&)\]:size-4 {
    @apply size-4;
} */
