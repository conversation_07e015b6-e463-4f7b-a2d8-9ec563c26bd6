<div class="space-y-4 mx-auto max-w-6xl">
    <div class="flex items-center justify-between">
        <flux:heading size="xl">
            User Roles
        </flux:heading>
    </div>

    <x-card>
        <x-slot:content>
            <div class="p-3">
                <flux:input icon="magnifying-glass" placeholder="Search users..." wire:model.live.debounce.300ms='search' clearable />
            </div>

            @if ($users->count() > 0)
                <x-table :paginate="$users" class="border-t border-zinc-200 dark:border-white/20">
                    <x-table.columns class="[&>tr>th]:px-3! bg-zinc-50 dark:bg-zinc-800">
                        <x-table.column>
                            Name
                        </x-table.column>

                        <x-table.column>
                            Email
                        </x-table.column>

                        <x-table.column>
                            Roles
                        </x-table.column>

                        <x-table.column class="[&>div]:justify-end!">
                            Actions
                        </x-table.column>
                    </x-table.columns>

                    <x-table.rows class="dark:bg-zinc-900">
                        @foreach ($users as $user)
                            <x-table.row :key="$user->id" class="[&>td]:px-3! [&>td]:py-2!">
                                <x-table.cell class="whitespace-nowrap" variant="strong">
                                    {{ $user->name }}
                                </x-table.cell>

                                <x-table.cell class="whitespace-nowrap">
                                    {{ $user->email }}
                                </x-table.cell>

                                <x-table.cell class="whitespace-nowrap">
                                    <div class="flex flex-wrap gap-1">
                                        @forelse($user->roles as $role)
                                            <flux:badge color="blue" size="sm">
                                                {{ $role->name }}
                                            </flux:badge>
                                        @empty
                                            <span class="text-gray-500 text-sm">No roles</span>
                                        @endforelse
                                    </div>
                                </x-table.cell>

                                <x-table.cell class="[&>div]:justify-end!">
                                    <div class="flex items-center">
                                        <div>
                                            <flux:modal.trigger name="edit-user-roles-{{ $user->id }}">
                                                <flux:button icon="pencil-square" variant="ghost" size="sm"
                                                    class="text-indigo-500!" />
                                            </flux:modal.trigger>

                                            <livewire:user-role-form data-name="edit-user-roles-{{ $user->id }}" :$user :key="$user->id" />
                                        </div>
                                    </div>
                                </x-table.cell>
                            </x-table.row>
                        @endforeach
                    </x-table.rows>
                </x-table>
            @else
                <flux:heading class="italic! font-medium text-center pb-3">
                    No users found...
                </flux:heading>
            @endif
        </x-slot:content>
    </x-card>
</div>
