<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class CleanupPermissionsSeeder extends Seeder
{
    public function run(): void
    {
        // Clear all role-permission relationships
        DB::table('role_has_permissions')->truncate();
        
        // Clear all user-role relationships
        DB::table('model_has_roles')->truncate();
        
        // Clear all user-permission relationships
        DB::table('model_has_permissions')->truncate();
        
        // Delete all existing roles and permissions
        Role::truncate();
        Permission::truncate();
        
        // Recreate permissions
        $permissions = [
            // Transaction permissions
            'view transactions',
            'create transactions',
            'edit transactions',
            'delete transactions',
            
            // Account permissions
            'view accounts',
            'create accounts',
            'edit accounts',
            'delete accounts',
            
            // Category permissions
            'view categories',
            'create categories',
            'edit categories',
            'delete categories',
            
            // Tag permissions
            'view tags',
            'create tags',
            'edit tags',
            'delete tags',
            
            // User management permissions
            'view users',
            'create users',
            'edit users',
            'delete users',
            
            // Role management permissions
            'view roles',
            'create roles',
            'edit roles',
            'delete roles',
            'assign roles',
            
            // Permission management permissions
            'view permissions',
            'create permissions',
            'edit permissions',
            'delete permissions',
            
            // Settings permissions
            'view settings',
            'edit settings',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission, 'guard_name' => 'web']);
        }

        // Recreate roles with permissions
        $superAdmin = Role::create(['name' => 'super-admin', 'guard_name' => 'web']);
        $superAdmin->givePermissionTo(Permission::all());

        $admin = Role::create(['name' => 'admin', 'guard_name' => 'web']);
        $admin->givePermissionTo([
            'view transactions', 'create transactions', 'edit transactions', 'delete transactions',
            'view accounts', 'create accounts', 'edit accounts', 'delete accounts',
            'view categories', 'create categories', 'edit categories', 'delete categories',
            'view tags', 'create tags', 'edit tags', 'delete tags',
            'view users', 'edit users',
            'view roles', 'view permissions',
            'view settings', 'edit settings',
        ]);

        $manager = Role::create(['name' => 'manager', 'guard_name' => 'web']);
        $manager->givePermissionTo([
            'view transactions', 'create transactions', 'edit transactions',
            'view accounts', 'create accounts', 'edit accounts',
            'view categories', 'create categories', 'edit categories',
            'view tags', 'create tags', 'edit tags',
            'view users',
            'view settings',
        ]);

        $user = Role::create(['name' => 'user', 'guard_name' => 'web']);
        $user->givePermissionTo([
            'view transactions', 'create transactions', 'edit transactions',
            'view accounts', 'view categories', 'view tags',
            'view settings',
        ]);

        $viewer = Role::create(['name' => 'viewer', 'guard_name' => 'web']);
        $viewer->givePermissionTo([
            'view transactions', 'view accounts', 'view categories', 'view tags', 'view settings',
        ]);

        $this->command->info('Permissions and roles cleaned up and recreated successfully!');
    }
}
