{"__meta": {"id": "01JW5WFA0Q2GGXV21F3X67RAY9", "datetime": "2025-05-26 08:48:43", "utime": **********.544479, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.102917, "end": **********.544504, "duration": 1.441586971282959, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": **********.102917, "relative_start": 0, "end": **********.414114, "relative_end": **********.414114, "duration": 0.*****************, "duration_str": "311ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.414129, "relative_start": 0.*****************, "end": **********.544506, "relative_end": 2.1457672119140625e-06, "duration": 1.****************, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.461583, "relative_start": 0.*****************, "end": **********.468532, "relative_end": **********.468532, "duration": 0.006949186325073242, "duration_str": "6.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.permission-table", "start": **********.573492, "relative_start": 0.*****************, "end": **********.573492, "relative_end": **********.573492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.579242, "relative_start": 0.*****************, "end": **********.579242, "relative_end": **********.579242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.581124, "relative_start": 0.4782071113586426, "end": **********.581124, "relative_end": **********.581124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.58414, "relative_start": 0.48122310638427734, "end": **********.58414, "relative_end": **********.58414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "start": **********.585379, "relative_start": 0.48246192932128906, "end": **********.585379, "relative_end": **********.585379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.586073, "relative_start": 0.4831559658050537, "end": **********.586073, "relative_end": **********.586073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.586723, "relative_start": 0.48380613327026367, "end": **********.586723, "relative_end": **********.586723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.587346, "relative_start": 0.48442912101745605, "end": **********.587346, "relative_end": **********.587346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.588545, "relative_start": 0.4856281280517578, "end": **********.588545, "relative_end": **********.588545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.591275, "relative_start": 0.4883580207824707, "end": **********.591275, "relative_end": **********.591275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "start": **********.592065, "relative_start": 0.4891481399536133, "end": **********.592065, "relative_end": **********.592065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.592799, "relative_start": 0.4898819923400879, "end": **********.592799, "relative_end": **********.592799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.593842, "relative_start": 0.4909250736236572, "end": **********.593842, "relative_end": **********.593842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "start": **********.595449, "relative_start": 0.49253201484680176, "end": **********.595449, "relative_end": **********.595449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.597028, "relative_start": 0.4941110610961914, "end": **********.597028, "relative_end": **********.597028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.597918, "relative_start": 0.49500107765197754, "end": **********.597918, "relative_end": **********.597918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.600179, "relative_start": 0.49726200103759766, "end": **********.600179, "relative_end": **********.600179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.601071, "relative_start": 0.4981539249420166, "end": **********.601071, "relative_end": **********.601071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.60187, "relative_start": 0.49895310401916504, "end": **********.60187, "relative_end": **********.60187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": **********.603023, "relative_start": 0.5001060962677002, "end": **********.603023, "relative_end": **********.603023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": **********.603616, "relative_start": 0.5006990432739258, "end": **********.603616, "relative_end": **********.603616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": **********.6041, "relative_start": 0.501183032989502, "end": **********.6041, "relative_end": **********.6041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": **********.604562, "relative_start": 0.5016450881958008, "end": **********.604562, "relative_end": **********.604562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": **********.605014, "relative_start": 0.5020971298217773, "end": **********.605014, "relative_end": **********.605014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.columns", "start": **********.605464, "relative_start": 0.502547025680542, "end": **********.605464, "relative_end": **********.605464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.606261, "relative_start": 0.5033440589904785, "end": **********.606261, "relative_end": **********.606261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.60681, "relative_start": 0.5038931369781494, "end": **********.60681, "relative_end": **********.60681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.607386, "relative_start": 0.5044691562652588, "end": **********.607386, "relative_end": **********.607386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.608837, "relative_start": 0.5059199333190918, "end": **********.608837, "relative_end": **********.608837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.609576, "relative_start": 0.5066590309143066, "end": **********.609576, "relative_end": **********.609576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.610177, "relative_start": 0.5072600841522217, "end": **********.610177, "relative_end": **********.610177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.612023, "relative_start": 0.5091061592102051, "end": **********.612023, "relative_end": **********.612023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.613179, "relative_start": 0.5102620124816895, "end": **********.613179, "relative_end": **********.613179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.614099, "relative_start": 0.5111820697784424, "end": **********.614099, "relative_end": **********.614099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.616503, "relative_start": 0.5135860443115234, "end": **********.616503, "relative_end": **********.616503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.618115, "relative_start": 0.5151979923248291, "end": **********.618115, "relative_end": **********.618115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.619157, "relative_start": 0.516240119934082, "end": **********.619157, "relative_end": **********.619157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.619797, "relative_start": 0.5168800354003906, "end": **********.619797, "relative_end": **********.619797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.620334, "relative_start": 0.5174169540405273, "end": **********.620334, "relative_end": **********.620334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.623717, "relative_start": 0.5208001136779785, "end": **********.623717, "relative_end": **********.623717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.625793, "relative_start": 0.5228760242462158, "end": **********.625793, "relative_end": **********.625793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.626788, "relative_start": 0.5238709449768066, "end": **********.626788, "relative_end": **********.626788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.627743, "relative_start": 0.5248260498046875, "end": **********.627743, "relative_end": **********.627743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.631534, "relative_start": 0.5286171436309814, "end": **********.631534, "relative_end": **********.631534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.633929, "relative_start": 0.5310120582580566, "end": **********.633929, "relative_end": **********.633929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.635093, "relative_start": 0.5321760177612305, "end": **********.635093, "relative_end": **********.635093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.636102, "relative_start": 0.5331850051879883, "end": **********.636102, "relative_end": **********.636102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.636804, "relative_start": 0.5338871479034424, "end": **********.636804, "relative_end": **********.636804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.637376, "relative_start": 0.534459114074707, "end": **********.637376, "relative_end": **********.637376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.637826, "relative_start": 0.5349090099334717, "end": **********.637826, "relative_end": **********.637826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.638518, "relative_start": 0.5356011390686035, "end": **********.638518, "relative_end": **********.638518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.639071, "relative_start": 0.53615403175354, "end": **********.639071, "relative_end": **********.639071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.639778, "relative_start": 0.5368609428405762, "end": **********.639778, "relative_end": **********.639778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.640252, "relative_start": 0.5373351573944092, "end": **********.640252, "relative_end": **********.640252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.641794, "relative_start": 0.538877010345459, "end": **********.641794, "relative_end": **********.641794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.642396, "relative_start": 0.5394790172576904, "end": **********.642396, "relative_end": **********.642396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.642912, "relative_start": 0.5399949550628662, "end": **********.642912, "relative_end": **********.642912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.643334, "relative_start": 0.540416955947876, "end": **********.643334, "relative_end": **********.643334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.646635, "relative_start": 0.5437180995941162, "end": **********.646635, "relative_end": **********.646635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.647357, "relative_start": 0.5444400310516357, "end": **********.647357, "relative_end": **********.647357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.648383, "relative_start": 0.5454659461975098, "end": **********.648383, "relative_end": **********.648383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.649483, "relative_start": 0.5465660095214844, "end": **********.649483, "relative_end": **********.649483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.650493, "relative_start": 0.5475759506225586, "end": **********.650493, "relative_end": **********.650493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.651904, "relative_start": 0.5489871501922607, "end": **********.651904, "relative_end": **********.651904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.653305, "relative_start": 0.5503880977630615, "end": **********.653305, "relative_end": **********.653305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.653844, "relative_start": 0.5509271621704102, "end": **********.653844, "relative_end": **********.653844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.65435, "relative_start": 0.5514330863952637, "end": **********.65435, "relative_end": **********.65435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.654876, "relative_start": 0.5519590377807617, "end": **********.654876, "relative_end": **********.654876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.655374, "relative_start": 0.5524570941925049, "end": **********.655374, "relative_end": **********.655374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.660296, "relative_start": 0.5573790073394775, "end": **********.660296, "relative_end": **********.660296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.662774, "relative_start": 0.5598571300506592, "end": **********.662774, "relative_end": **********.662774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.663908, "relative_start": 0.5609910488128662, "end": **********.663908, "relative_end": **********.663908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.664927, "relative_start": 0.5620100498199463, "end": **********.664927, "relative_end": **********.664927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.666294, "relative_start": 0.5633771419525146, "end": **********.666294, "relative_end": **********.666294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.66698, "relative_start": 0.5640630722045898, "end": **********.66698, "relative_end": **********.66698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.667678, "relative_start": 0.5647611618041992, "end": **********.667678, "relative_end": **********.667678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.668581, "relative_start": 0.5656640529632568, "end": **********.668581, "relative_end": **********.668581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.669148, "relative_start": 0.5662310123443604, "end": **********.669148, "relative_end": **********.669148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.669503, "relative_start": 0.5665860176086426, "end": **********.669503, "relative_end": **********.669503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.670857, "relative_start": 0.5679399967193604, "end": **********.670857, "relative_end": **********.670857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.671367, "relative_start": 0.5684499740600586, "end": **********.671367, "relative_end": **********.671367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.67182, "relative_start": 0.5689029693603516, "end": **********.67182, "relative_end": **********.67182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.672079, "relative_start": 0.569162130355835, "end": **********.672079, "relative_end": **********.672079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.673352, "relative_start": 0.5704350471496582, "end": **********.673352, "relative_end": **********.673352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.673936, "relative_start": 0.5710189342498779, "end": **********.673936, "relative_end": **********.673936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.674414, "relative_start": 0.5714969635009766, "end": **********.674414, "relative_end": **********.674414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.674891, "relative_start": 0.5719740390777588, "end": **********.674891, "relative_end": **********.674891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.675347, "relative_start": 0.5724301338195801, "end": **********.675347, "relative_end": **********.675347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.676324, "relative_start": 0.5734069347381592, "end": **********.676324, "relative_end": **********.676324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.678434, "relative_start": 0.575516939163208, "end": **********.678434, "relative_end": **********.678434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.67939, "relative_start": 0.5764729976654053, "end": **********.67939, "relative_end": **********.67939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.680204, "relative_start": 0.577286958694458, "end": **********.680204, "relative_end": **********.680204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.681602, "relative_start": 0.5786850452423096, "end": **********.681602, "relative_end": **********.681602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.682562, "relative_start": 0.5796451568603516, "end": **********.682562, "relative_end": **********.682562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.683049, "relative_start": 0.580132007598877, "end": **********.683049, "relative_end": **********.683049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.683824, "relative_start": 0.5809071063995361, "end": **********.683824, "relative_end": **********.683824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.684534, "relative_start": 0.5816171169281006, "end": **********.684534, "relative_end": **********.684534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.685104, "relative_start": 0.5821869373321533, "end": **********.685104, "relative_end": **********.685104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.685615, "relative_start": 0.5826981067657471, "end": **********.685615, "relative_end": **********.685615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.686559, "relative_start": 0.5836420059204102, "end": **********.686559, "relative_end": **********.686559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.687069, "relative_start": 0.5841519832611084, "end": **********.687069, "relative_end": **********.687069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.687604, "relative_start": 0.5846869945526123, "end": **********.687604, "relative_end": **********.687604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.688546, "relative_start": 0.5856289863586426, "end": **********.688546, "relative_end": **********.688546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.689169, "relative_start": 0.586251974105835, "end": **********.689169, "relative_end": **********.689169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.689859, "relative_start": 0.5869419574737549, "end": **********.689859, "relative_end": **********.689859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.691185, "relative_start": 0.5882680416107178, "end": **********.691185, "relative_end": **********.691185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.691686, "relative_start": 0.5887689590454102, "end": **********.691686, "relative_end": **********.691686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.692417, "relative_start": 0.5894999504089355, "end": **********.692417, "relative_end": **********.692417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.693256, "relative_start": 0.590338945388794, "end": **********.693256, "relative_end": **********.693256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.693828, "relative_start": 0.5909111499786377, "end": **********.693828, "relative_end": **********.693828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.698076, "relative_start": 0.5951590538024902, "end": **********.698076, "relative_end": **********.698076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.699604, "relative_start": 0.5966870784759521, "end": **********.699604, "relative_end": **********.699604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.700268, "relative_start": 0.59735107421875, "end": **********.700268, "relative_end": **********.700268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.700738, "relative_start": 0.5978209972381592, "end": **********.700738, "relative_end": **********.700738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.702531, "relative_start": 0.599614143371582, "end": **********.702531, "relative_end": **********.702531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.703539, "relative_start": 0.6006219387054443, "end": **********.703539, "relative_end": **********.703539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.704073, "relative_start": 0.6011559963226318, "end": **********.704073, "relative_end": **********.704073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.704582, "relative_start": 0.6016650199890137, "end": **********.704582, "relative_end": **********.704582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.705073, "relative_start": 0.6021561622619629, "end": **********.705073, "relative_end": **********.705073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.705591, "relative_start": 0.6026740074157715, "end": **********.705591, "relative_end": **********.705591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.706057, "relative_start": 0.603140115737915, "end": **********.706057, "relative_end": **********.706057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.706566, "relative_start": 0.6036491394042969, "end": **********.706566, "relative_end": **********.706566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.707088, "relative_start": 0.6041710376739502, "end": **********.707088, "relative_end": **********.707088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.707556, "relative_start": 0.6046390533447266, "end": **********.707556, "relative_end": **********.707556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.707889, "relative_start": 0.6049721240997314, "end": **********.707889, "relative_end": **********.707889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.70969, "relative_start": 0.6067731380462646, "end": **********.70969, "relative_end": **********.70969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.710476, "relative_start": 0.6075589656829834, "end": **********.710476, "relative_end": **********.710476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.712113, "relative_start": 0.6091959476470947, "end": **********.712113, "relative_end": **********.712113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.714357, "relative_start": 0.6114399433135986, "end": **********.714357, "relative_end": **********.714357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.716796, "relative_start": 0.6138789653778076, "end": **********.716796, "relative_end": **********.716796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.717981, "relative_start": 0.6150641441345215, "end": **********.717981, "relative_end": **********.717981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.718567, "relative_start": 0.615649938583374, "end": **********.718567, "relative_end": **********.718567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.719176, "relative_start": 0.6162590980529785, "end": **********.719176, "relative_end": **********.719176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.719749, "relative_start": 0.6168320178985596, "end": **********.719749, "relative_end": **********.719749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.721391, "relative_start": 0.618474006652832, "end": **********.721391, "relative_end": **********.721391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.723852, "relative_start": 0.6209349632263184, "end": **********.723852, "relative_end": **********.723852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.724528, "relative_start": 0.6216111183166504, "end": **********.724528, "relative_end": **********.724528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.725077, "relative_start": 0.6221599578857422, "end": **********.725077, "relative_end": **********.725077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.72572, "relative_start": 0.6228029727935791, "end": **********.72572, "relative_end": **********.72572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.726282, "relative_start": 0.6233649253845215, "end": **********.726282, "relative_end": **********.726282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.730317, "relative_start": 0.6274001598358154, "end": **********.730317, "relative_end": **********.730317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.732605, "relative_start": 0.629688024520874, "end": **********.732605, "relative_end": **********.732605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.733262, "relative_start": 0.6303451061248779, "end": **********.733262, "relative_end": **********.733262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.733922, "relative_start": 0.631005048751831, "end": **********.733922, "relative_end": **********.733922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.734629, "relative_start": 0.6317119598388672, "end": **********.734629, "relative_end": **********.734629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.735394, "relative_start": 0.6324770450592041, "end": **********.735394, "relative_end": **********.735394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.736151, "relative_start": 0.6332340240478516, "end": **********.736151, "relative_end": **********.736151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.736999, "relative_start": 0.6340820789337158, "end": **********.736999, "relative_end": **********.736999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.73783, "relative_start": 0.6349129676818848, "end": **********.73783, "relative_end": **********.73783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.738518, "relative_start": 0.6356010437011719, "end": **********.738518, "relative_end": **********.738518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.740201, "relative_start": 0.6372840404510498, "end": **********.740201, "relative_end": **********.740201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.740911, "relative_start": 0.6379940509796143, "end": **********.740911, "relative_end": **********.740911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.741637, "relative_start": 0.6387200355529785, "end": **********.741637, "relative_end": **********.741637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.742005, "relative_start": 0.6390881538391113, "end": **********.742005, "relative_end": **********.742005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.743287, "relative_start": 0.6403701305389404, "end": **********.743287, "relative_end": **********.743287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.743793, "relative_start": 0.640876054763794, "end": **********.743793, "relative_end": **********.743793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.745141, "relative_start": 0.6422240734100342, "end": **********.745141, "relative_end": **********.745141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.747036, "relative_start": 0.6441190242767334, "end": **********.747036, "relative_end": **********.747036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.748442, "relative_start": 0.6455249786376953, "end": **********.748442, "relative_end": **********.748442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.750251, "relative_start": 0.647334098815918, "end": **********.750251, "relative_end": **********.750251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.75242, "relative_start": 0.6495029926300049, "end": **********.75242, "relative_end": **********.75242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.753377, "relative_start": 0.6504600048065186, "end": **********.753377, "relative_end": **********.753377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.754416, "relative_start": 0.6514990329742432, "end": **********.754416, "relative_end": **********.754416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.755366, "relative_start": 0.6524491310119629, "end": **********.755366, "relative_end": **********.755366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.755979, "relative_start": 0.653062105178833, "end": **********.755979, "relative_end": **********.755979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.756321, "relative_start": 0.6534039974212646, "end": **********.756321, "relative_end": **********.756321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.756798, "relative_start": 0.6538810729980469, "end": **********.756798, "relative_end": **********.756798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.757248, "relative_start": 0.6543309688568115, "end": **********.757248, "relative_end": **********.757248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.757647, "relative_start": 0.6547300815582275, "end": **********.757647, "relative_end": **********.757647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.758017, "relative_start": 0.6551001071929932, "end": **********.758017, "relative_end": **********.758017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.758858, "relative_start": 0.6559410095214844, "end": **********.758858, "relative_end": **********.758858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.759214, "relative_start": 0.656296968460083, "end": **********.759214, "relative_end": **********.759214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.759631, "relative_start": 0.6567139625549316, "end": **********.759631, "relative_end": **********.759631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.760501, "relative_start": 0.6575839519500732, "end": **********.760501, "relative_end": **********.760501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.762254, "relative_start": 0.659337043762207, "end": **********.762254, "relative_end": **********.762254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.763405, "relative_start": 0.6604881286621094, "end": **********.763405, "relative_end": **********.763405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.765466, "relative_start": 0.6625490188598633, "end": **********.765466, "relative_end": **********.765466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.766243, "relative_start": 0.6633260250091553, "end": **********.766243, "relative_end": **********.766243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.766841, "relative_start": 0.663923978805542, "end": **********.766841, "relative_end": **********.766841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.767538, "relative_start": 0.664621114730835, "end": **********.767538, "relative_end": **********.767538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.768249, "relative_start": 0.6653320789337158, "end": **********.768249, "relative_end": **********.768249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.770205, "relative_start": 0.667288064956665, "end": **********.770205, "relative_end": **********.770205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.77166, "relative_start": 0.6687431335449219, "end": **********.77166, "relative_end": **********.77166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.772247, "relative_start": 0.6693301200866699, "end": **********.772247, "relative_end": **********.772247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.772707, "relative_start": 0.6697900295257568, "end": **********.772707, "relative_end": **********.772707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.774445, "relative_start": 0.6715281009674072, "end": **********.774445, "relative_end": **********.774445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.775308, "relative_start": 0.6723909378051758, "end": **********.775308, "relative_end": **********.775308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.775858, "relative_start": 0.6729409694671631, "end": **********.775858, "relative_end": **********.775858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.776722, "relative_start": 0.6738049983978271, "end": **********.776722, "relative_end": **********.776722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.778266, "relative_start": 0.6753489971160889, "end": **********.778266, "relative_end": **********.778266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.778846, "relative_start": 0.675929069519043, "end": **********.778846, "relative_end": **********.778846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.779582, "relative_start": 0.6766650676727295, "end": **********.779582, "relative_end": **********.779582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.780644, "relative_start": 0.6777269840240479, "end": **********.780644, "relative_end": **********.780644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.781668, "relative_start": 0.6787509918212891, "end": **********.781668, "relative_end": **********.781668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.782255, "relative_start": 0.6793379783630371, "end": **********.782255, "relative_end": **********.782255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.78267, "relative_start": 0.6797530651092529, "end": **********.78267, "relative_end": **********.78267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.784238, "relative_start": 0.6813211441040039, "end": **********.784238, "relative_end": **********.784238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.785182, "relative_start": 0.682265043258667, "end": **********.785182, "relative_end": **********.785182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.785978, "relative_start": 0.6830611228942871, "end": **********.785978, "relative_end": **********.785978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.786479, "relative_start": 0.6835620403289795, "end": **********.786479, "relative_end": **********.786479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.78786, "relative_start": 0.6849429607391357, "end": **********.78786, "relative_end": **********.78786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.788401, "relative_start": 0.6854839324951172, "end": **********.788401, "relative_end": **********.788401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.788879, "relative_start": 0.6859619617462158, "end": **********.788879, "relative_end": **********.788879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.789467, "relative_start": 0.6865501403808594, "end": **********.789467, "relative_end": **********.789467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.789999, "relative_start": 0.687082052230835, "end": **********.789999, "relative_end": **********.789999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.790787, "relative_start": 0.6878700256347656, "end": **********.790787, "relative_end": **********.790787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.79203, "relative_start": 0.6891131401062012, "end": **********.79203, "relative_end": **********.79203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.792597, "relative_start": 0.6896800994873047, "end": **********.792597, "relative_end": **********.792597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.793427, "relative_start": 0.6905100345611572, "end": **********.793427, "relative_end": **********.793427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.794722, "relative_start": 0.6918051242828369, "end": **********.794722, "relative_end": **********.794722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.795917, "relative_start": 0.693000078201294, "end": **********.795917, "relative_end": **********.795917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.798663, "relative_start": 0.6957459449768066, "end": **********.798663, "relative_end": **********.798663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.800541, "relative_start": 0.6976239681243896, "end": **********.800541, "relative_end": **********.800541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.801506, "relative_start": 0.6985890865325928, "end": **********.801506, "relative_end": **********.801506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.80213, "relative_start": 0.6992130279541016, "end": **********.80213, "relative_end": **********.80213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.802691, "relative_start": 0.6997740268707275, "end": **********.802691, "relative_end": **********.802691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.803186, "relative_start": 0.7002689838409424, "end": **********.803186, "relative_end": **********.803186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.80364, "relative_start": 0.7007229328155518, "end": **********.80364, "relative_end": **********.80364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.804159, "relative_start": 0.7012419700622559, "end": **********.804159, "relative_end": **********.804159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.804568, "relative_start": 0.7016510963439941, "end": **********.804568, "relative_end": **********.804568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.804893, "relative_start": 0.7019760608673096, "end": **********.804893, "relative_end": **********.804893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.806164, "relative_start": 0.7032470703125, "end": **********.806164, "relative_end": **********.806164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.807039, "relative_start": 0.7041220664978027, "end": **********.807039, "relative_end": **********.807039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.807869, "relative_start": 0.7049520015716553, "end": **********.807869, "relative_end": **********.807869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.808276, "relative_start": 0.7053589820861816, "end": **********.808276, "relative_end": **********.808276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.810164, "relative_start": 0.7072470188140869, "end": **********.810164, "relative_end": **********.810164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.811301, "relative_start": 0.7083840370178223, "end": **********.811301, "relative_end": **********.811301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.812295, "relative_start": 0.7093780040740967, "end": **********.812295, "relative_end": **********.812295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.813245, "relative_start": 0.7103281021118164, "end": **********.813245, "relative_end": **********.813245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.814429, "relative_start": 0.7115120887756348, "end": **********.814429, "relative_end": **********.814429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.815887, "relative_start": 0.7129700183868408, "end": **********.815887, "relative_end": **********.815887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.817701, "relative_start": 0.7147841453552246, "end": **********.817701, "relative_end": **********.817701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.818295, "relative_start": 0.7153780460357666, "end": **********.818295, "relative_end": **********.818295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.818742, "relative_start": 0.715825080871582, "end": **********.818742, "relative_end": **********.818742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.819254, "relative_start": 0.7163369655609131, "end": **********.819254, "relative_end": **********.819254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.819745, "relative_start": 0.7168281078338623, "end": **********.819745, "relative_end": **********.819745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.82001, "relative_start": 0.7170929908752441, "end": **********.82001, "relative_end": **********.82001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.820384, "relative_start": 0.7174670696258545, "end": **********.820384, "relative_end": **********.820384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.820787, "relative_start": 0.7178699970245361, "end": **********.820787, "relative_end": **********.820787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.821179, "relative_start": 0.7182619571685791, "end": **********.821179, "relative_end": **********.821179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.821558, "relative_start": 0.7186410427093506, "end": **********.821558, "relative_end": **********.821558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.822376, "relative_start": 0.719459056854248, "end": **********.822376, "relative_end": **********.822376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.822754, "relative_start": 0.719836950302124, "end": **********.822754, "relative_end": **********.822754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.823151, "relative_start": 0.7202341556549072, "end": **********.823151, "relative_end": **********.823151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.82398, "relative_start": 0.7210631370544434, "end": **********.82398, "relative_end": **********.82398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.824402, "relative_start": 0.7214851379394531, "end": **********.824402, "relative_end": **********.824402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.825099, "relative_start": 0.722182035446167, "end": **********.825099, "relative_end": **********.825099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.827219, "relative_start": 0.7243020534515381, "end": **********.827219, "relative_end": **********.827219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.829114, "relative_start": 0.7261970043182373, "end": **********.829114, "relative_end": **********.829114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.830071, "relative_start": 0.727154016494751, "end": **********.830071, "relative_end": **********.830071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.831348, "relative_start": 0.728430986404419, "end": **********.831348, "relative_end": **********.831348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.832203, "relative_start": 0.7292859554290771, "end": **********.832203, "relative_end": **********.832203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.834621, "relative_start": 0.7317039966583252, "end": **********.834621, "relative_end": **********.834621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.835888, "relative_start": 0.7329709529876709, "end": **********.835888, "relative_end": **********.835888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.836421, "relative_start": 0.733504056930542, "end": **********.836421, "relative_end": **********.836421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.836883, "relative_start": 0.7339661121368408, "end": **********.836883, "relative_end": **********.836883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.838607, "relative_start": 0.7356901168823242, "end": **********.838607, "relative_end": **********.838607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.839441, "relative_start": 0.7365241050720215, "end": **********.839441, "relative_end": **********.839441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.839985, "relative_start": 0.7370679378509521, "end": **********.839985, "relative_end": **********.839985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.840489, "relative_start": 0.7375719547271729, "end": **********.840489, "relative_end": **********.840489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.841032, "relative_start": 0.7381150722503662, "end": **********.841032, "relative_end": **********.841032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.841612, "relative_start": 0.7386951446533203, "end": **********.841612, "relative_end": **********.841612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.842047, "relative_start": 0.7391300201416016, "end": **********.842047, "relative_end": **********.842047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.842596, "relative_start": 0.7396790981292725, "end": **********.842596, "relative_end": **********.842596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.843131, "relative_start": 0.7402141094207764, "end": **********.843131, "relative_end": **********.843131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.843618, "relative_start": 0.7407009601593018, "end": **********.843618, "relative_end": **********.843618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.844498, "relative_start": 0.7415809631347656, "end": **********.844498, "relative_end": **********.844498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.847361, "relative_start": 0.7444441318511963, "end": **********.847361, "relative_end": **********.847361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.84819, "relative_start": 0.7452731132507324, "end": **********.84819, "relative_end": **********.84819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.849316, "relative_start": 0.74639892578125, "end": **********.849316, "relative_end": **********.849316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.849926, "relative_start": 0.7470090389251709, "end": **********.849926, "relative_end": **********.849926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.851889, "relative_start": 0.7489719390869141, "end": **********.851889, "relative_end": **********.851889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.852445, "relative_start": 0.7495279312133789, "end": **********.852445, "relative_end": **********.852445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.852952, "relative_start": 0.7500350475311279, "end": **********.852952, "relative_end": **********.852952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.853494, "relative_start": 0.7505769729614258, "end": **********.853494, "relative_end": **********.853494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.85404, "relative_start": 0.7511229515075684, "end": **********.85404, "relative_end": **********.85404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.854847, "relative_start": 0.7519299983978271, "end": **********.854847, "relative_end": **********.854847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.856093, "relative_start": 0.7531759738922119, "end": **********.856093, "relative_end": **********.856093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.85671, "relative_start": 0.7537930011749268, "end": **********.85671, "relative_end": **********.85671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.857337, "relative_start": 0.7544200420379639, "end": **********.857337, "relative_end": **********.857337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.85824, "relative_start": 0.7553229331970215, "end": **********.85824, "relative_end": **********.85824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.85901, "relative_start": 0.7560930252075195, "end": **********.85901, "relative_end": **********.85901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.860481, "relative_start": 0.7575640678405762, "end": **********.860481, "relative_end": **********.860481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.86513, "relative_start": 0.7622129917144775, "end": **********.86513, "relative_end": **********.86513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.86621, "relative_start": 0.7632930278778076, "end": **********.86621, "relative_end": **********.86621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.867031, "relative_start": 0.7641141414642334, "end": **********.867031, "relative_end": **********.867031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.867754, "relative_start": 0.7648370265960693, "end": **********.867754, "relative_end": **********.867754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.86881, "relative_start": 0.7658929824829102, "end": **********.86881, "relative_end": **********.86881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.869329, "relative_start": 0.7664120197296143, "end": **********.869329, "relative_end": **********.869329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.869841, "relative_start": 0.7669241428375244, "end": **********.869841, "relative_end": **********.869841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.870274, "relative_start": 0.7673571109771729, "end": **********.870274, "relative_end": **********.870274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.87062, "relative_start": 0.7677030563354492, "end": **********.87062, "relative_end": **********.87062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.871812, "relative_start": 0.768895149230957, "end": **********.871812, "relative_end": **********.871812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.872395, "relative_start": 0.7694780826568604, "end": **********.872395, "relative_end": **********.872395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.873051, "relative_start": 0.7701339721679688, "end": **********.873051, "relative_end": **********.873051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.873407, "relative_start": 0.7704899311065674, "end": **********.873407, "relative_end": **********.873407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.875161, "relative_start": 0.7722439765930176, "end": **********.875161, "relative_end": **********.875161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.875739, "relative_start": 0.7728221416473389, "end": **********.875739, "relative_end": **********.875739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.876164, "relative_start": 0.7732470035552979, "end": **********.876164, "relative_end": **********.876164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.87699, "relative_start": 0.7740731239318848, "end": **********.87699, "relative_end": **********.87699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.878151, "relative_start": 0.7752339839935303, "end": **********.878151, "relative_end": **********.878151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.879393, "relative_start": 0.7764761447906494, "end": **********.879393, "relative_end": **********.879393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.881189, "relative_start": 0.7782721519470215, "end": **********.881189, "relative_end": **********.881189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.881983, "relative_start": 0.7790660858154297, "end": **********.881983, "relative_end": **********.881983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.882797, "relative_start": 0.7798800468444824, "end": **********.882797, "relative_end": **********.882797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.883853, "relative_start": 0.7809360027313232, "end": **********.883853, "relative_end": **********.883853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.884821, "relative_start": 0.7819039821624756, "end": **********.884821, "relative_end": **********.884821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.885238, "relative_start": 0.7823209762573242, "end": **********.885238, "relative_end": **********.885238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.885741, "relative_start": 0.7828240394592285, "end": **********.885741, "relative_end": **********.885741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.886208, "relative_start": 0.7832911014556885, "end": **********.886208, "relative_end": **********.886208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.886642, "relative_start": 0.7837250232696533, "end": **********.886642, "relative_end": **********.886642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.887087, "relative_start": 0.7841701507568359, "end": **********.887087, "relative_end": **********.887087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.88801, "relative_start": 0.7850930690765381, "end": **********.88801, "relative_end": **********.88801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.888404, "relative_start": 0.7854869365692139, "end": **********.888404, "relative_end": **********.888404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.888891, "relative_start": 0.7859740257263184, "end": **********.888891, "relative_end": **********.888891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.889799, "relative_start": 0.7868821620941162, "end": **********.889799, "relative_end": **********.889799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.890187, "relative_start": 0.7872700691223145, "end": **********.890187, "relative_end": **********.890187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.890656, "relative_start": 0.7877390384674072, "end": **********.890656, "relative_end": **********.890656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.891867, "relative_start": 0.7889499664306641, "end": **********.891867, "relative_end": **********.891867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.892386, "relative_start": 0.7894690036773682, "end": **********.892386, "relative_end": **********.892386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.893116, "relative_start": 0.7901990413665771, "end": **********.893116, "relative_end": **********.893116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.893768, "relative_start": 0.7908511161804199, "end": **********.893768, "relative_end": **********.893768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.894894, "relative_start": 0.7919769287109375, "end": **********.894894, "relative_end": **********.894894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.897008, "relative_start": 0.794090986251831, "end": **********.897008, "relative_end": **********.897008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.898323, "relative_start": 0.7954061031341553, "end": **********.898323, "relative_end": **********.898323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.898889, "relative_start": 0.7959721088409424, "end": **********.898889, "relative_end": **********.898889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.899341, "relative_start": 0.796424150466919, "end": **********.899341, "relative_end": **********.899341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.902309, "relative_start": 0.7993919849395752, "end": **********.902309, "relative_end": **********.902309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.903272, "relative_start": 0.8003549575805664, "end": **********.903272, "relative_end": **********.903272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.903877, "relative_start": 0.8009600639343262, "end": **********.903877, "relative_end": **********.903877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.904382, "relative_start": 0.8014650344848633, "end": **********.904382, "relative_end": **********.904382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.904931, "relative_start": 0.8020141124725342, "end": **********.904931, "relative_end": **********.904931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.905399, "relative_start": 0.8024821281433105, "end": **********.905399, "relative_end": **********.905399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.905961, "relative_start": 0.8030440807342529, "end": **********.905961, "relative_end": **********.905961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.906505, "relative_start": 0.8035881519317627, "end": **********.906505, "relative_end": **********.906505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.907082, "relative_start": 0.8041651248931885, "end": **********.907082, "relative_end": **********.907082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.907567, "relative_start": 0.804650068283081, "end": **********.907567, "relative_end": **********.907567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.907913, "relative_start": 0.8049960136413574, "end": **********.907913, "relative_end": **********.907913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.90943, "relative_start": 0.8065130710601807, "end": **********.90943, "relative_end": **********.90943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.910081, "relative_start": 0.8071639537811279, "end": **********.910081, "relative_end": **********.910081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.911276, "relative_start": 0.8083591461181641, "end": **********.911276, "relative_end": **********.911276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.912004, "relative_start": 0.8090870380401611, "end": **********.912004, "relative_end": **********.912004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.914445, "relative_start": 0.8115279674530029, "end": **********.914445, "relative_end": **********.914445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.915282, "relative_start": 0.8123650550842285, "end": **********.915282, "relative_end": **********.915282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.915743, "relative_start": 0.8128261566162109, "end": **********.915743, "relative_end": **********.915743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.916222, "relative_start": 0.813305139541626, "end": **********.916222, "relative_end": **********.916222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.91666, "relative_start": 0.8137431144714355, "end": **********.91666, "relative_end": **********.91666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.91735, "relative_start": 0.8144330978393555, "end": **********.91735, "relative_end": **********.91735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.918427, "relative_start": 0.8155100345611572, "end": **********.918427, "relative_end": **********.918427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.918859, "relative_start": 0.8159420490264893, "end": **********.918859, "relative_end": **********.918859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.919266, "relative_start": 0.8163490295410156, "end": **********.919266, "relative_end": **********.919266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.919713, "relative_start": 0.816796064376831, "end": **********.919713, "relative_end": **********.919713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.920148, "relative_start": 0.8172309398651123, "end": **********.920148, "relative_end": **********.920148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.921443, "relative_start": 0.818526029586792, "end": **********.921443, "relative_end": **********.921443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.922657, "relative_start": 0.8197400569915771, "end": **********.922657, "relative_end": **********.922657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.923114, "relative_start": 0.8201971054077148, "end": **********.923114, "relative_end": **********.923114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.923533, "relative_start": 0.8206160068511963, "end": **********.923533, "relative_end": **********.923533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.92433, "relative_start": 0.8214130401611328, "end": **********.92433, "relative_end": **********.92433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.924919, "relative_start": 0.8220019340515137, "end": **********.924919, "relative_end": **********.924919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.925387, "relative_start": 0.82246994972229, "end": **********.925387, "relative_end": **********.925387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.9259, "relative_start": 0.8229830265045166, "end": **********.9259, "relative_end": **********.9259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.926292, "relative_start": 0.8233749866485596, "end": **********.926292, "relative_end": **********.926292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.926597, "relative_start": 0.8236801624298096, "end": **********.926597, "relative_end": **********.926597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.928697, "relative_start": 0.8257801532745361, "end": **********.928697, "relative_end": **********.928697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.930281, "relative_start": 0.8273639678955078, "end": **********.930281, "relative_end": **********.930281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.93135, "relative_start": 0.8284330368041992, "end": **********.93135, "relative_end": **********.93135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.931771, "relative_start": 0.8288540840148926, "end": **********.931771, "relative_end": **********.931771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.93331, "relative_start": 0.8303930759429932, "end": **********.93331, "relative_end": **********.93331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.933824, "relative_start": 0.8309071063995361, "end": **********.933824, "relative_end": **********.933824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.934944, "relative_start": 0.8320269584655762, "end": **********.934944, "relative_end": **********.934944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.936378, "relative_start": 0.8334610462188721, "end": **********.936378, "relative_end": **********.936378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.936984, "relative_start": 0.8340671062469482, "end": **********.936984, "relative_end": **********.936984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.937719, "relative_start": 0.8348021507263184, "end": **********.937719, "relative_end": **********.937719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.939115, "relative_start": 0.836198091506958, "end": **********.939115, "relative_end": **********.939115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.939838, "relative_start": 0.836920976638794, "end": **********.939838, "relative_end": **********.939838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.94031, "relative_start": 0.837393045425415, "end": **********.94031, "relative_end": **********.94031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.940831, "relative_start": 0.837913990020752, "end": **********.940831, "relative_end": **********.940831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.941273, "relative_start": 0.8383560180664062, "end": **********.941273, "relative_end": **********.941273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.94153, "relative_start": 0.8386130332946777, "end": **********.94153, "relative_end": **********.94153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.941895, "relative_start": 0.8389780521392822, "end": **********.941895, "relative_end": **********.941895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.942312, "relative_start": 0.8393950462341309, "end": **********.942312, "relative_end": **********.942312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.942674, "relative_start": 0.839756965637207, "end": **********.942674, "relative_end": **********.942674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.943034, "relative_start": 0.8401169776916504, "end": **********.943034, "relative_end": **********.943034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.94412, "relative_start": 0.841202974319458, "end": **********.94412, "relative_end": **********.94412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.944795, "relative_start": 0.8418779373168945, "end": **********.944795, "relative_end": **********.944795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.945388, "relative_start": 0.8424711227416992, "end": **********.945388, "relative_end": **********.945388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.947141, "relative_start": 0.8442239761352539, "end": **********.947141, "relative_end": **********.947141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.947686, "relative_start": 0.8447690010070801, "end": **********.947686, "relative_end": **********.947686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.948221, "relative_start": 0.845304012298584, "end": **********.948221, "relative_end": **********.948221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.950082, "relative_start": 0.8471651077270508, "end": **********.950082, "relative_end": **********.950082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.950756, "relative_start": 0.8478391170501709, "end": **********.950756, "relative_end": **********.950756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.951372, "relative_start": 0.8484549522399902, "end": **********.951372, "relative_end": **********.951372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.951922, "relative_start": 0.8490049839019775, "end": **********.951922, "relative_end": **********.951922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.952491, "relative_start": 0.849574089050293, "end": **********.952491, "relative_end": **********.952491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.955259, "relative_start": 0.8523421287536621, "end": **********.955259, "relative_end": **********.955259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.956494, "relative_start": 0.8535771369934082, "end": **********.956494, "relative_end": **********.956494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.956996, "relative_start": 0.854079008102417, "end": **********.956996, "relative_end": **********.956996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.957388, "relative_start": 0.85447096824646, "end": **********.957388, "relative_end": **********.957388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.958927, "relative_start": 0.8560099601745605, "end": **********.958927, "relative_end": **********.958927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.959682, "relative_start": 0.8567650318145752, "end": **********.959682, "relative_end": **********.959682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.960184, "relative_start": 0.8572671413421631, "end": **********.960184, "relative_end": **********.960184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.961571, "relative_start": 0.8586540222167969, "end": **********.961571, "relative_end": **********.961571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.962309, "relative_start": 0.8593919277191162, "end": **********.962309, "relative_end": **********.962309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.962878, "relative_start": 0.8599610328674316, "end": **********.962878, "relative_end": **********.962878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.963577, "relative_start": 0.8606600761413574, "end": **********.963577, "relative_end": **********.963577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.964189, "relative_start": 0.8612720966339111, "end": **********.964189, "relative_end": **********.964189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.964714, "relative_start": 0.8617970943450928, "end": **********.964714, "relative_end": **********.964714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.965184, "relative_start": 0.862267017364502, "end": **********.965184, "relative_end": **********.965184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.965514, "relative_start": 0.8625969886779785, "end": **********.965514, "relative_end": **********.965514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.966683, "relative_start": 0.8637659549713135, "end": **********.966683, "relative_end": **********.966683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.967485, "relative_start": 0.8645679950714111, "end": **********.967485, "relative_end": **********.967485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.969411, "relative_start": 0.8664939403533936, "end": **********.969411, "relative_end": **********.969411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.970026, "relative_start": 0.8671090602874756, "end": **********.970026, "relative_end": **********.970026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.97203, "relative_start": 0.8691129684448242, "end": **********.97203, "relative_end": **********.97203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.972575, "relative_start": 0.8696579933166504, "end": **********.972575, "relative_end": **********.972575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.973064, "relative_start": 0.8701469898223877, "end": **********.973064, "relative_end": **********.973064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.973578, "relative_start": 0.8706610202789307, "end": **********.973578, "relative_end": **********.973578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.974082, "relative_start": 0.8711650371551514, "end": **********.974082, "relative_end": **********.974082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.974858, "relative_start": 0.871941089630127, "end": **********.974858, "relative_end": **********.974858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.976084, "relative_start": 0.8731670379638672, "end": **********.976084, "relative_end": **********.976084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.976675, "relative_start": 0.87375807762146, "end": **********.976675, "relative_end": **********.976675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.97802, "relative_start": 0.8751029968261719, "end": **********.97802, "relative_end": **********.97802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.978746, "relative_start": 0.8758289813995361, "end": **********.978746, "relative_end": **********.978746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.979382, "relative_start": 0.8764650821685791, "end": **********.979382, "relative_end": **********.979382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.981219, "relative_start": 0.8783020973205566, "end": **********.981219, "relative_end": **********.981219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.982472, "relative_start": 0.8795549869537354, "end": **********.982472, "relative_end": **********.982472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.98293, "relative_start": 0.8800129890441895, "end": **********.98293, "relative_end": **********.98293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.983338, "relative_start": 0.8804211616516113, "end": **********.983338, "relative_end": **********.983338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.983794, "relative_start": 0.8808770179748535, "end": **********.983794, "relative_end": **********.983794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.985698, "relative_start": 0.8827810287475586, "end": **********.985698, "relative_end": **********.985698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.986975, "relative_start": 0.8840579986572266, "end": **********.986975, "relative_end": **********.986975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.98773, "relative_start": 0.8848130702972412, "end": **********.98773, "relative_end": **********.98773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.988391, "relative_start": 0.8854739665985107, "end": **********.988391, "relative_end": **********.988391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.988959, "relative_start": 0.8860421180725098, "end": **********.988959, "relative_end": **********.988959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.990693, "relative_start": 0.8877761363983154, "end": **********.990693, "relative_end": **********.990693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.991255, "relative_start": 0.8883380889892578, "end": **********.991255, "relative_end": **********.991255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.991789, "relative_start": 0.8888721466064453, "end": **********.991789, "relative_end": **********.991789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.992104, "relative_start": 0.8891870975494385, "end": **********.992104, "relative_end": **********.992104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.993729, "relative_start": 0.8908121585845947, "end": **********.993729, "relative_end": **********.993729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.994753, "relative_start": 0.8918359279632568, "end": **********.994753, "relative_end": **********.994753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.995376, "relative_start": 0.8924591541290283, "end": **********.995376, "relative_end": **********.995376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.996166, "relative_start": 0.8932490348815918, "end": **********.996166, "relative_end": **********.996166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.997003, "relative_start": 0.8940861225128174, "end": **********.997003, "relative_end": **********.997003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.997846, "relative_start": 0.8949289321899414, "end": **********.997846, "relative_end": **********.997846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.999122, "relative_start": 0.896204948425293, "end": **********.999122, "relative_end": **********.999122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.999619, "relative_start": 0.8967020511627197, "end": **********.999619, "relative_end": **********.999619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.0001, "relative_start": 0.8971829414367676, "end": **********.0001, "relative_end": **********.0001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.000763, "relative_start": 0.897845983505249, "end": **********.000763, "relative_end": **********.000763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.003118, "relative_start": 0.9002010822296143, "end": **********.003118, "relative_end": **********.003118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.004351, "relative_start": 0.9014339447021484, "end": **********.004351, "relative_end": **********.004351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.00572, "relative_start": 0.9028029441833496, "end": **********.00572, "relative_end": **********.00572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.006669, "relative_start": 0.9037520885467529, "end": **********.006669, "relative_end": **********.006669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.007514, "relative_start": 0.9045970439910889, "end": **********.007514, "relative_end": **********.007514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.008882, "relative_start": 0.9059650897979736, "end": **********.008882, "relative_end": **********.008882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.010039, "relative_start": 0.9071221351623535, "end": **********.010039, "relative_end": **********.010039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.01047, "relative_start": 0.90755295753479, "end": **********.01047, "relative_end": **********.01047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.011522, "relative_start": 0.9086050987243652, "end": **********.011522, "relative_end": **********.011522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.012629, "relative_start": 0.9097120761871338, "end": **********.012629, "relative_end": **********.012629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.013108, "relative_start": 0.9101910591125488, "end": **********.013108, "relative_end": **********.013108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.013629, "relative_start": 0.9107120037078857, "end": **********.013629, "relative_end": **********.013629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.015763, "relative_start": 0.9128460884094238, "end": **********.015763, "relative_end": **********.015763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.019177, "relative_start": 0.9162600040435791, "end": **********.019177, "relative_end": **********.019177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.020976, "relative_start": 0.9180591106414795, "end": **********.020976, "relative_end": **********.020976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.022482, "relative_start": 0.919564962387085, "end": **********.022482, "relative_end": **********.022482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.023282, "relative_start": 0.9203650951385498, "end": **********.023282, "relative_end": **********.023282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.026292, "relative_start": 0.923375129699707, "end": **********.026292, "relative_end": **********.026292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.02835, "relative_start": 0.9254331588745117, "end": **********.02835, "relative_end": **********.02835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.029234, "relative_start": 0.9263169765472412, "end": **********.029234, "relative_end": **********.029234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.029953, "relative_start": 0.9270360469818115, "end": **********.029953, "relative_end": **********.029953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.031948, "relative_start": 0.9290311336517334, "end": **********.031948, "relative_end": **********.031948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.035209, "relative_start": 0.9322919845581055, "end": **********.035209, "relative_end": **********.035209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.036244, "relative_start": 0.9333269596099854, "end": **********.036244, "relative_end": **********.036244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.037088, "relative_start": 0.9341709613800049, "end": **********.037088, "relative_end": **********.037088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.037747, "relative_start": 0.9348299503326416, "end": **********.037747, "relative_end": **********.037747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.038193, "relative_start": 0.9352760314941406, "end": **********.038193, "relative_end": **********.038193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.038664, "relative_start": 0.9357471466064453, "end": **********.038664, "relative_end": **********.038664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.039233, "relative_start": 0.9363160133361816, "end": **********.039233, "relative_end": **********.039233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.039941, "relative_start": 0.9370241165161133, "end": **********.039941, "relative_end": **********.039941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.041032, "relative_start": 0.938115119934082, "end": **********.041032, "relative_end": **********.041032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.041578, "relative_start": 0.9386610984802246, "end": **********.041578, "relative_end": **********.041578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.042832, "relative_start": 0.9399149417877197, "end": **********.042832, "relative_end": **********.042832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.04336, "relative_start": 0.9404430389404297, "end": **********.04336, "relative_end": **********.04336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.043848, "relative_start": 0.9409310817718506, "end": **********.043848, "relative_end": **********.043848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.044608, "relative_start": 0.9416911602020264, "end": **********.044608, "relative_end": **********.044608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.046666, "relative_start": 0.943748950958252, "end": **********.046666, "relative_end": **********.046666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.047299, "relative_start": 0.9443819522857666, "end": **********.047299, "relative_end": **********.047299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.047764, "relative_start": 0.9448471069335938, "end": **********.047764, "relative_end": **********.047764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.048259, "relative_start": 0.9453420639038086, "end": **********.048259, "relative_end": **********.048259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.049569, "relative_start": 0.9466519355773926, "end": **********.049569, "relative_end": **********.049569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.051023, "relative_start": 0.948106050491333, "end": **********.051023, "relative_end": **********.051023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.05306, "relative_start": 0.9501430988311768, "end": **********.05306, "relative_end": **********.05306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.054042, "relative_start": 0.9511251449584961, "end": **********.054042, "relative_end": **********.054042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.05461, "relative_start": 0.951693058013916, "end": **********.05461, "relative_end": **********.05461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.055177, "relative_start": 0.9522600173950195, "end": **********.055177, "relative_end": **********.055177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.05577, "relative_start": 0.9528529644012451, "end": **********.05577, "relative_end": **********.05577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.057955, "relative_start": 0.9550380706787109, "end": **********.057955, "relative_end": **********.057955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.059361, "relative_start": 0.9564440250396729, "end": **********.059361, "relative_end": **********.059361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.059911, "relative_start": 0.9569940567016602, "end": **********.059911, "relative_end": **********.059911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.060377, "relative_start": 0.9574599266052246, "end": **********.060377, "relative_end": **********.060377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.061491, "relative_start": 0.9585740566253662, "end": **********.061491, "relative_end": **********.061491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.06234, "relative_start": 0.9594230651855469, "end": **********.06234, "relative_end": **********.06234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.063057, "relative_start": 0.9601399898529053, "end": **********.063057, "relative_end": **********.063057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.06375, "relative_start": 0.9608330726623535, "end": **********.06375, "relative_end": **********.06375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.064203, "relative_start": 0.9612860679626465, "end": **********.064203, "relative_end": **********.064203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.06495, "relative_start": 0.9620330333709717, "end": **********.06495, "relative_end": **********.06495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.066509, "relative_start": 0.9635920524597168, "end": **********.066509, "relative_end": **********.066509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.067074, "relative_start": 0.9641571044921875, "end": **********.067074, "relative_end": **********.067074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.06767, "relative_start": 0.9647531509399414, "end": **********.06767, "relative_end": **********.06767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.068447, "relative_start": 0.9655301570892334, "end": **********.068447, "relative_end": **********.068447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.07076, "relative_start": 0.9678430557250977, "end": **********.07076, "relative_end": **********.07076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.07154, "relative_start": 0.968623161315918, "end": **********.07154, "relative_end": **********.07154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.072063, "relative_start": 0.9691460132598877, "end": **********.072063, "relative_end": **********.072063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.072663, "relative_start": 0.9697461128234863, "end": **********.072663, "relative_end": **********.072663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.073409, "relative_start": 0.9704921245574951, "end": **********.073409, "relative_end": **********.073409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.074605, "relative_start": 0.9716880321502686, "end": **********.074605, "relative_end": **********.074605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.075866, "relative_start": 0.9729490280151367, "end": **********.075866, "relative_end": **********.075866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.076345, "relative_start": 0.9734280109405518, "end": **********.076345, "relative_end": **********.076345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.076801, "relative_start": 0.973884105682373, "end": **********.076801, "relative_end": **********.076801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.077615, "relative_start": 0.9746980667114258, "end": **********.077615, "relative_end": **********.077615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.078363, "relative_start": 0.9754459857940674, "end": **********.078363, "relative_end": **********.078363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.078738, "relative_start": 0.9758210182189941, "end": **********.078738, "relative_end": **********.078738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.079255, "relative_start": 0.9763381481170654, "end": **********.079255, "relative_end": **********.079255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.08082, "relative_start": 0.9779031276702881, "end": **********.08082, "relative_end": **********.08082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.081729, "relative_start": 0.9788119792938232, "end": **********.081729, "relative_end": **********.081729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.082377, "relative_start": 0.9794600009918213, "end": **********.082377, "relative_end": **********.082377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.083377, "relative_start": 0.9804599285125732, "end": **********.083377, "relative_end": **********.083377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.083784, "relative_start": 0.9808671474456787, "end": **********.083784, "relative_end": **********.083784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.084213, "relative_start": 0.9812960624694824, "end": **********.084213, "relative_end": **********.084213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.086154, "relative_start": 0.9832370281219482, "end": **********.086154, "relative_end": **********.086154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.086925, "relative_start": 0.9840080738067627, "end": **********.086925, "relative_end": **********.086925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.087797, "relative_start": 0.9848799705505371, "end": **********.087797, "relative_end": **********.087797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.089556, "relative_start": 0.9866390228271484, "end": **********.089556, "relative_end": **********.089556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.09031, "relative_start": 0.9873931407928467, "end": **********.09031, "relative_end": **********.09031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.090973, "relative_start": 0.988055944442749, "end": **********.090973, "relative_end": **********.090973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.091645, "relative_start": 0.9887280464172363, "end": **********.091645, "relative_end": **********.091645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.092165, "relative_start": 0.9892480373382568, "end": **********.092165, "relative_end": **********.092165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.093734, "relative_start": 0.9908170700073242, "end": **********.093734, "relative_end": **********.093734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.096346, "relative_start": 0.9934289455413818, "end": **********.096346, "relative_end": **********.096346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.097249, "relative_start": 0.9943320751190186, "end": **********.097249, "relative_end": **********.097249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.09793, "relative_start": 0.9950129985809326, "end": **********.09793, "relative_end": **********.09793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.099787, "relative_start": 0.9968700408935547, "end": **********.099787, "relative_end": **********.099787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.101118, "relative_start": 0.9982011318206787, "end": **********.101118, "relative_end": **********.101118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.101965, "relative_start": 0.9990479946136475, "end": **********.101965, "relative_end": **********.101965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.103236, "relative_start": 1.000319004058838, "end": **********.103236, "relative_end": **********.103236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.104092, "relative_start": 1.0011749267578125, "end": **********.104092, "relative_end": **********.104092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.104881, "relative_start": 1.0019640922546387, "end": **********.104881, "relative_end": **********.104881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.105561, "relative_start": 1.0026440620422363, "end": **********.105561, "relative_end": **********.105561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.106276, "relative_start": 1.003359079360962, "end": **********.106276, "relative_end": **********.106276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.106966, "relative_start": 1.0040490627288818, "end": **********.106966, "relative_end": **********.106966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.107628, "relative_start": 1.0047111511230469, "end": **********.107628, "relative_end": **********.107628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.108013, "relative_start": 1.0050959587097168, "end": **********.108013, "relative_end": **********.108013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.109266, "relative_start": 1.0063490867614746, "end": **********.109266, "relative_end": **********.109266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.109825, "relative_start": 1.0069079399108887, "end": **********.109825, "relative_end": **********.109825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.110343, "relative_start": 1.0074260234832764, "end": **********.110343, "relative_end": **********.110343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.111498, "relative_start": 1.0085811614990234, "end": **********.111498, "relative_end": **********.111498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.113887, "relative_start": 1.010970115661621, "end": **********.113887, "relative_end": **********.113887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.11451, "relative_start": 1.0115931034088135, "end": **********.11451, "relative_end": **********.11451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.115003, "relative_start": 1.0120861530303955, "end": **********.115003, "relative_end": **********.115003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.115585, "relative_start": 1.0126681327819824, "end": **********.115585, "relative_end": **********.115585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.116152, "relative_start": 1.013235092163086, "end": **********.116152, "relative_end": **********.116152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.116972, "relative_start": 1.0140550136566162, "end": **********.116972, "relative_end": **********.116972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.119383, "relative_start": 1.0164661407470703, "end": **********.119383, "relative_end": **********.119383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.120372, "relative_start": 1.0174551010131836, "end": **********.120372, "relative_end": **********.120372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.121232, "relative_start": 1.018315076828003, "end": **********.121232, "relative_end": **********.121232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.122346, "relative_start": 1.0194289684295654, "end": **********.122346, "relative_end": **********.122346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.122965, "relative_start": 1.0200481414794922, "end": **********.122965, "relative_end": **********.122965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.124713, "relative_start": 1.0217959880828857, "end": **********.124713, "relative_end": **********.124713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.125957, "relative_start": 1.0230400562286377, "end": **********.125957, "relative_end": **********.125957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.126694, "relative_start": 1.0237770080566406, "end": **********.126694, "relative_end": **********.126694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.127835, "relative_start": 1.0249180793762207, "end": **********.127835, "relative_end": **********.127835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.128715, "relative_start": 1.0257980823516846, "end": **********.128715, "relative_end": **********.128715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.129906, "relative_start": 1.0269889831542969, "end": **********.129906, "relative_end": **********.129906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.130482, "relative_start": 1.0275650024414062, "end": **********.130482, "relative_end": **********.130482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.131003, "relative_start": 1.0280859470367432, "end": **********.131003, "relative_end": **********.131003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.13139, "relative_start": 1.028473138809204, "end": **********.13139, "relative_end": **********.13139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.131703, "relative_start": 1.0287859439849854, "end": **********.131703, "relative_end": **********.131703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.132878, "relative_start": 1.029961109161377, "end": **********.132878, "relative_end": **********.132878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.133387, "relative_start": 1.0304701328277588, "end": **********.133387, "relative_end": **********.133387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.133897, "relative_start": 1.030980110168457, "end": **********.133897, "relative_end": **********.133897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.134623, "relative_start": 1.0317060947418213, "end": **********.134623, "relative_end": **********.134623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.137094, "relative_start": 1.0341770648956299, "end": **********.137094, "relative_end": **********.137094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.138037, "relative_start": 1.0351200103759766, "end": **********.138037, "relative_end": **********.138037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.138552, "relative_start": 1.035634994506836, "end": **********.138552, "relative_end": **********.138552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.139182, "relative_start": 1.0362651348114014, "end": **********.139182, "relative_end": **********.139182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.139761, "relative_start": 1.03684401512146, "end": **********.139761, "relative_end": **********.139761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.140715, "relative_start": 1.0377979278564453, "end": **********.140715, "relative_end": **********.140715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.142128, "relative_start": 1.0392110347747803, "end": **********.142128, "relative_end": **********.142128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.142933, "relative_start": 1.0400159358978271, "end": **********.142933, "relative_end": **********.142933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.143476, "relative_start": 1.0405590534210205, "end": **********.143476, "relative_end": **********.143476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.144762, "relative_start": 1.0418450832366943, "end": **********.144762, "relative_end": **********.144762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.145666, "relative_start": 1.0427489280700684, "end": **********.145666, "relative_end": **********.145666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.146328, "relative_start": 1.0434110164642334, "end": **********.146328, "relative_end": **********.146328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.147093, "relative_start": 1.0441761016845703, "end": **********.147093, "relative_end": **********.147093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.147657, "relative_start": 1.0447399616241455, "end": **********.147657, "relative_end": **********.147657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.148081, "relative_start": 1.0451641082763672, "end": **********.148081, "relative_end": **********.148081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.148507, "relative_start": 1.0455901622772217, "end": **********.148507, "relative_end": **********.148507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.149494, "relative_start": 1.046576976776123, "end": **********.149494, "relative_end": **********.149494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.149881, "relative_start": 1.0469639301300049, "end": **********.149881, "relative_end": **********.149881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.150765, "relative_start": 1.0478479862213135, "end": **********.150765, "relative_end": **********.150765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.152156, "relative_start": 1.049239158630371, "end": **********.152156, "relative_end": **********.152156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.152794, "relative_start": 1.0498769283294678, "end": **********.152794, "relative_end": **********.152794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.153604, "relative_start": 1.0506870746612549, "end": **********.153604, "relative_end": **********.153604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.154961, "relative_start": 1.052044153213501, "end": **********.154961, "relative_end": **********.154961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.155467, "relative_start": 1.0525500774383545, "end": **********.155467, "relative_end": **********.155467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.155918, "relative_start": 1.0530009269714355, "end": **********.155918, "relative_end": **********.155918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.156433, "relative_start": 1.053516149520874, "end": **********.156433, "relative_end": **********.156433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.156924, "relative_start": 1.0540070533752441, "end": **********.156924, "relative_end": **********.156924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.158956, "relative_start": 1.0560390949249268, "end": **********.158956, "relative_end": **********.158956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.160218, "relative_start": 1.0573010444641113, "end": **********.160218, "relative_end": **********.160218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.161781, "relative_start": 1.0588641166687012, "end": **********.161781, "relative_end": **********.161781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.162358, "relative_start": 1.059441089630127, "end": **********.162358, "relative_end": **********.162358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.164174, "relative_start": 1.0612571239471436, "end": **********.164174, "relative_end": **********.164174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.165004, "relative_start": 1.062087059020996, "end": **********.165004, "relative_end": **********.165004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.165525, "relative_start": 1.062608003616333, "end": **********.165525, "relative_end": **********.165525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.166013, "relative_start": 1.063096046447754, "end": **********.166013, "relative_end": **********.166013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.166446, "relative_start": 1.0635290145874023, "end": **********.166446, "relative_end": **********.166446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.166837, "relative_start": 1.063920021057129, "end": **********.166837, "relative_end": **********.166837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.167199, "relative_start": 1.064281940460205, "end": **********.167199, "relative_end": **********.167199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.167678, "relative_start": 1.0647611618041992, "end": **********.167678, "relative_end": **********.167678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.16819, "relative_start": 1.0652730464935303, "end": **********.16819, "relative_end": **********.16819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.168732, "relative_start": 1.0658149719238281, "end": **********.168732, "relative_end": **********.168732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.169275, "relative_start": 1.0663580894470215, "end": **********.169275, "relative_end": **********.169275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.170673, "relative_start": 1.067755937576294, "end": **********.170673, "relative_end": **********.170673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.171253, "relative_start": 1.068336009979248, "end": **********.171253, "relative_end": **********.171253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.171753, "relative_start": 1.068835973739624, "end": **********.171753, "relative_end": **********.171753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.172053, "relative_start": 1.069136142730713, "end": **********.172053, "relative_end": **********.172053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.173292, "relative_start": 1.0703749656677246, "end": **********.173292, "relative_end": **********.173292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.173803, "relative_start": 1.0708861351013184, "end": **********.173803, "relative_end": **********.173803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.174673, "relative_start": 1.07175612449646, "end": **********.174673, "relative_end": **********.174673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.175272, "relative_start": 1.072355031967163, "end": **********.175272, "relative_end": **********.175272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.175795, "relative_start": 1.072878122329712, "end": **********.175795, "relative_end": **********.175795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.176781, "relative_start": 1.0738639831542969, "end": **********.176781, "relative_end": **********.176781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.179178, "relative_start": 1.076261043548584, "end": **********.179178, "relative_end": **********.179178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.179748, "relative_start": 1.0768311023712158, "end": **********.179748, "relative_end": **********.179748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.180252, "relative_start": 1.0773351192474365, "end": **********.180252, "relative_end": **********.180252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.180799, "relative_start": 1.0778820514678955, "end": **********.180799, "relative_end": **********.180799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.181355, "relative_start": 1.0784380435943604, "end": **********.181355, "relative_end": **********.181355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.18277, "relative_start": 1.0798530578613281, "end": **********.18277, "relative_end": **********.18277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.184099, "relative_start": 1.0811820030212402, "end": **********.184099, "relative_end": **********.184099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.184759, "relative_start": 1.0818419456481934, "end": **********.184759, "relative_end": **********.184759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.185468, "relative_start": 1.0825510025024414, "end": **********.185468, "relative_end": **********.185468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.186482, "relative_start": 1.0835649967193604, "end": **********.186482, "relative_end": **********.186482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.187601, "relative_start": 1.084684133529663, "end": **********.187601, "relative_end": **********.187601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.188223, "relative_start": 1.08530592918396, "end": **********.188223, "relative_end": **********.188223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.188789, "relative_start": 1.085871934890747, "end": **********.188789, "relative_end": **********.188789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.189208, "relative_start": 1.0862910747528076, "end": **********.189208, "relative_end": **********.189208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.189529, "relative_start": 1.0866119861602783, "end": **********.189529, "relative_end": **********.189529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.190984, "relative_start": 1.0880670547485352, "end": **********.190984, "relative_end": **********.190984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.191534, "relative_start": 1.0886170864105225, "end": **********.191534, "relative_end": **********.191534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.192027, "relative_start": 1.0891101360321045, "end": **********.192027, "relative_end": **********.192027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.192364, "relative_start": 1.089447021484375, "end": **********.192364, "relative_end": **********.192364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.194276, "relative_start": 1.0913591384887695, "end": **********.194276, "relative_end": **********.194276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.195067, "relative_start": 1.0921499729156494, "end": **********.195067, "relative_end": **********.195067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.195555, "relative_start": 1.0926380157470703, "end": **********.195555, "relative_end": **********.195555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.196057, "relative_start": 1.0931401252746582, "end": **********.196057, "relative_end": **********.196057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.19653, "relative_start": 1.0936131477355957, "end": **********.19653, "relative_end": **********.19653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.197274, "relative_start": 1.0943570137023926, "end": **********.197274, "relative_end": **********.197274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.198454, "relative_start": 1.0955369472503662, "end": **********.198454, "relative_end": **********.198454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.198961, "relative_start": 1.0960440635681152, "end": **********.198961, "relative_end": **********.198961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.199409, "relative_start": 1.096492052078247, "end": **********.199409, "relative_end": **********.199409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.199945, "relative_start": 1.0970280170440674, "end": **********.199945, "relative_end": **********.199945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.200456, "relative_start": 1.097538948059082, "end": **********.200456, "relative_end": **********.200456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.200956, "relative_start": 1.098039150238037, "end": **********.200956, "relative_end": **********.200956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.201604, "relative_start": 1.098686933517456, "end": **********.201604, "relative_end": **********.201604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.202154, "relative_start": 1.0992369651794434, "end": **********.202154, "relative_end": **********.202154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.202623, "relative_start": 1.0997059345245361, "end": **********.202623, "relative_end": **********.202623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.203039, "relative_start": 1.1001219749450684, "end": **********.203039, "relative_end": **********.203039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.203907, "relative_start": 1.1009900569915771, "end": **********.203907, "relative_end": **********.203907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.204264, "relative_start": 1.1013469696044922, "end": **********.204264, "relative_end": **********.204264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.204675, "relative_start": 1.1017580032348633, "end": **********.204675, "relative_end": **********.204675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.205779, "relative_start": 1.1028621196746826, "end": **********.205779, "relative_end": **********.205779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.206213, "relative_start": 1.1032960414886475, "end": **********.206213, "relative_end": **********.206213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.206647, "relative_start": 1.1037299633026123, "end": **********.206647, "relative_end": **********.206647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.20781, "relative_start": 1.1048929691314697, "end": **********.20781, "relative_end": **********.20781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.208306, "relative_start": 1.10538911819458, "end": **********.208306, "relative_end": **********.208306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.208894, "relative_start": 1.1059770584106445, "end": **********.208894, "relative_end": **********.208894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.209645, "relative_start": 1.1067280769348145, "end": **********.209645, "relative_end": **********.209645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.21022, "relative_start": 1.1073031425476074, "end": **********.21022, "relative_end": **********.21022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.212831, "relative_start": 1.1099140644073486, "end": **********.212831, "relative_end": **********.212831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.214092, "relative_start": 1.1111750602722168, "end": **********.214092, "relative_end": **********.214092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.214622, "relative_start": 1.1117050647735596, "end": **********.214622, "relative_end": **********.214622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.215063, "relative_start": 1.1121461391448975, "end": **********.215063, "relative_end": **********.215063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.216849, "relative_start": 1.1139321327209473, "end": **********.216849, "relative_end": **********.216849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.218042, "relative_start": 1.1151249408721924, "end": **********.218042, "relative_end": **********.218042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.218636, "relative_start": 1.1157190799713135, "end": **********.218636, "relative_end": **********.218636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.219113, "relative_start": 1.1161961555480957, "end": **********.219113, "relative_end": **********.219113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.21954, "relative_start": 1.1166231632232666, "end": **********.21954, "relative_end": **********.21954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.219953, "relative_start": 1.1170361042022705, "end": **********.219953, "relative_end": **********.219953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.220438, "relative_start": 1.117521047592163, "end": **********.220438, "relative_end": **********.220438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.220999, "relative_start": 1.118082046508789, "end": **********.220999, "relative_end": **********.220999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.221545, "relative_start": 1.1186280250549316, "end": **********.221545, "relative_end": **********.221545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.222018, "relative_start": 1.1191010475158691, "end": **********.222018, "relative_end": **********.222018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.222372, "relative_start": 1.119455099105835, "end": **********.222372, "relative_end": **********.222372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.223568, "relative_start": 1.1206510066986084, "end": **********.223568, "relative_end": **********.223568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.224132, "relative_start": 1.1212151050567627, "end": **********.224132, "relative_end": **********.224132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.224748, "relative_start": 1.121830940246582, "end": **********.224748, "relative_end": **********.224748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.225095, "relative_start": 1.122178077697754, "end": **********.225095, "relative_end": **********.225095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.226661, "relative_start": 1.123744010925293, "end": **********.226661, "relative_end": **********.226661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.227408, "relative_start": 1.1244909763336182, "end": **********.227408, "relative_end": **********.227408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.228392, "relative_start": 1.1254749298095703, "end": **********.228392, "relative_end": **********.228392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.228984, "relative_start": 1.1260671615600586, "end": **********.228984, "relative_end": **********.228984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.229512, "relative_start": 1.1265950202941895, "end": **********.229512, "relative_end": **********.229512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.230274, "relative_start": 1.127357006072998, "end": **********.230274, "relative_end": **********.230274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.231403, "relative_start": 1.128486156463623, "end": **********.231403, "relative_end": **********.231403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.231846, "relative_start": 1.1289291381835938, "end": **********.231846, "relative_end": **********.231846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.232281, "relative_start": 1.129364013671875, "end": **********.232281, "relative_end": **********.232281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.232875, "relative_start": 1.129958152770996, "end": **********.232875, "relative_end": **********.232875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.233495, "relative_start": 1.1305780410766602, "end": **********.233495, "relative_end": **********.233495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.234854, "relative_start": 1.131937026977539, "end": **********.234854, "relative_end": **********.234854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.236401, "relative_start": 1.133484125137329, "end": **********.236401, "relative_end": **********.236401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.236954, "relative_start": 1.1340370178222656, "end": **********.236954, "relative_end": **********.236954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.237397, "relative_start": 1.1344799995422363, "end": **********.237397, "relative_end": **********.237397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.2379, "relative_start": 1.1349830627441406, "end": **********.2379, "relative_end": **********.2379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.238371, "relative_start": 1.1354539394378662, "end": **********.238371, "relative_end": **********.238371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.238792, "relative_start": 1.1358749866485596, "end": **********.238792, "relative_end": **********.238792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.239255, "relative_start": 1.1363379955291748, "end": **********.239255, "relative_end": **********.239255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.239658, "relative_start": 1.1367411613464355, "end": **********.239658, "relative_end": **********.239658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.239975, "relative_start": 1.1370580196380615, "end": **********.239975, "relative_end": **********.239975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.241053, "relative_start": 1.1381361484527588, "end": **********.241053, "relative_end": **********.241053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.241557, "relative_start": 1.1386399269104004, "end": **********.241557, "relative_end": **********.241557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.242055, "relative_start": 1.1391379833221436, "end": **********.242055, "relative_end": **********.242055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.242349, "relative_start": 1.1394319534301758, "end": **********.242349, "relative_end": **********.242349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.243517, "relative_start": 1.1405999660491943, "end": **********.243517, "relative_end": **********.243517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.244427, "relative_start": 1.141510009765625, "end": **********.244427, "relative_end": **********.244427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.24506, "relative_start": 1.1421430110931396, "end": **********.24506, "relative_end": **********.24506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.24563, "relative_start": 1.1427130699157715, "end": **********.24563, "relative_end": **********.24563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.246093, "relative_start": 1.1431760787963867, "end": **********.246093, "relative_end": **********.246093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.246932, "relative_start": 1.1440150737762451, "end": **********.246932, "relative_end": **********.246932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.248167, "relative_start": 1.1452500820159912, "end": **********.248167, "relative_end": **********.248167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.248648, "relative_start": 1.145730972290039, "end": **********.248648, "relative_end": **********.248648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.24907, "relative_start": 1.1461529731750488, "end": **********.24907, "relative_end": **********.24907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.249573, "relative_start": 1.1466560363769531, "end": **********.249573, "relative_end": **********.249573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.250056, "relative_start": 1.147139072418213, "end": **********.250056, "relative_end": **********.250056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.250335, "relative_start": 1.1474180221557617, "end": **********.250335, "relative_end": **********.250335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.251177, "relative_start": 1.1482601165771484, "end": **********.251177, "relative_end": **********.251177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.251772, "relative_start": 1.1488549709320068, "end": **********.251772, "relative_end": **********.251772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.252203, "relative_start": 1.1492860317230225, "end": **********.252203, "relative_end": **********.252203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.252632, "relative_start": 1.1497149467468262, "end": **********.252632, "relative_end": **********.252632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.253565, "relative_start": 1.1506481170654297, "end": **********.253565, "relative_end": **********.253565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.253943, "relative_start": 1.1510260105133057, "end": **********.253943, "relative_end": **********.253943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.254387, "relative_start": 1.1514699459075928, "end": **********.254387, "relative_end": **********.254387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.255234, "relative_start": 1.1523170471191406, "end": **********.255234, "relative_end": **********.255234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.255614, "relative_start": 1.1526970863342285, "end": **********.255614, "relative_end": **********.255614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.256062, "relative_start": 1.1531450748443604, "end": **********.256062, "relative_end": **********.256062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.257471, "relative_start": 1.1545541286468506, "end": **********.257471, "relative_end": **********.257471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.258511, "relative_start": 1.1555941104888916, "end": **********.258511, "relative_end": **********.258511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.259034, "relative_start": 1.1561169624328613, "end": **********.259034, "relative_end": **********.259034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.25958, "relative_start": 1.156662940979004, "end": **********.25958, "relative_end": **********.25958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.260052, "relative_start": 1.157135009765625, "end": **********.260052, "relative_end": **********.260052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.26203, "relative_start": 1.1591129302978516, "end": **********.26203, "relative_end": **********.26203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.263177, "relative_start": 1.1602599620819092, "end": **********.263177, "relative_end": **********.263177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.263691, "relative_start": 1.1607739925384521, "end": **********.263691, "relative_end": **********.263691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.264097, "relative_start": 1.161180019378662, "end": **********.264097, "relative_end": **********.264097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.265787, "relative_start": 1.162869930267334, "end": **********.265787, "relative_end": **********.265787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.266625, "relative_start": 1.163707971572876, "end": **********.266625, "relative_end": **********.266625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.267141, "relative_start": 1.1642241477966309, "end": **********.267141, "relative_end": **********.267141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.267584, "relative_start": 1.1646671295166016, "end": **********.267584, "relative_end": **********.267584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.268031, "relative_start": 1.165113925933838, "end": **********.268031, "relative_end": **********.268031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.268403, "relative_start": 1.1654860973358154, "end": **********.268403, "relative_end": **********.268403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.268758, "relative_start": 1.1658411026000977, "end": **********.268758, "relative_end": **********.268758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.269225, "relative_start": 1.1663079261779785, "end": **********.269225, "relative_end": **********.269225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.269709, "relative_start": 1.1667921543121338, "end": **********.269709, "relative_end": **********.269709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.270163, "relative_start": 1.1672461032867432, "end": **********.270163, "relative_end": **********.270163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.270492, "relative_start": 1.1675751209259033, "end": **********.270492, "relative_end": **********.270492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.271593, "relative_start": 1.1686761379241943, "end": **********.271593, "relative_end": **********.271593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.272122, "relative_start": 1.1692049503326416, "end": **********.272122, "relative_end": **********.272122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.272644, "relative_start": 1.169727087020874, "end": **********.272644, "relative_end": **********.272644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.272959, "relative_start": 1.1700420379638672, "end": **********.272959, "relative_end": **********.272959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.274389, "relative_start": 1.1714720726013184, "end": **********.274389, "relative_end": **********.274389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.274922, "relative_start": 1.1720049381256104, "end": **********.274922, "relative_end": **********.274922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.275385, "relative_start": 1.1724679470062256, "end": **********.275385, "relative_end": **********.275385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.275911, "relative_start": 1.1729941368103027, "end": **********.275911, "relative_end": **********.275911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.276387, "relative_start": 1.1734700202941895, "end": **********.276387, "relative_end": **********.276387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.27735, "relative_start": 1.1744329929351807, "end": **********.27735, "relative_end": **********.27735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.279018, "relative_start": 1.1761009693145752, "end": **********.279018, "relative_end": **********.279018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.279539, "relative_start": 1.1766221523284912, "end": **********.279539, "relative_end": **********.279539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.27997, "relative_start": 1.1770529747009277, "end": **********.27997, "relative_end": **********.27997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.280454, "relative_start": 1.177536964416504, "end": **********.280454, "relative_end": **********.280454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.280925, "relative_start": 1.1780080795288086, "end": **********.280925, "relative_end": **********.280925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.282346, "relative_start": 1.179429054260254, "end": **********.282346, "relative_end": **********.282346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.283597, "relative_start": 1.1806800365447998, "end": **********.283597, "relative_end": **********.283597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.284061, "relative_start": 1.1811439990997314, "end": **********.284061, "relative_end": **********.284061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.284505, "relative_start": 1.1815879344940186, "end": **********.284505, "relative_end": **********.284505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.284997, "relative_start": 1.1820800304412842, "end": **********.284997, "relative_end": **********.284997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.285471, "relative_start": 1.182554006576538, "end": **********.285471, "relative_end": **********.285471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.285888, "relative_start": 1.1829710006713867, "end": **********.285888, "relative_end": **********.285888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.286362, "relative_start": 1.1834449768066406, "end": **********.286362, "relative_end": **********.286362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.286787, "relative_start": 1.1838700771331787, "end": **********.286787, "relative_end": **********.286787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.287108, "relative_start": 1.1841909885406494, "end": **********.287108, "relative_end": **********.287108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.288229, "relative_start": 1.185312032699585, "end": **********.288229, "relative_end": **********.288229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.288866, "relative_start": 1.1859490871429443, "end": **********.288866, "relative_end": **********.288866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.289431, "relative_start": 1.186514139175415, "end": **********.289431, "relative_end": **********.289431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.289821, "relative_start": 1.186903953552246, "end": **********.289821, "relative_end": **********.289821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.291032, "relative_start": 1.188115119934082, "end": **********.291032, "relative_end": **********.291032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.291486, "relative_start": 1.1885690689086914, "end": **********.291486, "relative_end": **********.291486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.291918, "relative_start": 1.1890010833740234, "end": **********.291918, "relative_end": **********.291918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.292393, "relative_start": 1.1894760131835938, "end": **********.292393, "relative_end": **********.292393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.292838, "relative_start": 1.1899211406707764, "end": **********.292838, "relative_end": **********.292838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.293532, "relative_start": 1.190614938735962, "end": **********.293532, "relative_end": **********.293532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.295159, "relative_start": 1.19224214553833, "end": **********.295159, "relative_end": **********.295159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.295693, "relative_start": 1.1927759647369385, "end": **********.295693, "relative_end": **********.295693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.296145, "relative_start": 1.193228006362915, "end": **********.296145, "relative_end": **********.296145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.297009, "relative_start": 1.194092035293579, "end": **********.297009, "relative_end": **********.297009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.297556, "relative_start": 1.194638967514038, "end": **********.297556, "relative_end": **********.297556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.297861, "relative_start": 1.194944143295288, "end": **********.297861, "relative_end": **********.297861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.298272, "relative_start": 1.19535493850708, "end": **********.298272, "relative_end": **********.298272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.298751, "relative_start": 1.1958341598510742, "end": **********.298751, "relative_end": **********.298751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.299157, "relative_start": 1.196239948272705, "end": **********.299157, "relative_end": **********.299157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.299581, "relative_start": 1.1966640949249268, "end": **********.299581, "relative_end": **********.299581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.300459, "relative_start": 1.1975419521331787, "end": **********.300459, "relative_end": **********.300459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.300831, "relative_start": 1.1979141235351562, "end": **********.300831, "relative_end": **********.300831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.301266, "relative_start": 1.1983489990234375, "end": **********.301266, "relative_end": **********.301266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.302104, "relative_start": 1.1991870403289795, "end": **********.302104, "relative_end": **********.302104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.302474, "relative_start": 1.1995570659637451, "end": **********.302474, "relative_end": **********.302474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.302929, "relative_start": 1.200011968612671, "end": **********.302929, "relative_end": **********.302929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.304068, "relative_start": 1.2011511325836182, "end": **********.304068, "relative_end": **********.304068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.30463, "relative_start": 1.2017130851745605, "end": **********.30463, "relative_end": **********.30463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.305102, "relative_start": 1.2021851539611816, "end": **********.305102, "relative_end": **********.305102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.305611, "relative_start": 1.2026939392089844, "end": **********.305611, "relative_end": **********.305611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.306108, "relative_start": 1.2031910419464111, "end": **********.306108, "relative_end": **********.306108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.307603, "relative_start": 1.204685926437378, "end": **********.307603, "relative_end": **********.307603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.308805, "relative_start": 1.205888032913208, "end": **********.308805, "relative_end": **********.308805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.309322, "relative_start": 1.2064051628112793, "end": **********.309322, "relative_end": **********.309322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.30975, "relative_start": 1.2068331241607666, "end": **********.30975, "relative_end": **********.30975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.317347, "relative_start": 1.2144300937652588, "end": **********.317347, "relative_end": **********.317347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.31844, "relative_start": 1.2155230045318604, "end": **********.31844, "relative_end": **********.31844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.318994, "relative_start": 1.2160770893096924, "end": **********.318994, "relative_end": **********.318994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.319458, "relative_start": 1.216541051864624, "end": **********.319458, "relative_end": **********.319458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.319909, "relative_start": 1.2169921398162842, "end": **********.319909, "relative_end": **********.319909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.320291, "relative_start": 1.2173740863800049, "end": **********.320291, "relative_end": **********.320291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.320728, "relative_start": 1.217811107635498, "end": **********.320728, "relative_end": **********.320728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.32132, "relative_start": 1.2184031009674072, "end": **********.32132, "relative_end": **********.32132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.321984, "relative_start": 1.219067096710205, "end": **********.321984, "relative_end": **********.321984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.322456, "relative_start": 1.219538927078247, "end": **********.322456, "relative_end": **********.322456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.322789, "relative_start": 1.219871997833252, "end": **********.322789, "relative_end": **********.322789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.323939, "relative_start": 1.221022129058838, "end": **********.323939, "relative_end": **********.323939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.324476, "relative_start": 1.2215590476989746, "end": **********.324476, "relative_end": **********.324476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.324991, "relative_start": 1.222074031829834, "end": **********.324991, "relative_end": **********.324991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.32529, "relative_start": 1.2223730087280273, "end": **********.32529, "relative_end": **********.32529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.326566, "relative_start": 1.223649024963379, "end": **********.326566, "relative_end": **********.326566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.327092, "relative_start": 1.224174976348877, "end": **********.327092, "relative_end": **********.327092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.328391, "relative_start": 1.2254741191864014, "end": **********.328391, "relative_end": **********.328391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.329392, "relative_start": 1.2264750003814697, "end": **********.329392, "relative_end": **********.329392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.330418, "relative_start": 1.2275011539459229, "end": **********.330418, "relative_end": **********.330418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.331549, "relative_start": 1.2286319732666016, "end": **********.331549, "relative_end": **********.331549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.332797, "relative_start": 1.2298800945281982, "end": **********.332797, "relative_end": **********.332797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.333285, "relative_start": 1.2303681373596191, "end": **********.333285, "relative_end": **********.333285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.333745, "relative_start": 1.230828046798706, "end": **********.333745, "relative_end": **********.333745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.334267, "relative_start": 1.2313499450683594, "end": **********.334267, "relative_end": **********.334267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.33477, "relative_start": 1.2318530082702637, "end": **********.33477, "relative_end": **********.33477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.336135, "relative_start": 1.2332179546356201, "end": **********.336135, "relative_end": **********.336135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.337629, "relative_start": 1.2347121238708496, "end": **********.337629, "relative_end": **********.337629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.338241, "relative_start": 1.2353241443634033, "end": **********.338241, "relative_end": **********.338241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.338736, "relative_start": 1.2358191013336182, "end": **********.338736, "relative_end": **********.338736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.339264, "relative_start": 1.236346960067749, "end": **********.339264, "relative_end": **********.339264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.339778, "relative_start": 1.236860990524292, "end": **********.339778, "relative_end": **********.339778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.34023, "relative_start": 1.2373130321502686, "end": **********.34023, "relative_end": **********.34023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.340723, "relative_start": 1.2378060817718506, "end": **********.340723, "relative_end": **********.340723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.341152, "relative_start": 1.2382349967956543, "end": **********.341152, "relative_end": **********.341152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.341481, "relative_start": 1.2385640144348145, "end": **********.341481, "relative_end": **********.341481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.342625, "relative_start": 1.2397079467773438, "end": **********.342625, "relative_end": **********.342625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.343363, "relative_start": 1.2404460906982422, "end": **********.343363, "relative_end": **********.343363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.344168, "relative_start": 1.241250991821289, "end": **********.344168, "relative_end": **********.344168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.344683, "relative_start": 1.2417659759521484, "end": **********.344683, "relative_end": **********.344683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.346118, "relative_start": 1.2432010173797607, "end": **********.346118, "relative_end": **********.346118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.346694, "relative_start": 1.2437770366668701, "end": **********.346694, "relative_end": **********.346694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.347175, "relative_start": 1.244257926940918, "end": **********.347175, "relative_end": **********.347175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.347718, "relative_start": 1.2448010444641113, "end": **********.347718, "relative_end": **********.347718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.348231, "relative_start": 1.245314121246338, "end": **********.348231, "relative_end": **********.348231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.349031, "relative_start": 1.2461140155792236, "end": **********.349031, "relative_end": **********.349031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.35027, "relative_start": 1.2473530769348145, "end": **********.35027, "relative_end": **********.35027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.350766, "relative_start": 1.2478489875793457, "end": **********.350766, "relative_end": **********.350766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.351245, "relative_start": 1.2483279705047607, "end": **********.351245, "relative_end": **********.351245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.351771, "relative_start": 1.248854160308838, "end": **********.351771, "relative_end": **********.351771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.352282, "relative_start": 1.2493650913238525, "end": **********.352282, "relative_end": **********.352282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.352673, "relative_start": 1.249756097793579, "end": **********.352673, "relative_end": **********.352673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.353202, "relative_start": 1.2502851486206055, "end": **********.353202, "relative_end": **********.353202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.353885, "relative_start": 1.2509679794311523, "end": **********.353885, "relative_end": **********.353885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.35439, "relative_start": 1.2514729499816895, "end": **********.35439, "relative_end": **********.35439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.354834, "relative_start": 1.2519171237945557, "end": **********.354834, "relative_end": **********.354834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.355787, "relative_start": 1.2528700828552246, "end": **********.355787, "relative_end": **********.355787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.356181, "relative_start": 1.2532639503479004, "end": **********.356181, "relative_end": **********.356181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.356619, "relative_start": 1.25370192527771, "end": **********.356619, "relative_end": **********.356619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.357499, "relative_start": 1.2545819282531738, "end": **********.357499, "relative_end": **********.357499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.357882, "relative_start": 1.25496506690979, "end": **********.357882, "relative_end": **********.357882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.358338, "relative_start": 1.2554211616516113, "end": **********.358338, "relative_end": **********.358338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.360011, "relative_start": 1.257094144821167, "end": **********.360011, "relative_end": **********.360011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.361642, "relative_start": 1.2587249279022217, "end": **********.361642, "relative_end": **********.361642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.362387, "relative_start": 1.259469985961914, "end": **********.362387, "relative_end": **********.362387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.36293, "relative_start": 1.2600131034851074, "end": **********.36293, "relative_end": **********.36293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.363448, "relative_start": 1.260530948638916, "end": **********.363448, "relative_end": **********.363448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.364987, "relative_start": 1.2620699405670166, "end": **********.364987, "relative_end": **********.364987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.366194, "relative_start": 1.2632770538330078, "end": **********.366194, "relative_end": **********.366194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.366736, "relative_start": 1.2638189792633057, "end": **********.366736, "relative_end": **********.366736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.367179, "relative_start": 1.2642619609832764, "end": **********.367179, "relative_end": **********.367179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.368872, "relative_start": 1.2659549713134766, "end": **********.368872, "relative_end": **********.368872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.369996, "relative_start": 1.2670791149139404, "end": **********.369996, "relative_end": **********.369996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.37058, "relative_start": 1.2676630020141602, "end": **********.37058, "relative_end": **********.37058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.371064, "relative_start": 1.2681469917297363, "end": **********.371064, "relative_end": **********.371064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.371521, "relative_start": 1.268604040145874, "end": **********.371521, "relative_end": **********.371521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.371907, "relative_start": 1.2689900398254395, "end": **********.371907, "relative_end": **********.371907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.372264, "relative_start": 1.2693469524383545, "end": **********.372264, "relative_end": **********.372264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.372749, "relative_start": 1.2698321342468262, "end": **********.372749, "relative_end": **********.372749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.373253, "relative_start": 1.2703361511230469, "end": **********.373253, "relative_end": **********.373253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.373725, "relative_start": 1.2708079814910889, "end": **********.373725, "relative_end": **********.373725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.374052, "relative_start": 1.2711350917816162, "end": **********.374052, "relative_end": **********.374052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.375343, "relative_start": 1.2724261283874512, "end": **********.375343, "relative_end": **********.375343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.375952, "relative_start": 1.2730350494384766, "end": **********.375952, "relative_end": **********.375952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.376539, "relative_start": 1.2736220359802246, "end": **********.376539, "relative_end": **********.376539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.376927, "relative_start": 1.2740099430084229, "end": **********.376927, "relative_end": **********.376927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.379126, "relative_start": 1.2762091159820557, "end": **********.379126, "relative_end": **********.379126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.379698, "relative_start": 1.2767810821533203, "end": **********.379698, "relative_end": **********.379698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.380165, "relative_start": 1.2772481441497803, "end": **********.380165, "relative_end": **********.380165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.380677, "relative_start": 1.2777600288391113, "end": **********.380677, "relative_end": **********.380677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.381188, "relative_start": 1.278270959854126, "end": **********.381188, "relative_end": **********.381188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.381937, "relative_start": 1.279020071029663, "end": **********.381937, "relative_end": **********.381937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.383199, "relative_start": 1.2802820205688477, "end": **********.383199, "relative_end": **********.383199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.383686, "relative_start": 1.2807691097259521, "end": **********.383686, "relative_end": **********.383686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.384148, "relative_start": 1.2812309265136719, "end": **********.384148, "relative_end": **********.384148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.384832, "relative_start": 1.2819149494171143, "end": **********.384832, "relative_end": **********.384832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.385521, "relative_start": 1.2826039791107178, "end": **********.385521, "relative_end": **********.385521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.387037, "relative_start": 1.2841200828552246, "end": **********.387037, "relative_end": **********.387037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.388356, "relative_start": 1.2854390144348145, "end": **********.388356, "relative_end": **********.388356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.388868, "relative_start": 1.2859511375427246, "end": **********.388868, "relative_end": **********.388868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.389333, "relative_start": 1.2864160537719727, "end": **********.389333, "relative_end": **********.389333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.38985, "relative_start": 1.2869329452514648, "end": **********.38985, "relative_end": **********.38985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.390648, "relative_start": 1.2877309322357178, "end": **********.390648, "relative_end": **********.390648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.391207, "relative_start": 1.288290023803711, "end": **********.391207, "relative_end": **********.391207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.391733, "relative_start": 1.288815975189209, "end": **********.391733, "relative_end": **********.391733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.392139, "relative_start": 1.289222002029419, "end": **********.392139, "relative_end": **********.392139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.392486, "relative_start": 1.2895691394805908, "end": **********.392486, "relative_end": **********.392486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.394401, "relative_start": 1.2914841175079346, "end": **********.394401, "relative_end": **********.394401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.395324, "relative_start": 1.2924070358276367, "end": **********.395324, "relative_end": **********.395324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.39613, "relative_start": 1.293213129043579, "end": **********.39613, "relative_end": **********.39613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.396573, "relative_start": 1.2936561107635498, "end": **********.396573, "relative_end": **********.396573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.398236, "relative_start": 1.2953190803527832, "end": **********.398236, "relative_end": **********.398236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.398752, "relative_start": 1.295835018157959, "end": **********.398752, "relative_end": **********.398752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.399217, "relative_start": 1.296299934387207, "end": **********.399217, "relative_end": **********.399217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.39971, "relative_start": 1.296792984008789, "end": **********.39971, "relative_end": **********.39971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.400765, "relative_start": 1.2978479862213135, "end": **********.400765, "relative_end": **********.400765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.40232, "relative_start": 1.2994029521942139, "end": **********.40232, "relative_end": **********.40232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.404024, "relative_start": 1.3011069297790527, "end": **********.404024, "relative_end": **********.404024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.404617, "relative_start": 1.3017001152038574, "end": **********.404617, "relative_end": **********.404617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.405277, "relative_start": 1.3023600578308105, "end": **********.405277, "relative_end": **********.405277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.406172, "relative_start": 1.3032550811767578, "end": **********.406172, "relative_end": **********.406172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.406779, "relative_start": 1.3038620948791504, "end": **********.406779, "relative_end": **********.406779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.407116, "relative_start": 1.304198980331421, "end": **********.407116, "relative_end": **********.407116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.407706, "relative_start": 1.3047890663146973, "end": **********.407706, "relative_end": **********.407706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.408208, "relative_start": 1.305290937423706, "end": **********.408208, "relative_end": **********.408208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.408775, "relative_start": 1.3058581352233887, "end": **********.408775, "relative_end": **********.408775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.409432, "relative_start": 1.3065149784088135, "end": **********.409432, "relative_end": **********.409432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.410396, "relative_start": 1.3074791431427002, "end": **********.410396, "relative_end": **********.410396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.41106, "relative_start": 1.308143138885498, "end": **********.41106, "relative_end": **********.41106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.411554, "relative_start": 1.3086371421813965, "end": **********.411554, "relative_end": **********.411554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.412437, "relative_start": 1.3095200061798096, "end": **********.412437, "relative_end": **********.412437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.412809, "relative_start": 1.309891939163208, "end": **********.412809, "relative_end": **********.412809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.41322, "relative_start": 1.310302972793579, "end": **********.41322, "relative_end": **********.41322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.414328, "relative_start": 1.3114111423492432, "end": **********.414328, "relative_end": **********.414328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.414805, "relative_start": 1.3118879795074463, "end": **********.414805, "relative_end": **********.414805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.415255, "relative_start": 1.31233811378479, "end": **********.415255, "relative_end": **********.415255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.415833, "relative_start": 1.3129160404205322, "end": **********.415833, "relative_end": **********.415833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.416374, "relative_start": 1.3134570121765137, "end": **********.416374, "relative_end": **********.416374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.418654, "relative_start": 1.315737009048462, "end": **********.418654, "relative_end": **********.418654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.420446, "relative_start": 1.3175289630889893, "end": **********.420446, "relative_end": **********.420446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.421253, "relative_start": 1.318336009979248, "end": **********.421253, "relative_end": **********.421253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.421972, "relative_start": 1.3190550804138184, "end": **********.421972, "relative_end": **********.421972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.423653, "relative_start": 1.3207359313964844, "end": **********.423653, "relative_end": **********.423653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.424484, "relative_start": 1.3215670585632324, "end": **********.424484, "relative_end": **********.424484, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.425154, "relative_start": 1.3222370147705078, "end": **********.425154, "relative_end": **********.425154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.425743, "relative_start": 1.3228261470794678, "end": **********.425743, "relative_end": **********.425743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.42629, "relative_start": 1.3233730792999268, "end": **********.42629, "relative_end": **********.42629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.426686, "relative_start": 1.3237690925598145, "end": **********.426686, "relative_end": **********.426686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.427047, "relative_start": 1.3241300582885742, "end": **********.427047, "relative_end": **********.427047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.428638, "relative_start": 1.325721025466919, "end": **********.428638, "relative_end": **********.428638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.429383, "relative_start": 1.3264660835266113, "end": **********.429383, "relative_end": **********.429383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.430009, "relative_start": 1.327091932296753, "end": **********.430009, "relative_end": **********.430009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.430538, "relative_start": 1.3276209831237793, "end": **********.430538, "relative_end": **********.430538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.431891, "relative_start": 1.3289740085601807, "end": **********.431891, "relative_end": **********.431891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.432915, "relative_start": 1.3299980163574219, "end": **********.432915, "relative_end": **********.432915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.43372, "relative_start": 1.3308031558990479, "end": **********.43372, "relative_end": **********.43372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.434309, "relative_start": 1.3313920497894287, "end": **********.434309, "relative_end": **********.434309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.436984, "relative_start": 1.3340671062469482, "end": **********.436984, "relative_end": **********.436984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.437864, "relative_start": 1.334947109222412, "end": **********.437864, "relative_end": **********.437864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.438424, "relative_start": 1.3355071544647217, "end": **********.438424, "relative_end": **********.438424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.439034, "relative_start": 1.3361170291900635, "end": **********.439034, "relative_end": **********.439034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.43969, "relative_start": 1.336773157119751, "end": **********.43969, "relative_end": **********.43969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.440648, "relative_start": 1.337731122970581, "end": **********.440648, "relative_end": **********.440648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.442147, "relative_start": 1.3392300605773926, "end": **********.442147, "relative_end": **********.442147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.44272, "relative_start": 1.3398029804229736, "end": **********.44272, "relative_end": **********.44272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.443183, "relative_start": 1.3402659893035889, "end": **********.443183, "relative_end": **********.443183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.443723, "relative_start": 1.340806007385254, "end": **********.443723, "relative_end": **********.443723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.444881, "relative_start": 1.3419640064239502, "end": **********.444881, "relative_end": **********.444881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.446563, "relative_start": 1.3436460494995117, "end": **********.446563, "relative_end": **********.446563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.448022, "relative_start": 1.3451049327850342, "end": **********.448022, "relative_end": **********.448022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.448553, "relative_start": 1.3456361293792725, "end": **********.448553, "relative_end": **********.448553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.44927, "relative_start": 1.3463530540466309, "end": **********.44927, "relative_end": **********.44927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.449973, "relative_start": 1.3470561504364014, "end": **********.449973, "relative_end": **********.449973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.450497, "relative_start": 1.3475799560546875, "end": **********.450497, "relative_end": **********.450497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.450931, "relative_start": 1.3480141162872314, "end": **********.450931, "relative_end": **********.450931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.451412, "relative_start": 1.3484950065612793, "end": **********.451412, "relative_end": **********.451412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.451844, "relative_start": 1.3489270210266113, "end": **********.451844, "relative_end": **********.451844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.452165, "relative_start": 1.349247932434082, "end": **********.452165, "relative_end": **********.452165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.454393, "relative_start": 1.3514759540557861, "end": **********.454393, "relative_end": **********.454393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.455288, "relative_start": 1.3523709774017334, "end": **********.455288, "relative_end": **********.455288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.456141, "relative_start": 1.3532240390777588, "end": **********.456141, "relative_end": **********.456141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.456621, "relative_start": 1.3537039756774902, "end": **********.456621, "relative_end": **********.456621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.458916, "relative_start": 1.3559989929199219, "end": **********.458916, "relative_end": **********.458916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.459728, "relative_start": 1.3568110466003418, "end": **********.459728, "relative_end": **********.459728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.460832, "relative_start": 1.3579151630401611, "end": **********.460832, "relative_end": **********.460832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.462057, "relative_start": 1.359140157699585, "end": **********.462057, "relative_end": **********.462057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.463195, "relative_start": 1.3602781295776367, "end": **********.463195, "relative_end": **********.463195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.46446, "relative_start": 1.3615429401397705, "end": **********.46446, "relative_end": **********.46446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.46605, "relative_start": 1.3631329536437988, "end": **********.46605, "relative_end": **********.46605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.466737, "relative_start": 1.3638200759887695, "end": **********.466737, "relative_end": **********.466737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.46723, "relative_start": 1.3643131256103516, "end": **********.46723, "relative_end": **********.46723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.468219, "relative_start": 1.3653020858764648, "end": **********.468219, "relative_end": **********.468219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.469331, "relative_start": 1.3664140701293945, "end": **********.469331, "relative_end": **********.469331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.469905, "relative_start": 1.366987943649292, "end": **********.469905, "relative_end": **********.469905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.470879, "relative_start": 1.367962121963501, "end": **********.470879, "relative_end": **********.470879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.472012, "relative_start": 1.3690950870513916, "end": **********.472012, "relative_end": **********.472012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.473097, "relative_start": 1.3701801300048828, "end": **********.473097, "relative_end": **********.473097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.473859, "relative_start": 1.3709421157836914, "end": **********.473859, "relative_end": **********.473859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.474899, "relative_start": 1.3719820976257324, "end": **********.474899, "relative_end": **********.474899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.475356, "relative_start": 1.3724391460418701, "end": **********.475356, "relative_end": **********.475356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "start": **********.475816, "relative_start": 1.372899055480957, "end": **********.475816, "relative_end": **********.475816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "start": **********.476743, "relative_start": 1.373826026916504, "end": **********.476743, "relative_end": **********.476743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.477459, "relative_start": 1.374541997909546, "end": **********.477459, "relative_end": **********.477459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.478214, "relative_start": 1.3752970695495605, "end": **********.478214, "relative_end": **********.478214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.479906, "relative_start": 1.3769891262054443, "end": **********.479906, "relative_end": **********.479906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": **********.480579, "relative_start": 1.377661943435669, "end": **********.480579, "relative_end": **********.480579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.481117, "relative_start": 1.3782000541687012, "end": **********.481117, "relative_end": **********.481117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.481658, "relative_start": 1.3787410259246826, "end": **********.481658, "relative_end": **********.481658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.482198, "relative_start": 1.3792810440063477, "end": **********.482198, "relative_end": **********.482198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.permission-form", "start": **********.483686, "relative_start": 1.3807690143585205, "end": **********.483686, "relative_end": **********.483686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.485318, "relative_start": 1.3824009895324707, "end": **********.485318, "relative_end": **********.485318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.485909, "relative_start": 1.3829920291900635, "end": **********.485909, "relative_end": **********.485909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.486346, "relative_start": 1.3834290504455566, "end": **********.486346, "relative_end": **********.486346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.487963, "relative_start": 1.3850460052490234, "end": **********.487963, "relative_end": **********.487963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.48905, "relative_start": 1.3861329555511475, "end": **********.48905, "relative_end": **********.48905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.489797, "relative_start": 1.3868801593780518, "end": **********.489797, "relative_end": **********.489797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.490322, "relative_start": 1.3874051570892334, "end": **********.490322, "relative_end": **********.490322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.490768, "relative_start": 1.3878509998321533, "end": **********.490768, "relative_end": **********.490768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.491148, "relative_start": 1.3882310390472412, "end": **********.491148, "relative_end": **********.491148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.49151, "relative_start": 1.3885929584503174, "end": **********.49151, "relative_end": **********.49151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.492014, "relative_start": 1.389096975326538, "end": **********.492014, "relative_end": **********.492014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.492518, "relative_start": 1.3896009922027588, "end": **********.492518, "relative_end": **********.492518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.492967, "relative_start": 1.390049934387207, "end": **********.492967, "relative_end": **********.492967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.493317, "relative_start": 1.3903999328613281, "end": **********.493317, "relative_end": **********.493317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.495394, "relative_start": 1.392477035522461, "end": **********.495394, "relative_end": **********.495394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.496372, "relative_start": 1.3934550285339355, "end": **********.496372, "relative_end": **********.496372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.497203, "relative_start": 1.3942861557006836, "end": **********.497203, "relative_end": **********.497203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.497631, "relative_start": 1.394714117050171, "end": **********.497631, "relative_end": **********.497631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.498892, "relative_start": 1.395975112915039, "end": **********.498892, "relative_end": **********.498892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.499387, "relative_start": 1.396470069885254, "end": **********.499387, "relative_end": **********.499387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.499834, "relative_start": 1.3969171047210693, "end": **********.499834, "relative_end": **********.499834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.500442, "relative_start": 1.3975250720977783, "end": **********.500442, "relative_end": **********.500442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.501162, "relative_start": 1.398245096206665, "end": **********.501162, "relative_end": **********.501162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.502436, "relative_start": 1.3995189666748047, "end": **********.502436, "relative_end": **********.502436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.504047, "relative_start": 1.401129961013794, "end": **********.504047, "relative_end": **********.504047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.504874, "relative_start": 1.4019570350646973, "end": **********.504874, "relative_end": **********.504874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.505578, "relative_start": 1.4026610851287842, "end": **********.505578, "relative_end": **********.505578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.506217, "relative_start": 1.4033000469207764, "end": **********.506217, "relative_end": **********.506217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.506757, "relative_start": 1.4038400650024414, "end": **********.506757, "relative_end": **********.506757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.508171, "relative_start": 1.4052541255950928, "end": **********.508171, "relative_end": **********.508171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.50945, "relative_start": 1.4065330028533936, "end": **********.50945, "relative_end": **********.50945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": **********.509963, "relative_start": 1.4070460796356201, "end": **********.509963, "relative_end": **********.509963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.51082, "relative_start": 1.4079029560089111, "end": **********.51082, "relative_end": **********.51082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.51192, "relative_start": 1.4090030193328857, "end": **********.51192, "relative_end": **********.51192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": **********.512761, "relative_start": 1.409844160079956, "end": **********.512761, "relative_end": **********.512761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.513758, "relative_start": 1.4108409881591797, "end": **********.513758, "relative_end": **********.513758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": **********.51457, "relative_start": 1.4116530418395996, "end": **********.51457, "relative_end": **********.51457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.515033, "relative_start": 1.4121160507202148, "end": **********.515033, "relative_end": **********.515033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.515472, "relative_start": 1.4125549793243408, "end": **********.515472, "relative_end": **********.515472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.516761, "relative_start": 1.413844108581543, "end": **********.516761, "relative_end": **********.516761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.517341, "relative_start": 1.414423942565918, "end": **********.517341, "relative_end": **********.517341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.517866, "relative_start": 1.4149489402770996, "end": **********.517866, "relative_end": **********.517866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.518172, "relative_start": 1.415255069732666, "end": **********.518172, "relative_end": **********.518172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.519414, "relative_start": 1.416496992111206, "end": **********.519414, "relative_end": **********.519414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.519922, "relative_start": 1.4170050621032715, "end": **********.519922, "relative_end": **********.519922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.520405, "relative_start": 1.4174880981445312, "end": **********.520405, "relative_end": **********.520405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.521121, "relative_start": 1.4182040691375732, "end": **********.521121, "relative_end": **********.521121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": **********.521749, "relative_start": 1.4188320636749268, "end": **********.521749, "relative_end": **********.521749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.522652, "relative_start": 1.4197349548339844, "end": **********.522652, "relative_end": **********.522652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.523919, "relative_start": 1.4210021495819092, "end": **********.523919, "relative_end": **********.523919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": **********.524435, "relative_start": 1.421518087387085, "end": **********.524435, "relative_end": **********.524435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.524918, "relative_start": 1.4220011234283447, "end": **********.524918, "relative_end": **********.524918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.525453, "relative_start": 1.4225361347198486, "end": **********.525453, "relative_end": **********.525453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": **********.525971, "relative_start": 1.4230539798736572, "end": **********.525971, "relative_end": **********.525971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": **********.526282, "relative_start": 1.4233651161193848, "end": **********.526282, "relative_end": **********.526282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": **********.526736, "relative_start": 1.4238190650939941, "end": **********.526736, "relative_end": **********.526736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.rows", "start": **********.527988, "relative_start": 1.4250710010528564, "end": **********.527988, "relative_end": **********.527988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.index", "start": **********.529381, "relative_start": 1.4264640808105469, "end": **********.529381, "relative_end": **********.529381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.531151, "relative_start": 1.****************, "end": **********.531151, "relative_end": **********.531151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.533332, "relative_start": 1.***************, "end": **********.533332, "relative_end": **********.533332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "start": **********.53421, "relative_start": 1.**************, "end": **********.53421, "relative_end": **********.53421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.536812, "relative_start": 1.****************, "end": **********.539888, "relative_end": **********.539888, "duration": 0.003075838088989258, "duration_str": "3.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 33354432, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1095, "nb_templates": 1095, "templates": [{"name": "1x livewire.permission-table", "param_count": null, "params": [], "start": **********.573429, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/permission-table.blade.phplivewire.permission-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fpermission-table.blade.php&line=1", "ajax": false, "filename": "permission-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.permission-table"}, {"name": "31x e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "param_count": null, "params": [], "start": **********.579151, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 31, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::heading"}, {"name": "122x e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "param_count": null, "params": [], "start": **********.581054, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 122, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button.index"}, {"name": "93x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "param_count": null, "params": [], "start": **********.584084, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 93, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "param_count": null, "params": [], "start": **********.585299, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/plus.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fplus.blade.php&line=1", "ajax": false, "filename": "plus.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus"}, {"name": "122x e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "param_count": null, "params": [], "start": **********.586018, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}, "render_count": 122, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link"}, {"name": "122x e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "param_count": null, "params": [], "start": **********.586669, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}, "render_count": 122, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip"}, {"name": "31x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "param_count": null, "params": [], "start": **********.587293, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/trigger.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Ftrigger.blade.php&line=1", "ajax": false, "filename": "trigger.blade.php", "line": "?"}, "render_count": 31, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger"}, {"name": "16x e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "param_count": null, "params": [], "start": **********.588467, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 16, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "param_count": null, "params": [], "start": **********.592008, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/magnifying-glass.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmagnifying-glass.blade.php&line=1", "ajax": false, "filename": "magnifying-glass.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass"}, {"name": "31x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "param_count": null, "params": [], "start": **********.593777, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 31, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "param_count": null, "params": [], "start": **********.595343, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/clearable.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Fclearable.blade.php&line=1", "ajax": false, "filename": "clearable.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable"}, {"name": "31x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "param_count": null, "params": [], "start": **********.596888, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/x-mark.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fx-mark.blade.php&line=1", "ajax": false, "filename": "x-mark.blade.php", "line": "?"}, "render_count": 31, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark"}, {"name": "16x e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "param_count": null, "params": [], "start": **********.601809, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}, "render_count": 16, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-field"}, {"name": "5x components.table.column", "param_count": null, "params": [], "start": **********.602967, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/column.blade.phpcomponents.table.column", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}, "render_count": 5, "name_original": "components.table.column"}, {"name": "1x components.table.columns", "param_count": null, "params": [], "start": **********.605407, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/columns.blade.phpcomponents.table.columns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcolumns.blade.php&line=1", "ajax": false, "filename": "columns.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.columns"}, {"name": "75x components.table.cell", "param_count": null, "params": [], "start": **********.606203, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/cell.blade.phpcomponents.table.cell", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}, "render_count": 75, "name_original": "components.table.cell"}, {"name": "30x e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "param_count": null, "params": [], "start": **********.60733, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/badge/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::badge.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbadge%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 30, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::badge.index"}, {"name": "30x e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "param_count": null, "params": [], "start": **********.608776, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-div.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-div.blade.php&line=1", "ajax": false, "filename": "button-or-div.blade.php", "line": "?"}, "render_count": 30, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-div"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "param_count": null, "params": [], "start": **********.618008, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/pencil-square.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fpencil-square.blade.php&line=1", "ajax": false, "filename": "pencil-square.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square"}, {"name": "15x livewire.permission-form", "param_count": null, "params": [], "start": **********.623658, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/permission-form.blade.phplivewire.permission-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fpermission-form.blade.php&line=1", "ajax": false, "filename": "permission-form.blade.php", "line": "?"}, "render_count": 15, "name_original": "livewire.permission-form"}, {"name": "30x e60dd9d2c3a62d619c9acb38f20d5aa5::label", "param_count": null, "params": [], "start": **********.626734, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 30, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::label"}, {"name": "30x e60dd9d2c3a62d619c9acb38f20d5aa5::error", "param_count": null, "params": [], "start": **********.633828, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 30, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::error"}, {"name": "30x e60dd9d2c3a62d619c9acb38f20d5aa5::field", "param_count": null, "params": [], "start": **********.634976, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 30, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::field"}, {"name": "30x components.option", "param_count": null, "params": [], "start": **********.636753, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/option.blade.phpcomponents.option", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Foption.blade.php&line=1", "ajax": false, "filename": "option.blade.php", "line": "?"}, "render_count": 30, "name_original": "components.option"}, {"name": "15x components.select", "param_count": null, "params": [], "start": **********.637773, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/select.blade.phpcomponents.select", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 15, "name_original": "components.select"}, {"name": "30x e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "param_count": null, "params": [], "start": **********.639721, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/spacer.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fspacer.blade.php&line=1", "ajax": false, "filename": "spacer.blade.php", "line": "?"}, "render_count": 30, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::spacer"}, {"name": "60x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "param_count": null, "params": [], "start": **********.642858, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/close.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 60, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close"}, {"name": "30x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "param_count": null, "params": [], "start": **********.650431, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 30, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "param_count": null, "params": [], "start": **********.663846, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/trash.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Ftrash.blade.php&line=1", "ajax": false, "filename": "trash.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "param_count": null, "params": [], "start": **********.668522, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/subheading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsubheading.blade.php&line=1", "ajax": false, "filename": "subheading.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::subheading"}, {"name": "15x components.table.row", "param_count": null, "params": [], "start": **********.68373, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/row.blade.phpcomponents.table.row", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}, "render_count": 15, "name_original": "components.table.row"}, {"name": "1x components.table.rows", "param_count": null, "params": [], "start": **********.527887, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/rows.blade.phpcomponents.table.rows", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Frows.blade.php&line=1", "ajax": false, "filename": "rows.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.rows"}, {"name": "1x components.table.index", "param_count": null, "params": [], "start": **********.529267, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/index.blade.phpcomponents.table.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.index"}, {"name": "1x livewire::tailwind", "param_count": null, "params": [], "start": **********.531072, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire::tailwind"}, {"name": "1x components.card", "param_count": null, "params": [], "start": **********.533272, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.card"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "param_count": null, "params": [], "start": **********.534154, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/card/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::card.index"}]}, "queries": {"count": 5, "nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.008820000000000001, "accumulated_duration_str": "8.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 131}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 111}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 60}, {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 88}], "start": **********.453969, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CacheManager.php:245", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheManager.php&line=245", "ajax": false, "filename": "CacheManager.php", "line": "245"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn' limit 1", "type": "query", "params": [], "bindings": ["dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.493263, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 47.619}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.5289629, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 47.619, "width_percent": 6.576}, {"sql": "select count(*) as aggregate from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/PermissionTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PermissionTable.php", "line": 50}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.5596561, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "PermissionTable.php:50", "source": {"index": 16, "namespace": null, "name": "app/Livewire/PermissionTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PermissionTable.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPermissionTable.php&line=50", "ajax": false, "filename": "PermissionTable.php", "line": "50"}, "connection": "daily", "explain": null, "start_percent": 54.195, "width_percent": 33.22}, {"sql": "select `permissions`.*, (select count(*) from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `permissions`.`id` = `role_has_permissions`.`permission_id`) as `roles_count`, (select count(*) from `users` inner join `model_has_permissions` on `users`.`id` = `model_has_permissions`.`model_id` where `permissions`.`id` = `model_has_permissions`.`permission_id` and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User') as `users_count` from `permissions` order by `name` asc limit 15 offset 15", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/PermissionTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PermissionTable.php", "line": 50}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.56481, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "PermissionTable.php:50", "source": {"index": 16, "namespace": null, "name": "app/Livewire/PermissionTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\PermissionTable.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FPermissionTable.php&line=50", "ajax": false, "filename": "PermissionTable.php", "line": "50"}, "connection": "daily", "explain": null, "start_percent": 87.415, "width_percent": 12.585}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 16, "is_counter": true}, "livewire": {"data": {"permission-table #OWPY2wiYvDSKuVpgxlUQ": "array:4 [\n  \"data\" => array:2 [\n    \"search\" => \"\"\n    \"paginators\" => array:1 [\n      \"page\" => 2\n    ]\n  ]\n  \"name\" => \"permission-table\"\n  \"component\" => \"App\\Livewire\\PermissionTable\"\n  \"id\" => \"OWPY2wiYvDSKuVpgxlUQ\"\n]", "permission-form #CoNDopVLkVVqew6o1TtE": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1869\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 7\n        \"name\" => \"edit accounts\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 2\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 7\n        \"name\" => \"edit accounts\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 2\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"edit accounts\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"CoNDopVLkVVqew6o1TtE\"\n]", "permission-form #Gnn5kByYHRNdJoCP6YSj": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1843\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 11\n        \"name\" => \"edit categories\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 2\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 11\n        \"name\" => \"edit categories\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 2\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"edit categories\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"Gnn5kByYHRNdJoCP6YSj\"\n]", "permission-form #LJBIuYW8ebWkA4xeDFvU": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spatie\\Permission\\Models\\Permission {#1844\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 27\n        \"name\" => \"edit permissions\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"roles_count\" => 1\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 27\n        \"name\" => \"edit permissions\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"roles_count\" => 1\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"edit permissions\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"LJBIuYW8ebWkA4xeDFvU\"\n]", "permission-form #U9CZxlSrfTnNJWCcSx1F": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1845\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 23\n        \"name\" => \"edit roles\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 1\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 23\n        \"name\" => \"edit roles\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 1\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"edit roles\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"U9CZxlSrfTnNJWCcSx1F\"\n]", "permission-form #UXecnMOJ3IS4cekAqfOx": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1846\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 31\n        \"name\" => \"edit settings\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"roles_count\" => 1\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 31\n        \"name\" => \"edit settings\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"roles_count\" => 1\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"edit settings\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"UXecnMOJ3IS4cekAqfOx\"\n]", "permission-form #DmDqYxiSmvIVi0Y4eE5F": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1847\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 15\n        \"name\" => \"edit tags\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 2\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 15\n        \"name\" => \"edit tags\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 2\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"edit tags\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"DmDqYxiSmvIVi0Y4eE5F\"\n]", "permission-form #vxfjTHU1NiDKBuFW0cCw": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1848\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 3\n        \"name\" => \"edit transactions\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 3\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 3\n        \"name\" => \"edit transactions\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 3\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"edit transactions\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"vxfjTHU1NiDKBuFW0cCw\"\n]", "permission-form #X2QBhlGDCk1QsNjIc3wv": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1849\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 19\n        \"name\" => \"edit users\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 1\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 19\n        \"name\" => \"edit users\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 1\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"edit users\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"X2QBhlGDCk1QsNjIc3wv\"\n]", "permission-form #jkZktyG7mCTmJsaor8Gk": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1850\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 5\n        \"name\" => \"view accounts\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 4\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 5\n        \"name\" => \"view accounts\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 4\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"view accounts\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"jkZktyG7mCTmJsaor8Gk\"\n]", "permission-form #UEkb0HmB3OJRJhquJpJB": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1851\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 9\n        \"name\" => \"view categories\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 4\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 9\n        \"name\" => \"view categories\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 4\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"view categories\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"UEkb0HmB3OJRJhquJpJB\"\n]", "permission-form #g51UwvRugztFgy0CYCF2": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1852\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 25\n        \"name\" => \"view permissions\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 1\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 25\n        \"name\" => \"view permissions\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 1\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"view permissions\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"g51UwvRugztFgy0CYCF2\"\n]", "permission-form #E84CVyTgLp8ZgBGTRZvL": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1853\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 21\n        \"name\" => \"view roles\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 1\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 21\n        \"name\" => \"view roles\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 1\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"view roles\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"E84CVyTgLp8ZgBGTRZvL\"\n]", "permission-form #wwUP0Zc4mvpAWEJAcgCn": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1854\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 30\n        \"name\" => \"view settings\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"roles_count\" => 4\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 30\n        \"name\" => \"view settings\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:24\"\n        \"updated_at\" => \"2025-05-25 10:32:24\"\n        \"roles_count\" => 4\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"view settings\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"wwUP0Zc4mvpAWEJAcgCn\"\n]", "permission-form #JohWz8BpoSJopl7MlJdP": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1855\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 13\n        \"name\" => \"view tags\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 4\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 13\n        \"name\" => \"view tags\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 4\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"view tags\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"JohWz8BpoSJopl7MlJdP\"\n]", "permission-form #9LEg7zNOHr2lu4L9APYY": "array:4 [\n  \"data\" => array:4 [\n    \"show_permission_form\" => false\n    \"permission\" => Spa<PERSON>\\Permission\\Models\\Permission {#1856\n      #connection: \"mysql\"\n      #table: \"permissions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 1\n        \"name\" => \"view transactions\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 4\n        \"users_count\" => 0\n      ]\n      #original: array:7 [\n        \"id\" => 1\n        \"name\" => \"view transactions\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-05-25 10:32:23\"\n        \"updated_at\" => \"2025-05-25 10:32:23\"\n        \"roles_count\" => 4\n        \"users_count\" => 0\n      ]\n      #changes: []\n      #previous: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"name\" => \"view transactions\"\n    \"guard_name\" => \"web\"\n  ]\n  \"name\" => \"permission-form\"\n  \"component\" => \"App\\Livewire\\PermissionForm\"\n  \"id\" => \"9LEg7zNOHr2lu4L9APYY\"\n]"}, "count": 16}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\PermissionTable@gotoPage<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2FHandlesPagination.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2FHandlesPagination.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Features/SupportPagination/HandlesPagination.php:34-37</a>", "middleware": "web", "duration": "1.45s", "peak_memory": "34MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-930330807 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-930330807\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2049910498 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"886 characters\">{&quot;data&quot;:{&quot;search&quot;:&quot;&quot;,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;OWPY2wiYvDSKuVpgxlUQ&quot;,&quot;name&quot;:&quot;permission-table&quot;,&quot;path&quot;:&quot;permissions&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-3383144489-0&quot;:[&quot;div&quot;,&quot;OGof4XNPBE4jskoIIjUt&quot;],&quot;29&quot;:[&quot;div&quot;,&quot;idSr4KoCqh1D0ftvm4S0&quot;],&quot;6&quot;:[&quot;div&quot;,&quot;SdWyl13GsSTWDY91HgjD&quot;],&quot;10&quot;:[&quot;div&quot;,&quot;y51VjMN2I2jib0duRocn&quot;],&quot;26&quot;:[&quot;div&quot;,&quot;Xkx3HPe1SGrn6S3S96Cq&quot;],&quot;22&quot;:[&quot;div&quot;,&quot;TsOYVHqWuWrx4EuCDdmn&quot;],&quot;14&quot;:[&quot;div&quot;,&quot;i8OiPBEwxp0BY9zpEEgJ&quot;],&quot;2&quot;:[&quot;div&quot;,&quot;G9R616XkT5RkavP7EmsD&quot;],&quot;18&quot;:[&quot;div&quot;,&quot;XsLgLJfPnq1LR9mhj710&quot;],&quot;8&quot;:[&quot;div&quot;,&quot;KYCmUbTzRVcgg6mUtAM1&quot;],&quot;12&quot;:[&quot;div&quot;,&quot;SbndFVwkJfs5hXxduniN&quot;],&quot;28&quot;:[&quot;div&quot;,&quot;IJjwuU8LeFQEIxDMlwxE&quot;],&quot;24&quot;:[&quot;div&quot;,&quot;3yFq0s7zPz16CYLVnVlU&quot;],&quot;16&quot;:[&quot;div&quot;,&quot;2y4MQIQRVKKKJ2aoZFqW&quot;],&quot;4&quot;:[&quot;div&quot;,&quot;CEJCahE3KYvI0pxHqzGr&quot;],&quot;20&quot;:[&quot;div&quot;,&quot;sl8Mih5evmmrfYKcqjvq&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;efa1ccd979ca63680d26e2f3a8e15228b6ce465849c3f630ceca26c010f6b0bc&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">gotoPage</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>2</span>\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049910498\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-257188207 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/permissions</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjBEZGUybGtCL3lGM1JUUTB0WjllT0E9PSIsInZhbHVlIjoiQ05XeUtWZExhNlZBcC9tcldBQ2N2eTVLdHliUVhoNEE2L05tR2VyMlVOZTZ5SEhaRDdYWFpvRzlZREluM1pBU3dhZ2lHT1NKVUMzZzhOTDQ4UlZSL0VjZW5ZMGNxL2hFV0l1OVJSZWdwQmVFY0hscXMrM1RadzF0UmdJcGVWaWwiLCJtYWMiOiJmZDM4ZjYxZTE4ZWVmYzJhZGU5YTNkYjJmNGIwMjNkYmQ2ODI5MzY4OWFiY2NjZjFkOWEyZjU1YmQwNGJhMDYxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imt5UXhLVlV0QXNCWWlXTTVxdSs0eFE9PSIsInZhbHVlIjoicTRvbktGNWxZamRYeE56b1lDVkRYdU1QNlR4UkYwN1AyV1RJRGgyR3MwaHNqZW8xZkplL3hLWk1aM1RqQkMvWnh2YngzTkR4V1poUFN3SVo0bUpPcTNHZDQrc1FyN25WeERVM2xVTUNiL3JPSCsvYzhtNU5rVnd0eEVyS2dDWmwiLCJtYWMiOiIxNjU2MTY4ZGI1NTc5MzNlMDAyNWVkNjI5NzgxMWFkZTM2MmVjY2NlY2Y1YmQxOGUxYzE1MjlmZmVkZWM2NWIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257188207\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-941948486 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941948486\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-535113231 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 26 May 2025 08:48:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535113231\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2013761306 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/permissions</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013761306\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}