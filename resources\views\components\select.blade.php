@props([
    'name' => $attributes->whereStartsWith('wire:model')->first(),
    'placeholder' => null,
    'invalid' => null,
    'size' => null,
    'multiple' => false,
    'clearable' => false,
])

@php
$invalid ??= ($name && $errors->has($name));

$classes = 'appearance-none w-full pl-3 pr-10 block border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-zinc-900 dark:text-zinc-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:focus:ring-indigo-400 dark:focus:border-indigo-400 ' .
           match ($size) {
               'sm' => 'h-8 py-1.5 text-sm leading-none rounded-md',
               'xs' => 'h-6 text-xs leading-none rounded-md',
               default => 'h-10 py-2 text-base sm:text-sm leading-none rounded-lg',
           } . ' ' .
           ($invalid ? 'border-red-500 dark:border-red-400 focus:ring-red-500 focus:border-red-500' : '');
@endphp

<div class="relative">
    <select
        {{ $attributes->class($classes) }}
        @if ($invalid) aria-invalid="true" @endif
        @if ($name) name="{{ $name }}" @endif
        @if ($multiple) multiple @endif
    >
        @if ($placeholder && !$multiple)
            <option value="" disabled selected>{{ $placeholder }}</option>
        @endif
        
        {{ $slot }}
    </select>
    
    @if (!$multiple)
        <!-- Dropdown arrow -->
        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg class="w-4 h-4 text-zinc-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
        </div>
    @endif
    
    @if ($clearable && !$multiple)
        <!-- Clear button -->
        <button 
            type="button" 
            class="absolute inset-y-0 right-8 flex items-center pr-1 text-zinc-400 hover:text-zinc-600 dark:hover:text-zinc-300"
            onclick="this.parentElement.querySelector('select').value = ''; this.parentElement.querySelector('select').dispatchEvent(new Event('change', { bubbles: true }));"
        >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    @endif
</div>
