<div>
    <?php if (isset($component)) { $__componentOriginal8a84eac5abb8af1e2274971f8640b38f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a84eac5abb8af1e2274971f8640b38f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::label','data' => ['class' => 'hidden']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::label'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'hidden']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a84eac5abb8af1e2274971f8640b38f)): ?>
<?php $attributes = $__attributesOriginal8a84eac5abb8af1e2274971f8640b38f; ?>
<?php unset($__attributesOriginal8a84eac5abb8af1e2274971f8640b38f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a84eac5abb8af1e2274971f8640b38f)): ?>
<?php $component = $__componentOriginal8a84eac5abb8af1e2274971f8640b38f; ?>
<?php unset($__componentOriginal8a84eac5abb8af1e2274971f8640b38f); ?>
<?php endif; ?>

    <div x-data="{
        isDragging: false,
        handleDrop(e) {
            e.preventDefault()
            this.isDragging = false
            this.$refs.input.files = e.dataTransfer.files
            this.$refs.input.dispatchEvent(new Event('change'))
        }
    }" x-on:dragenter.prevent="isDragging = true" x-on:dragleave.prevent="isDragging = false"
        x-on:dragover.prevent x-on:drop="handleDrop($event)" class="relative">
        <label for="files"
            <?php if($disabled): echo 'disabled'; endif; ?>
            class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                'flex flex-col items-center justify-center w-full h-48 transition-colors border border-dashed rounded-[8px] cursor-pointer bg-white inset-shadow-xs dark:bg-zinc-900 border-zinc-200 dark:border-zinc-700 dark:hover:border-emerald-600 hover:border-emerald-500 hover:bg-emerald-50 dark:hover:bg-emerald-950/50',
                'cursor-default! opacity-50 hover:border-zinc-200 dark:hover:border-zinc-500 hover:bg-white dark:hover:bg-zinc-900' => $disabled
            ]); ?>"
            :class="{
                'border-zinc-300 dark:border-zinc-700': !isDragging,
                '!border-emerald-500 !bg-emerald-50 dark:!bg-emerald-950/50': isDragging
            }">
            <?php if (isset($component)) { $__componentOriginal3d2ef69dc5cb798bddf8c4a41e1e72e6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3d2ef69dc5cb798bddf8c4a41e1e72e6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-up-to-line','data' => ['wire:target' => 'files','wire:loading.remove' => true,'class' => '!h-6 !w-6 !mb-3 !text-zinc-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon.arrow-up-to-line'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:target' => 'files','wire:loading.remove' => true,'class' => '!h-6 !w-6 !mb-3 !text-zinc-400']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3d2ef69dc5cb798bddf8c4a41e1e72e6)): ?>
<?php $attributes = $__attributesOriginal3d2ef69dc5cb798bddf8c4a41e1e72e6; ?>
<?php unset($__attributesOriginal3d2ef69dc5cb798bddf8c4a41e1e72e6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3d2ef69dc5cb798bddf8c4a41e1e72e6)): ?>
<?php $component = $__componentOriginal3d2ef69dc5cb798bddf8c4a41e1e72e6; ?>
<?php unset($__componentOriginal3d2ef69dc5cb798bddf8c4a41e1e72e6); ?>
<?php endif; ?>

            <div class="flex items-center justify-center mb-4 space-x-1.5 text-sm italic font-medium text-zinc-500"
                wire:target='files' wire:loading.flex>
                <span>Uploading files</span>

                <?php if (isset($component)) { $__componentOriginalb06f0c5905a9427a630c5e299af7ce46 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb06f0c5905a9427a630c5e299af7ce46 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading','data' => ['wire:target' => 'files','wire:loading' => true,'class' => 'h-[16px]!']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon.loading'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:target' => 'files','wire:loading' => true,'class' => 'h-[16px]!']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb06f0c5905a9427a630c5e299af7ce46)): ?>
<?php $attributes = $__attributesOriginalb06f0c5905a9427a630c5e299af7ce46; ?>
<?php unset($__attributesOriginalb06f0c5905a9427a630c5e299af7ce46); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb06f0c5905a9427a630c5e299af7ce46)): ?>
<?php $component = $__componentOriginalb06f0c5905a9427a630c5e299af7ce46; ?>
<?php unset($__componentOriginalb06f0c5905a9427a630c5e299af7ce46); ?>
<?php endif; ?>
            </div>

            <p class="mb-2 text-sm text-zinc-500 dark:text-zinc-400">
                <span class="font-semibold text-emerald-500">
                    Click to upload
                </span>

                or drag and drop
            </p>

            <p class="text-xs text-zinc-500 dark:text-zinc-400">
                JPG, JPEG, PNG, HEIC, SVG, AVIF, or WEBP
            </p>

            <input id="files" name="files" type="file" :key="$input_uuid . 'file'" x-ref="input" wire:model="files"
                accept=".jpg, .jpeg, .png, .heic, .svg, .avif, .webp" class="hidden cursor-pointer" multiple />
        </label>

        <!--[if BLOCK]><![endif]--><?php if($uploaded_files): ?>
            <div>
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $uploaded_files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div wire:key="<?php echo e($file['id']); ?>"
                        class="flex items-center shadow-xs mt-1.5 justify-between p-[7px] border border-zinc-200 dark:border-zinc-700 rounded-[8px] bg-white dark:bg-zinc-900">
                        <div class="flex items-center space-x-2">
                            <div>
                                <?php if (isset($component)) { $__componentOriginale428ff3664af329988e8d13451374b90 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale428ff3664af329988e8d13451374b90 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.file-preview','data' => ['file' => $file]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('file-preview'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['file' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($file)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale428ff3664af329988e8d13451374b90)): ?>
<?php $attributes = $__attributesOriginale428ff3664af329988e8d13451374b90; ?>
<?php unset($__attributesOriginale428ff3664af329988e8d13451374b90); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale428ff3664af329988e8d13451374b90)): ?>
<?php $component = $__componentOriginale428ff3664af329988e8d13451374b90; ?>
<?php unset($__componentOriginale428ff3664af329988e8d13451374b90); ?>
<?php endif; ?>
                            </div>

                            <div class="flex flex-col space-y-0.5">
                                <p class="text-[13px] break-all max-w-[240px] sm:!max-w-[1000px] leading-3 text-zinc-600 dark:text-zinc-300">
                                    <?php echo e($file['original_name']); ?>

                                </p>

                                <span class="text-xs text-zinc-400">
                                    Size: <?php echo e($file['size']); ?>

                                </span>
                            </div>
                        </div>

                        <?php if (isset($component)) { $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::button.index','data' => ['icon' => 'x-mark','variant' => 'ghost','class' => 'h-7! w-7! text-red-500! rounded-md','type' => 'button','wire:click' => 'removeFile(\''.e($file['name']).'\', \''.e($file['id']).'\')']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'x-mark','variant' => 'ghost','class' => 'h-7! w-7! text-red-500! rounded-md','type' => 'button','wire:click' => 'removeFile(\''.e($file['name']).'\', \''.e($file['id']).'\')']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $attributes = $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $component = $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['files.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="pt-2 text-sm text-rose-600 dark:text-rose-400">
                <?php echo e($message); ?>

            </div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div><?php /**PATH C:\laragon\www\pure-finance\resources\views/livewire/file-uploader.blade.php ENDPATH**/ ?>