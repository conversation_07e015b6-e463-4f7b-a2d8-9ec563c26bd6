<?php

declare(strict_types=1);

namespace App\Livewire;

use Flux\Flux;
use Livewire\Component;
use Livewire\Attributes\On;
use Spatie\Permission\Models\Role;
use Illuminate\Contracts\View\View;
use Spatie\Permission\Models\Permission;

class RoleForm extends Component
{
    public ?array $role = null;

    public string $name = '';

    public string $guard_name = 'web';

    public array $permissions = [];

    protected function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                'unique:roles,name' . ($this->role ? ','. $this->role['id'] : ''),
            ],
            'guard_name' => ['required', 'string', 'max:255'],
            'permissions' => ['array'],
            'permissions.*' => ['exists:permissions,id'],
        ];
    }

    protected function messages(): array
    {
        return [
            'name.unique' => 'The role name has already been taken.',
            'permissions.*.exists' => 'One or more selected permissions are invalid.',
        ];
    }

    #[On('load-role')]
    public function loadRole(array $role): void
    {
        $this->role = $role;
        $this->name = $this->role['name'];
        $this->guard_name = $this->role['guard_name'];

        // Load role permissions
        $roleModel = Role::find($this->role['id']);
        $this->permissions = $roleModel ? $roleModel->permissions->pluck('id')->toArray() : [];
    }

    public function resetForm(): void
    {
        $this->reset(['role', 'name', 'guard_name', 'permissions']);
        $this->resetErrorBag();
        $this->resetValidation();
    }

    public function submit(): void
    {
        $validated_data = $this->validate();

        if ($this->role) {
            $roleModel = Role::find($this->role['id']);
            $roleModel->update([
                'name' => $validated_data['name'],
                'guard_name' => $validated_data['guard_name'],
            ]);
        } else {
            $roleModel = Role::create([
                'name' => $validated_data['name'],
                'guard_name' => $validated_data['guard_name'],
            ]);
        }

        // Sync permissions
        if ($roleModel) {
            $roleModel->syncPermissions($validated_data['permissions']);
        }

        $this->dispatch('role-saved');

        if (!$this->role) {
            $this->reset(['name', 'guard_name', 'permissions']);
        }

        Flux::toast(
            variant: 'success',
            text: 'Role successfully ' . ($this->role ? 'updated' : 'created'),
        );

        $modalName = $this->role ? 'edit-role-' . $this->role['id'] : 'create-role';
        $this->resetForm();
        Flux::modal($modalName)->close();
    }

    public function render(): View
    {
        return view('livewire.role-form', [
            'availablePermissions' => Permission::orderBy('name')->get(),
        ]);
    }
}
