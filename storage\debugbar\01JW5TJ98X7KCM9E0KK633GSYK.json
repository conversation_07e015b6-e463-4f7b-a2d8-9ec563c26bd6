{"__meta": {"id": "01JW5TJ98X7KCM9E0KK633GSYK", "datetime": "2025-05-26 08:15:23", "utime": **********.934236, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748247322.949151, "end": **********.934249, "duration": 0.9850978851318359, "duration_str": "985ms", "measures": [{"label": "Booting", "start": 1748247322.949151, "relative_start": 0, "end": **********.274402, "relative_end": **********.274402, "duration": 0.*****************, "duration_str": "325ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.274414, "relative_start": 0.*****************, "end": **********.934251, "relative_end": 2.1457672119140625e-06, "duration": 0.***************, "duration_str": "660ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.314149, "relative_start": 0.*****************, "end": **********.316976, "relative_end": **********.316976, "duration": 0.002827167510986328, "duration_str": "2.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: volt-livewire::auth.login", "start": **********.803316, "relative_start": 0.****************, "end": **********.803316, "relative_end": **********.803316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.auth-header", "start": **********.807042, "relative_start": 0.****************, "end": **********.807042, "relative_end": **********.807042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.808122, "relative_start": 0.****************, "end": **********.808122, "relative_end": **********.808122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.auth-session-status", "start": **********.809243, "relative_start": 0.8600919246673584, "end": **********.809243, "relative_end": **********.809243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.809993, "relative_start": 0.8608419895172119, "end": **********.809993, "relative_end": **********.809993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.812402, "relative_start": 0.8632509708404541, "end": **********.812402, "relative_end": **********.812402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.813889, "relative_start": 0.8647379875183105, "end": **********.813889, "relative_end": **********.813889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.814861, "relative_start": 0.8657100200653076, "end": **********.814861, "relative_end": **********.814861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.816517, "relative_start": 0.8673660755157471, "end": **********.816517, "relative_end": **********.816517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.exclamation-triangle", "start": **********.817578, "relative_start": 0.868427038192749, "end": **********.817578, "relative_end": **********.817578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.896235, "relative_start": 0.9470839500427246, "end": **********.896235, "relative_end": **********.896235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": **********.897291, "relative_start": 0.9481399059295654, "end": **********.897291, "relative_end": **********.897291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.viewable", "start": **********.903195, "relative_start": 0.9540438652038574, "end": **********.903195, "relative_end": **********.903195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.eye-slash", "start": **********.906514, "relative_start": 0.9573628902435303, "end": **********.906514, "relative_end": **********.906514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.eye", "start": **********.907514, "relative_start": 0.9583630561828613, "end": **********.907514, "relative_end": **********.907514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.90813, "relative_start": 0.9589788913726807, "end": **********.90813, "relative_end": **********.90813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.910372, "relative_start": 0.9612209796905518, "end": **********.910372, "relative_end": **********.910372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.911585, "relative_start": 0.9624340534210205, "end": **********.911585, "relative_end": **********.911585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.912728, "relative_start": 0.9635770320892334, "end": **********.912728, "relative_end": **********.912728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.914016, "relative_start": 0.96486496925354, "end": **********.914016, "relative_end": **********.914016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.914573, "relative_start": 0.9654219150543213, "end": **********.914573, "relative_end": **********.914573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.915153, "relative_start": 0.9660019874572754, "end": **********.915153, "relative_end": **********.915153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::link", "start": **********.916897, "relative_start": 0.9677460193634033, "end": **********.916897, "relative_end": **********.916897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::checkbox.index", "start": **********.917811, "relative_start": 0.9686598777770996, "end": **********.917811, "relative_end": **********.917811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::checkbox.variants.default", "start": **********.918549, "relative_start": 0.969398021697998, "end": **********.918549, "relative_end": **********.918549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::checkbox.indicator", "start": **********.919567, "relative_start": 0.9704160690307617, "end": **********.919567, "relative_end": **********.919567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.check", "start": **********.92081, "relative_start": 0.9716589450836182, "end": **********.92081, "relative_end": **********.92081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.minus", "start": **********.922513, "relative_start": 0.9733619689941406, "end": **********.922513, "relative_end": **********.922513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-inline-field", "start": **********.923233, "relative_start": 0.9740819931030273, "end": **********.923233, "relative_end": **********.923233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": **********.92407, "relative_start": 0.9749188423156738, "end": **********.92407, "relative_end": **********.92407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": **********.924498, "relative_start": 0.9753470420837402, "end": **********.924498, "relative_end": **********.924498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": **********.925339, "relative_start": 0.9761879444122314, "end": **********.925339, "relative_end": **********.925339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.926008, "relative_start": 0.9768569469451904, "end": **********.926008, "relative_end": **********.926008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.927153, "relative_start": 0.9780020713806152, "end": **********.927153, "relative_end": **********.927153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": **********.927869, "relative_start": 0.9787180423736572, "end": **********.927869, "relative_end": **********.927869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.928412, "relative_start": 0.9792609214782715, "end": **********.928412, "relative_end": **********.928412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.92889, "relative_start": 0.9797389507293701, "end": **********.92889, "relative_end": **********.92889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::link", "start": **********.929595, "relative_start": 0.9804439544677734, "end": **********.929595, "relative_end": **********.929595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.930732, "relative_start": 0.9815809726715088, "end": **********.931832, "relative_end": **********.931832, "duration": 0.0011000633239746094, "duration_str": "1.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 30332080, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 38, "nb_templates": 38, "templates": [{"name": "volt-livewire::auth.login", "param_count": null, "params": [], "start": **********.803254, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views\\livewire/auth/login.blade.phpvolt-livewire::auth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "components.auth-header", "param_count": null, "params": [], "start": **********.80698, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/auth-header.blade.phpcomponents.auth-header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fauth-header.blade.php&line=1", "ajax": false, "filename": "auth-header.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "param_count": null, "params": [], "start": **********.808068, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}}, {"name": "components.auth-session-status", "param_count": null, "params": [], "start": **********.809189, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/auth-session-status.blade.phpcomponents.auth-session-status", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fauth-session-status.blade.php&line=1", "ajax": false, "filename": "auth-session-status.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "param_count": null, "params": [], "start": **********.809941, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "param_count": null, "params": [], "start": **********.812346, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::label", "param_count": null, "params": [], "start": **********.813836, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::error", "param_count": null, "params": [], "start": **********.814807, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "param_count": null, "params": [], "start": **********.816461, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.exclamation-triangle", "param_count": null, "params": [], "start": **********.817523, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/exclamation-triangle.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.exclamation-triangle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fexclamation-triangle.blade.php&line=1", "ajax": false, "filename": "exclamation-triangle.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::field", "param_count": null, "params": [], "start": **********.896175, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "param_count": null, "params": [], "start": **********.897236, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.viewable", "param_count": null, "params": [], "start": **********.903109, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/viewable.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.viewable", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Fviewable.blade.php&line=1", "ajax": false, "filename": "viewable.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.eye-slash", "param_count": null, "params": [], "start": **********.906455, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/eye-slash.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.eye-slash", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Feye-slash.blade.php&line=1", "ajax": false, "filename": "eye-slash.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.eye", "param_count": null, "params": [], "start": **********.907458, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/eye.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.eye", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Feye.blade.php&line=1", "ajax": false, "filename": "eye.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "param_count": null, "params": [], "start": **********.908075, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "param_count": null, "params": [], "start": **********.910242, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "param_count": null, "params": [], "start": **********.911487, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "param_count": null, "params": [], "start": **********.912626, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::label", "param_count": null, "params": [], "start": **********.913952, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::error", "param_count": null, "params": [], "start": **********.91452, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::field", "param_count": null, "params": [], "start": **********.915099, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::link", "param_count": null, "params": [], "start": **********.916842, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/link.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flink.blade.php&line=1", "ajax": false, "filename": "link.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::checkbox.index", "param_count": null, "params": [], "start": **********.917758, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/checkbox/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::checkbox.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fcheckbox%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::checkbox.variants.default", "param_count": null, "params": [], "start": **********.918493, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/checkbox/variants/default.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::checkbox.variants.default", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fcheckbox%2Fvariants%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::checkbox.indicator", "param_count": null, "params": [], "start": **********.9195, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/checkbox/indicator.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::checkbox.indicator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fcheckbox%2Findicator.blade.php&line=1", "ajax": false, "filename": "indicator.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.check", "param_count": null, "params": [], "start": **********.920646, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/check.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.minus", "param_count": null, "params": [], "start": **********.922434, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/minus.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.minus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fminus.blade.php&line=1", "ajax": false, "filename": "minus.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-inline-field", "param_count": null, "params": [], "start": **********.923179, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-inline-field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-inline-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-inline-field.blade.php&line=1", "ajax": false, "filename": "with-inline-field.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::label", "param_count": null, "params": [], "start": **********.924019, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::error", "param_count": null, "params": [], "start": **********.924448, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::field", "param_count": null, "params": [], "start": **********.925287, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "param_count": null, "params": [], "start": **********.925958, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "param_count": null, "params": [], "start": **********.927102, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "param_count": null, "params": [], "start": **********.927818, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "param_count": null, "params": [], "start": **********.928358, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "param_count": null, "params": [], "start": **********.928839, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}}, {"name": "e60dd9d2c3a62d619c9acb38f20d5aa5::link", "param_count": null, "params": [], "start": **********.929544, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/link.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flink.blade.php&line=1", "ajax": false, "filename": "link.blade.php", "line": "?"}}]}, "queries": {"count": 12, "nb_statements": 9, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05477, "accumulated_duration_str": "54.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 131}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 111}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 60}, {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 88}], "start": **********.30953, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CacheManager.php:245", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheManager.php&line=245", "ajax": false, "filename": "CacheManager.php", "line": "245"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'MQtNHXquDRSKEHKCRWAERWnWnxBskHoYMB9No2a4' limit 1", "type": "query", "params": [], "bindings": ["MQtNHXquDRSKEHKCRWAERWnWnxBskHoYMB9No2a4"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.331457, "duration": 0.03203, "duration_str": "32.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 58.481}, {"sql": "select * from `cache` where `key` in ('<EMAIL>|127.0.0.1')", "type": "query", "params": [], "bindings": ["<EMAIL>|127.0.0.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 300}], "start": **********.421259, "duration": 0.01385, "duration_str": "13.85ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "daily", "explain": null, "start_percent": 58.481, "width_percent": 25.288}, {"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 414}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 411}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 339}, {"index": 22, "namespace": null, "name": "resources/views/livewire/auth/login.blade.php", "file": "C:\\laragon\\www\\pure-finance\\resources\\views\\livewire\\auth\\login.blade.php", "line": 31}], "start": **********.461501, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:138", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=138", "ajax": false, "filename": "EloquentUserProvider.php", "line": "138"}, "connection": "daily", "explain": null, "start_percent": 83.768, "width_percent": 2.008}, {"sql": "select * from `cache` where `key` in ('<EMAIL>|127.0.0.1:timer')", "type": "query", "params": [], "bindings": ["<EMAIL>|127.0.0.1:timer"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 202}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 164}], "start": **********.766518, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "daily", "explain": null, "start_percent": 85.777, "width_percent": 1.205}, {"sql": "insert ignore into `cache` (`key`, `value`, `expiration`) values ('<EMAIL>|127.0.0.1:timer', 'i:1748247383;', 1748247383)", "type": "query", "params": [], "bindings": ["<EMAIL>|127.0.0.1:timer", "i:1748247383;", 1748247383], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 211}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 164}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 149}, {"index": 15, "namespace": null, "name": "resources/views/livewire/auth/login.blade.php", "file": "C:\\laragon\\www\\pure-finance\\resources\\views\\livewire\\auth\\login.blade.php", "line": 32}], "start": **********.7699978, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:211", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 211}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=211", "ajax": false, "filename": "DatabaseStore.php", "line": "211"}, "connection": "daily", "explain": null, "start_percent": 86.982, "width_percent": 5.879}, {"sql": "select * from `cache` where `key` in ('<EMAIL>|127.0.0.1')", "type": "query", "params": [], "bindings": ["<EMAIL>|127.0.0.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 202}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 169}], "start": **********.7757251, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "daily", "explain": null, "start_percent": 92.861, "width_percent": 0.876}, {"sql": "insert ignore into `cache` (`key`, `value`, `expiration`) values ('<EMAIL>|127.0.0.1', 'i:0;', 1748247383)", "type": "query", "params": [], "bindings": ["<EMAIL>|127.0.0.1", "i:0;", 1748247383], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 211}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 169}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 300}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 168}], "start": **********.778362, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:211", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 211}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=211", "ajax": false, "filename": "DatabaseStore.php", "line": "211"}, "connection": "daily", "explain": null, "start_percent": 93.737, "width_percent": 2.556}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 232}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 149}], "start": **********.785817, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:261", "source": {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=261", "ajax": false, "filename": "DatabaseStore.php", "line": "261"}, "connection": "daily", "explain": null, "start_percent": 96.294, "width_percent": 0}, {"sql": "select * from `cache` where `key` = '<EMAIL>|127.0.0.1' limit 1 for update", "type": "query", "params": [], "bindings": ["<EMAIL>|127.0.0.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 265}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 232}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}], "start": **********.786007, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:265", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=265", "ajax": false, "filename": "DatabaseStore.php", "line": "265"}, "connection": "daily", "explain": null, "start_percent": 96.294, "width_percent": 2.355}, {"sql": "update `cache` set `value` = 'i:1;' where `key` = '<EMAIL>|127.0.0.1'", "type": "query", "params": [], "bindings": ["i:1;", "<EMAIL>|127.0.0.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 290}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 232}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}], "start": **********.790793, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:290", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=290", "ajax": false, "filename": "DatabaseStore.php", "line": "290"}, "connection": "daily", "explain": null, "start_percent": 98.649, "width_percent": 1.351}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 232}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 149}], "start": **********.797774, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:261", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=261", "ajax": false, "filename": "DatabaseStore.php", "line": "261"}, "connection": "daily", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"auth.login #MS1MhUYpewEcBErhU2ps": "array:4 [\n  \"data\" => array:3 [\n    \"email\" => \"<EMAIL>\"\n    \"password\" => \"123456789\"\n    \"remember\" => false\n  ]\n  \"name\" => \"auth.login\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\laragon\\www\\pure-finance\\resources\\views\\livewire\\auth\\login.blade.php:13$4ee\"\n  \"id\" => \"MS1MhUYpewEcBErhU2ps\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Volt\\Component@anonymous\u0000C:\\laragon\\www\\pure-finance\\resources\\views\\livewire\\auth\\login.blade.php:13$4ee@login", "middleware": "web", "duration": "987ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1336596451 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1336596451\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1065984828 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oyF9YA5evMl3D9ZuE6HZ1zaJA204tqlFeHbEQSwN</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"282 characters\">{&quot;data&quot;:{&quot;email&quot;:&quot;&quot;,&quot;password&quot;:&quot;&quot;,&quot;remember&quot;:false},&quot;memo&quot;:{&quot;id&quot;:&quot;MS1MhUYpewEcBErhU2ps&quot;,&quot;name&quot;:&quot;auth.login&quot;,&quot;path&quot;:&quot;login&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;051431be9ca910cb8f7e798a402f2206224fa9aa9d3411cc2dd45c03d125f82e&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"5 characters\">login</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065984828\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1703282278 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">526</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InBna0toUDMvWG9LVFlPWk9YOFVKUUE9PSIsInZhbHVlIjoiRFo4cE5vUFU4YjRHNzd6RWVQcys5eERiS3hEaGRRNVBlcTFWRDFRT29EcUNhQ25FNXNqaCtOQm5Fb0JUYkRUcDZFUGF2eDd6SmV1ZndrckQxK2Z2MkNsMWVPRFl1MW94c0lyL04yM3RaR1BLdXg3OHFVSlRiNGRNL3lHZStieXUiLCJtYWMiOiIyN2NlMmI3OThkZmFiNDViMDBkOTNlMThjNWU2ZGIwMDlkMmEzZTljMzllMTIwM2IxOTBmNTcwN2I3M2NkZmJlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InB4L1JzRDk4Kyt1d3h5STFYK08yUEE9PSIsInZhbHVlIjoiMjM4VVdKSXVUVjgxQWdEamRNdlBremlSNGVwUEF6ZXcvOGlFcUc3c1ZNdUUrOGQvZXhDTno5UnJkdVBYaTJYOXY3ZGNsU3czaEtUL1Z6VzdRWWR2dXdKamhJYWh5bFh1blg4Qmd0ejU0cFc5SW5iVjFWdmVEQnZtQmorbkdDMk8iLCJtYWMiOiJmNGU0ZWM5M2Y3ZGEyNzk4NmRhMWJlMThhMDE4MmZmMmJjNWZlMjAxZDU0YmZkNWE4OWZlZjc2MjdlNmM1YTMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703282278\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oyF9YA5evMl3D9ZuE6HZ1zaJA204tqlFeHbEQSwN</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MQtNHXquDRSKEHKCRWAERWnWnxBskHoYMB9No2a4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1573216842 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 26 May 2025 08:15:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1573216842\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-880367667 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oyF9YA5evMl3D9ZuE6HZ1zaJA204tqlFeHbEQSwN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-880367667\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}