{"__meta": {"id": "01JW3ESH6TKJ5SGX740AFQVZV3", "datetime": "2025-05-25 10:11:09", "utime": **********.65919, "method": "GET", "uri": "/transaction-form/cole-llc", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748167868.628709, "end": **********.659205, "duration": 1.0304958820343018, "duration_str": "1.03s", "measures": [{"label": "Booting", "start": 1748167868.628709, "relative_start": 0, "end": **********.038968, "relative_end": **********.038968, "duration": 0.****************, "duration_str": "410ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.038988, "relative_start": 0.****************, "end": **********.659207, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "620ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.07174, "relative_start": 0.*****************, "end": **********.07598, "relative_end": **********.07598, "duration": 0.0042400360107421875, "duration_str": "4.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.transaction-form", "start": **********.229581, "relative_start": 0.****************, "end": **********.229581, "relative_end": **********.229581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.236446, "relative_start": 0.****************, "end": **********.236446, "relative_end": **********.236446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.237862, "relative_start": 0.6091530323028564, "end": **********.237862, "relative_end": **********.237862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.238539, "relative_start": 0.6098299026489258, "end": **********.238539, "relative_end": **********.238539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.23959, "relative_start": 0.6108808517456055, "end": **********.23959, "relative_end": **********.23959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.240383, "relative_start": 0.6116738319396973, "end": **********.240383, "relative_end": **********.240383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.24103, "relative_start": 0.6123208999633789, "end": **********.24103, "relative_end": **********.24103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.241763, "relative_start": 0.6130540370941162, "end": **********.241763, "relative_end": **********.241763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.242602, "relative_start": 0.6138930320739746, "end": **********.242602, "relative_end": **********.242602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.243544, "relative_start": 0.6148350238800049, "end": **********.243544, "relative_end": **********.243544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.24467, "relative_start": 0.6159608364105225, "end": **********.24467, "relative_end": **********.24467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.245347, "relative_start": 0.6166379451751709, "end": **********.245347, "relative_end": **********.245347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.24597, "relative_start": 0.6172609329223633, "end": **********.24597, "relative_end": **********.24597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.246479, "relative_start": 0.6177699565887451, "end": **********.246479, "relative_end": **********.246479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.246897, "relative_start": 0.6181879043579102, "end": **********.246897, "relative_end": **********.246897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.247651, "relative_start": 0.6189420223236084, "end": **********.247651, "relative_end": **********.247651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.248526, "relative_start": 0.6198170185089111, "end": **********.248526, "relative_end": **********.248526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.249575, "relative_start": 0.6208658218383789, "end": **********.249575, "relative_end": **********.249575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.250404, "relative_start": 0.621694803237915, "end": **********.250404, "relative_end": **********.250404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.251371, "relative_start": 0.622661828994751, "end": **********.251371, "relative_end": **********.251371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.251907, "relative_start": 0.6231980323791504, "end": **********.251907, "relative_end": **********.251907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.252292, "relative_start": 0.6235828399658203, "end": **********.252292, "relative_end": **********.252292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.252741, "relative_start": 0.6240320205688477, "end": **********.252741, "relative_end": **********.252741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.253206, "relative_start": 0.6244969367980957, "end": **********.253206, "relative_end": **********.253206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.253964, "relative_start": 0.6252548694610596, "end": **********.253964, "relative_end": **********.253964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.254547, "relative_start": 0.625838041305542, "end": **********.254547, "relative_end": **********.254547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.255139, "relative_start": 0.6264300346374512, "end": **********.255139, "relative_end": **********.255139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.255638, "relative_start": 0.6269288063049316, "end": **********.255638, "relative_end": **********.255638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.256001, "relative_start": 0.6272919178009033, "end": **********.256001, "relative_end": **********.256001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.25653, "relative_start": 0.6278209686279297, "end": **********.25653, "relative_end": **********.25653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.257072, "relative_start": 0.6283628940582275, "end": **********.257072, "relative_end": **********.257072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.25865, "relative_start": 0.6299409866333008, "end": **********.25865, "relative_end": **********.25865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.259522, "relative_start": 0.6308128833770752, "end": **********.259522, "relative_end": **********.259522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.260226, "relative_start": 0.6315169334411621, "end": **********.260226, "relative_end": **********.260226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.260738, "relative_start": 0.6320288181304932, "end": **********.260738, "relative_end": **********.260738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.261127, "relative_start": 0.6324179172515869, "end": **********.261127, "relative_end": **********.261127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.261595, "relative_start": 0.6328859329223633, "end": **********.261595, "relative_end": **********.261595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.262051, "relative_start": 0.6333420276641846, "end": **********.262051, "relative_end": **********.262051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.index", "start": **********.262858, "relative_start": 0.6341488361358643, "end": **********.262858, "relative_end": **********.262858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.variants.listbox", "start": **********.263589, "relative_start": 0.6348798274993896, "end": **********.263589, "relative_end": **********.263589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.button", "start": **********.264578, "relative_start": 0.635869026184082, "end": **********.264578, "relative_end": **********.264578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.selected", "start": **********.265976, "relative_start": 0.6372668743133545, "end": **********.265976, "relative_end": **********.265976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.270016, "relative_start": 0.6413068771362305, "end": **********.270016, "relative_end": **********.270016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.270655, "relative_start": 0.6419458389282227, "end": **********.270655, "relative_end": **********.270655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.272595, "relative_start": 0.6438858509063721, "end": **********.272595, "relative_end": **********.272595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.273398, "relative_start": 0.6446888446807861, "end": **********.273398, "relative_end": **********.273398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-down", "start": **********.274843, "relative_start": 0.6461338996887207, "end": **********.274843, "relative_end": **********.274843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.options", "start": **********.275631, "relative_start": 0.6469218730926514, "end": **********.275631, "relative_end": **********.275631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.276416, "relative_start": 0.6477069854736328, "end": **********.276416, "relative_end": **********.276416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.277634, "relative_start": 0.6489248275756836, "end": **********.277634, "relative_end": **********.277634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.278474, "relative_start": 0.6497650146484375, "end": **********.278474, "relative_end": **********.278474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.279122, "relative_start": 0.6504130363464355, "end": **********.279122, "relative_end": **********.279122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.279804, "relative_start": 0.651094913482666, "end": **********.279804, "relative_end": **********.279804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.280378, "relative_start": 0.6516690254211426, "end": **********.280378, "relative_end": **********.280378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.280819, "relative_start": 0.6521098613739014, "end": **********.280819, "relative_end": **********.280819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.281445, "relative_start": 0.6527359485626221, "end": **********.281445, "relative_end": **********.281445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.282093, "relative_start": 0.6533839702606201, "end": **********.282093, "relative_end": **********.282093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.283188, "relative_start": 0.6544790267944336, "end": **********.283188, "relative_end": **********.283188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.283991, "relative_start": 0.6552820205688477, "end": **********.283991, "relative_end": **********.283991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.categories", "start": **********.284765, "relative_start": 0.6560559272766113, "end": **********.284765, "relative_end": **********.284765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.285982, "relative_start": 0.6572728157043457, "end": **********.285982, "relative_end": **********.285982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.286545, "relative_start": 0.6578359603881836, "end": **********.286545, "relative_end": **********.286545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.287079, "relative_start": 0.6583700180053711, "end": **********.287079, "relative_end": **********.287079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.287559, "relative_start": 0.6588499546051025, "end": **********.287559, "relative_end": **********.287559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.287986, "relative_start": 0.6592769622802734, "end": **********.287986, "relative_end": **********.287986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.288935, "relative_start": 0.6602258682250977, "end": **********.288935, "relative_end": **********.288935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.289537, "relative_start": 0.6608278751373291, "end": **********.289537, "relative_end": **********.289537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.289994, "relative_start": 0.6612849235534668, "end": **********.289994, "relative_end": **********.289994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.290432, "relative_start": 0.6617228984832764, "end": **********.290432, "relative_end": **********.290432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.29084, "relative_start": 0.6621308326721191, "end": **********.29084, "relative_end": **********.29084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.291256, "relative_start": 0.6625468730926514, "end": **********.291256, "relative_end": **********.291256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.291666, "relative_start": 0.662956953048706, "end": **********.291666, "relative_end": **********.291666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.292082, "relative_start": 0.6633729934692383, "end": **********.292082, "relative_end": **********.292082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.292483, "relative_start": 0.6637740135192871, "end": **********.292483, "relative_end": **********.292483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.292888, "relative_start": 0.6641788482666016, "end": **********.292888, "relative_end": **********.292888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.293316, "relative_start": 0.6646068096160889, "end": **********.293316, "relative_end": **********.293316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.29373, "relative_start": 0.6650209426879883, "end": **********.29373, "relative_end": **********.29373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.294146, "relative_start": 0.6654369831085205, "end": **********.294146, "relative_end": **********.294146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.294575, "relative_start": 0.6658658981323242, "end": **********.294575, "relative_end": **********.294575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.294991, "relative_start": 0.6662819385528564, "end": **********.294991, "relative_end": **********.294991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.295441, "relative_start": 0.6667318344116211, "end": **********.295441, "relative_end": **********.295441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.295845, "relative_start": 0.6671359539031982, "end": **********.295845, "relative_end": **********.295845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.29626, "relative_start": 0.6675510406494141, "end": **********.29626, "relative_end": **********.29626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.296673, "relative_start": 0.667963981628418, "end": **********.296673, "relative_end": **********.296673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.297079, "relative_start": 0.6683700084686279, "end": **********.297079, "relative_end": **********.297079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.297511, "relative_start": 0.66880202293396, "end": **********.297511, "relative_end": **********.297511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.298102, "relative_start": 0.6693928241729736, "end": **********.298102, "relative_end": **********.298102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.298688, "relative_start": 0.6699788570404053, "end": **********.298688, "relative_end": **********.298688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.299275, "relative_start": 0.6705658435821533, "end": **********.299275, "relative_end": **********.299275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.299972, "relative_start": 0.6712629795074463, "end": **********.299972, "relative_end": **********.299972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.300651, "relative_start": 0.6719419956207275, "end": **********.300651, "relative_end": **********.300651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.301131, "relative_start": 0.672421932220459, "end": **********.301131, "relative_end": **********.301131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.plus", "start": **********.302068, "relative_start": 0.6733589172363281, "end": **********.302068, "relative_end": **********.302068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.302688, "relative_start": 0.6739788055419922, "end": **********.302688, "relative_end": **********.302688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.304577, "relative_start": 0.675868034362793, "end": **********.304577, "relative_end": **********.304577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.305239, "relative_start": 0.6765298843383789, "end": **********.305239, "relative_end": **********.305239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.trigger", "start": **********.3058, "relative_start": 0.6770908832550049, "end": **********.3058, "relative_end": **********.3058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.306415, "relative_start": 0.6777060031890869, "end": **********.306415, "relative_end": **********.306415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.category-form", "start": **********.309114, "relative_start": 0.6804049015045166, "end": **********.309114, "relative_end": **********.309114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.310689, "relative_start": 0.6819798946380615, "end": **********.310689, "relative_end": **********.310689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.311297, "relative_start": 0.6825878620147705, "end": **********.311297, "relative_end": **********.311297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.311953, "relative_start": 0.683243989944458, "end": **********.311953, "relative_end": **********.311953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.314195, "relative_start": 0.68548583984375, "end": **********.314195, "relative_end": **********.314195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.31567, "relative_start": 0.6869609355926514, "end": **********.31567, "relative_end": **********.31567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.316482, "relative_start": 0.6877729892730713, "end": **********.316482, "relative_end": **********.316482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.317275, "relative_start": 0.6885659694671631, "end": **********.317275, "relative_end": **********.317275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.321497, "relative_start": 0.6927878856658936, "end": **********.321497, "relative_end": **********.321497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.322236, "relative_start": 0.6935269832611084, "end": **********.322236, "relative_end": **********.322236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.322977, "relative_start": 0.694267988204956, "end": **********.322977, "relative_end": **********.322977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.323519, "relative_start": 0.6948099136352539, "end": **********.323519, "relative_end": **********.323519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.323914, "relative_start": 0.6952049732208252, "end": **********.323914, "relative_end": **********.323914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.324413, "relative_start": 0.6957039833068848, "end": **********.324413, "relative_end": **********.324413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.324937, "relative_start": 0.69622802734375, "end": **********.324937, "relative_end": **********.324937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.325832, "relative_start": 0.6971228122711182, "end": **********.325832, "relative_end": **********.325832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.326507, "relative_start": 0.6977980136871338, "end": **********.326507, "relative_end": **********.326507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.327213, "relative_start": 0.6985039710998535, "end": **********.327213, "relative_end": **********.327213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.327753, "relative_start": 0.6990439891815186, "end": **********.327753, "relative_end": **********.327753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.328162, "relative_start": 0.6994528770446777, "end": **********.328162, "relative_end": **********.328162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.328674, "relative_start": 0.6999650001525879, "end": **********.328674, "relative_end": **********.328674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.3292, "relative_start": 0.7004909515380859, "end": **********.3292, "relative_end": **********.3292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.330081, "relative_start": 0.7013719081878662, "end": **********.330081, "relative_end": **********.330081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.330731, "relative_start": 0.7020218372344971, "end": **********.330731, "relative_end": **********.330731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.331675, "relative_start": 0.7029659748077393, "end": **********.331675, "relative_end": **********.331675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.332556, "relative_start": 0.7038469314575195, "end": **********.332556, "relative_end": **********.332556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.333059, "relative_start": 0.7043499946594238, "end": **********.333059, "relative_end": **********.333059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.33382, "relative_start": 0.705111026763916, "end": **********.33382, "relative_end": **********.33382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.334656, "relative_start": 0.7059469223022461, "end": **********.334656, "relative_end": **********.334656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.335718, "relative_start": 0.7070088386535645, "end": **********.335718, "relative_end": **********.335718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.336441, "relative_start": 0.7077319622039795, "end": **********.336441, "relative_end": **********.336441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.337137, "relative_start": 0.708427906036377, "end": **********.337137, "relative_end": **********.337137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.337741, "relative_start": 0.7090318202972412, "end": **********.337741, "relative_end": **********.337741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.338237, "relative_start": 0.7095279693603516, "end": **********.338237, "relative_end": **********.338237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.338805, "relative_start": 0.7100958824157715, "end": **********.338805, "relative_end": **********.338805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.339363, "relative_start": 0.7106540203094482, "end": **********.339363, "relative_end": **********.339363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.340265, "relative_start": 0.7115559577941895, "end": **********.340265, "relative_end": **********.340265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.340958, "relative_start": 0.7122490406036377, "end": **********.340958, "relative_end": **********.340958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.341677, "relative_start": 0.7129678726196289, "end": **********.341677, "relative_end": **********.341677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.34224, "relative_start": 0.7135310173034668, "end": **********.34224, "relative_end": **********.34224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.342655, "relative_start": 0.7139458656311035, "end": **********.342655, "relative_end": **********.342655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.343182, "relative_start": 0.7144730091094971, "end": **********.343182, "relative_end": **********.343182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.343714, "relative_start": 0.7150049209594727, "end": **********.343714, "relative_end": **********.343714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.344617, "relative_start": 0.7159078121185303, "end": **********.344617, "relative_end": **********.344617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.345296, "relative_start": 0.7165868282318115, "end": **********.345296, "relative_end": **********.345296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.345993, "relative_start": 0.7172839641571045, "end": **********.345993, "relative_end": **********.345993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.346561, "relative_start": 0.7178518772125244, "end": **********.346561, "relative_end": **********.346561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.346981, "relative_start": 0.7182719707489014, "end": **********.346981, "relative_end": **********.346981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.347529, "relative_start": 0.7188198566436768, "end": **********.347529, "relative_end": **********.347529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.348264, "relative_start": 0.7195549011230469, "end": **********.348264, "relative_end": **********.348264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.354498, "relative_start": 0.7257888317108154, "end": **********.354498, "relative_end": **********.354498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.355519, "relative_start": 0.7268099784851074, "end": **********.355519, "relative_end": **********.355519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.356374, "relative_start": 0.7276649475097656, "end": **********.356374, "relative_end": **********.356374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.356965, "relative_start": 0.7282559871673584, "end": **********.356965, "relative_end": **********.356965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.357406, "relative_start": 0.7286968231201172, "end": **********.357406, "relative_end": **********.357406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.357926, "relative_start": 0.7292168140411377, "end": **********.357926, "relative_end": **********.357926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.358466, "relative_start": 0.7297568321228027, "end": **********.358466, "relative_end": **********.358466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.359375, "relative_start": 0.730665922164917, "end": **********.359375, "relative_end": **********.359375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.360063, "relative_start": 0.7313539981842041, "end": **********.360063, "relative_end": **********.360063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.360753, "relative_start": 0.732043981552124, "end": **********.360753, "relative_end": **********.360753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.361283, "relative_start": 0.7325739860534668, "end": **********.361283, "relative_end": **********.361283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.361696, "relative_start": 0.7329869270324707, "end": **********.361696, "relative_end": **********.361696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.362222, "relative_start": 0.7335128784179688, "end": **********.362222, "relative_end": **********.362222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.362789, "relative_start": 0.7340798377990723, "end": **********.362789, "relative_end": **********.362789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.363705, "relative_start": 0.7349958419799805, "end": **********.363705, "relative_end": **********.363705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.364441, "relative_start": 0.735731840133667, "end": **********.364441, "relative_end": **********.364441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.366185, "relative_start": 0.7374758720397949, "end": **********.366185, "relative_end": **********.366185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.367103, "relative_start": 0.738394021987915, "end": **********.367103, "relative_end": **********.367103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.367747, "relative_start": 0.7390379905700684, "end": **********.367747, "relative_end": **********.367747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.368337, "relative_start": 0.7396278381347656, "end": **********.368337, "relative_end": **********.368337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.368929, "relative_start": 0.7402198314666748, "end": **********.368929, "relative_end": **********.368929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.369859, "relative_start": 0.74114990234375, "end": **********.369859, "relative_end": **********.369859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.370557, "relative_start": 0.7418479919433594, "end": **********.370557, "relative_end": **********.370557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.371267, "relative_start": 0.7425580024719238, "end": **********.371267, "relative_end": **********.371267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.371829, "relative_start": 0.7431199550628662, "end": **********.371829, "relative_end": **********.371829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.372236, "relative_start": 0.7435269355773926, "end": **********.372236, "relative_end": **********.372236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.372752, "relative_start": 0.7440428733825684, "end": **********.372752, "relative_end": **********.372752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.373297, "relative_start": 0.7445878982543945, "end": **********.373297, "relative_end": **********.373297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.index", "start": **********.374154, "relative_start": 0.7454450130462646, "end": **********.374154, "relative_end": **********.374154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.variants.listbox", "start": **********.374794, "relative_start": 0.7460849285125732, "end": **********.374794, "relative_end": **********.374794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.button", "start": **********.375574, "relative_start": 0.7468650341033936, "end": **********.375574, "relative_end": **********.375574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.selected", "start": **********.376486, "relative_start": 0.747776985168457, "end": **********.376486, "relative_end": **********.376486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.377065, "relative_start": 0.7483558654785156, "end": **********.377065, "relative_end": **********.377065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.377594, "relative_start": 0.748884916305542, "end": **********.377594, "relative_end": **********.377594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.378952, "relative_start": 0.7502429485321045, "end": **********.378952, "relative_end": **********.378952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.379572, "relative_start": 0.7508628368377686, "end": **********.379572, "relative_end": **********.379572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-down", "start": **********.380173, "relative_start": 0.7514638900756836, "end": **********.380173, "relative_end": **********.380173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.options", "start": **********.380836, "relative_start": 0.752126932144165, "end": **********.380836, "relative_end": **********.380836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.382112, "relative_start": 0.7534029483795166, "end": **********.382112, "relative_end": **********.382112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.383528, "relative_start": 0.7548189163208008, "end": **********.383528, "relative_end": **********.383528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.38435, "relative_start": 0.755640983581543, "end": **********.38435, "relative_end": **********.38435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.385373, "relative_start": 0.7566640377044678, "end": **********.385373, "relative_end": **********.385373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.385926, "relative_start": 0.7572169303894043, "end": **********.385926, "relative_end": **********.385926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.387194, "relative_start": 0.7584848403930664, "end": **********.387194, "relative_end": **********.387194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.387735, "relative_start": 0.7590258121490479, "end": **********.387735, "relative_end": **********.387735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.388257, "relative_start": 0.7595479488372803, "end": **********.388257, "relative_end": **********.388257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.388642, "relative_start": 0.7599329948425293, "end": **********.388642, "relative_end": **********.388642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.389817, "relative_start": 0.7611079216003418, "end": **********.389817, "relative_end": **********.389817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.390559, "relative_start": 0.7618498802185059, "end": **********.390559, "relative_end": **********.390559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.391109, "relative_start": 0.7623999118804932, "end": **********.391109, "relative_end": **********.391109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.391629, "relative_start": 0.7629199028015137, "end": **********.391629, "relative_end": **********.391629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.392135, "relative_start": 0.7634258270263672, "end": **********.392135, "relative_end": **********.392135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": **********.392576, "relative_start": 0.7638669013977051, "end": **********.392576, "relative_end": **********.392576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.393783, "relative_start": 0.7650740146636963, "end": **********.393783, "relative_end": **********.393783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.395054, "relative_start": 0.7663450241088867, "end": **********.395054, "relative_end": **********.395054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.395564, "relative_start": 0.766855001449585, "end": **********.395564, "relative_end": **********.395564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.396024, "relative_start": 0.7673149108886719, "end": **********.396024, "relative_end": **********.396024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.396542, "relative_start": 0.7678329944610596, "end": **********.396542, "relative_end": **********.396542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.397485, "relative_start": 0.7687759399414062, "end": **********.397485, "relative_end": **********.397485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.401704, "relative_start": 0.7729949951171875, "end": **********.401704, "relative_end": **********.401704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.tags", "start": **********.402378, "relative_start": 0.7736690044403076, "end": **********.402378, "relative_end": **********.402378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.403536, "relative_start": 0.7748270034790039, "end": **********.403536, "relative_end": **********.403536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.404025, "relative_start": 0.7753159999847412, "end": **********.404025, "relative_end": **********.404025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.404432, "relative_start": 0.7757229804992676, "end": **********.404432, "relative_end": **********.404432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.40482, "relative_start": 0.7761108875274658, "end": **********.40482, "relative_end": **********.40482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.405198, "relative_start": 0.7764890193939209, "end": **********.405198, "relative_end": **********.405198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.plus", "start": **********.405722, "relative_start": 0.777012825012207, "end": **********.405722, "relative_end": **********.405722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.406162, "relative_start": 0.7774529457092285, "end": **********.406162, "relative_end": **********.406162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.407339, "relative_start": 0.7786300182342529, "end": **********.407339, "relative_end": **********.407339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.40788, "relative_start": 0.7791709899902344, "end": **********.40788, "relative_end": **********.40788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.trigger", "start": **********.4084, "relative_start": 0.7796909809112549, "end": **********.4084, "relative_end": **********.4084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.408848, "relative_start": 0.7801389694213867, "end": **********.408848, "relative_end": **********.408848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.tag-form", "start": **********.410435, "relative_start": 0.7817258834838867, "end": **********.410435, "relative_end": **********.410435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.411476, "relative_start": 0.7827668190002441, "end": **********.411476, "relative_end": **********.411476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.411998, "relative_start": 0.7832889556884766, "end": **********.411998, "relative_end": **********.411998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.412452, "relative_start": 0.7837429046630859, "end": **********.412452, "relative_end": **********.412452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.414627, "relative_start": 0.7859179973602295, "end": **********.414627, "relative_end": **********.414627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.416055, "relative_start": 0.7873458862304688, "end": **********.416055, "relative_end": **********.416055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.417074, "relative_start": 0.7883648872375488, "end": **********.417074, "relative_end": **********.417074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.417689, "relative_start": 0.7889800071716309, "end": **********.417689, "relative_end": **********.417689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.418055, "relative_start": 0.7893459796905518, "end": **********.418055, "relative_end": **********.418055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.419279, "relative_start": 0.7905700206756592, "end": **********.419279, "relative_end": **********.419279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.41983, "relative_start": 0.7911210060119629, "end": **********.41983, "relative_end": **********.41983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.420362, "relative_start": 0.7916529178619385, "end": **********.420362, "relative_end": **********.420362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.420661, "relative_start": 0.7919518947601318, "end": **********.420661, "relative_end": **********.420661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.421891, "relative_start": 0.7931818962097168, "end": **********.421891, "relative_end": **********.421891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.422415, "relative_start": 0.793705940246582, "end": **********.422415, "relative_end": **********.422415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.422885, "relative_start": 0.7941758632659912, "end": **********.422885, "relative_end": **********.422885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.423425, "relative_start": 0.7947158813476562, "end": **********.423425, "relative_end": **********.423425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": **********.423932, "relative_start": 0.7952229976654053, "end": **********.423932, "relative_end": **********.423932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.424734, "relative_start": 0.7960250377655029, "end": **********.424734, "relative_end": **********.424734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.42597, "relative_start": 0.7972609996795654, "end": **********.42597, "relative_end": **********.42597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.42649, "relative_start": 0.7977809906005859, "end": **********.42649, "relative_end": **********.42649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.426952, "relative_start": 0.7982428073883057, "end": **********.426952, "relative_end": **********.426952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.427481, "relative_start": 0.798771858215332, "end": **********.427481, "relative_end": **********.427481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.428278, "relative_start": 0.7995688915252686, "end": **********.428278, "relative_end": **********.428278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::badge.close", "start": **********.429908, "relative_start": 0.8011989593505859, "end": **********.429908, "relative_end": **********.429908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.430665, "relative_start": 0.8019559383392334, "end": **********.430665, "relative_end": **********.430665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.431435, "relative_start": 0.8027260303497314, "end": **********.431435, "relative_end": **********.431435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::badge.index", "start": **********.432227, "relative_start": 0.8035178184509277, "end": **********.432227, "relative_end": **********.432227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-div", "start": **********.434222, "relative_start": 0.8055129051208496, "end": **********.434222, "relative_end": **********.434222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.434848, "relative_start": 0.8061389923095703, "end": **********.434848, "relative_end": **********.434848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.435367, "relative_start": 0.8066580295562744, "end": **********.435367, "relative_end": **********.435367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.435809, "relative_start": 0.8070998191833496, "end": **********.435809, "relative_end": **********.435809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.437428, "relative_start": 0.8087189197540283, "end": **********.437428, "relative_end": **********.437428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.438251, "relative_start": 0.8095419406890869, "end": **********.438251, "relative_end": **********.438251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.438802, "relative_start": 0.8100929260253906, "end": **********.438802, "relative_end": **********.438802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.439303, "relative_start": 0.810593843460083, "end": **********.439303, "relative_end": **********.439303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.439753, "relative_start": 0.8110439777374268, "end": **********.439753, "relative_end": **********.439753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.441383, "relative_start": 0.812673807144165, "end": **********.441383, "relative_end": **********.441383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.currency-dollar", "start": **********.442177, "relative_start": 0.8134679794311523, "end": **********.442177, "relative_end": **********.442177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.443175, "relative_start": 0.8144659996032715, "end": **********.443175, "relative_end": **********.443175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.444161, "relative_start": 0.8154518604278564, "end": **********.444161, "relative_end": **********.444161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.444776, "relative_start": 0.8160669803619385, "end": **********.444776, "relative_end": **********.444776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.445276, "relative_start": 0.8165669441223145, "end": **********.445276, "relative_end": **********.445276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::date-picker.index", "start": **********.445794, "relative_start": 0.8170850276947021, "end": **********.445794, "relative_end": **********.445794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::date-picker.button", "start": **********.447436, "relative_start": 0.8187270164489746, "end": **********.447436, "relative_end": **********.447436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.calendar", "start": **********.449128, "relative_start": 0.8204188346862793, "end": **********.449128, "relative_end": **********.449128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::date-picker.selected", "start": **********.450087, "relative_start": 0.8213779926300049, "end": **********.450087, "relative_end": **********.450087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.450871, "relative_start": 0.8221619129180908, "end": **********.450871, "relative_end": **********.450871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.451409, "relative_start": 0.822700023651123, "end": **********.451409, "relative_end": **********.451409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.45267, "relative_start": 0.8239610195159912, "end": **********.45267, "relative_end": **********.45267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.453239, "relative_start": 0.8245298862457275, "end": **********.453239, "relative_end": **********.453239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.453813, "relative_start": 0.8251039981842041, "end": **********.453813, "relative_end": **********.453813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.45508, "relative_start": 0.8263709545135498, "end": **********.45508, "relative_end": **********.45508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.455645, "relative_start": 0.8269360065460205, "end": **********.455645, "relative_end": **********.455645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.456358, "relative_start": 0.8276488780975342, "end": **********.456358, "relative_end": **********.456358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.45761, "relative_start": 0.8289008140563965, "end": **********.45761, "relative_end": **********.45761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.458171, "relative_start": 0.8294618129730225, "end": **********.458171, "relative_end": **********.458171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.458846, "relative_start": 0.8301370143890381, "end": **********.458846, "relative_end": **********.458846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.460192, "relative_start": 0.8314828872680664, "end": **********.460192, "relative_end": **********.460192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.461116, "relative_start": 0.8324069976806641, "end": **********.461116, "relative_end": **********.461116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.462043, "relative_start": 0.8333339691162109, "end": **********.462043, "relative_end": **********.462043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::switch", "start": **********.463571, "relative_start": 0.8348619937896729, "end": **********.463571, "relative_end": **********.463571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-reversed-inline-field", "start": **********.466755, "relative_start": 0.8380458354949951, "end": **********.466755, "relative_end": **********.466755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.468395, "relative_start": 0.8396859169006348, "end": **********.468395, "relative_end": **********.468395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.46912, "relative_start": 0.8404109477996826, "end": **********.46912, "relative_end": **********.46912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.469749, "relative_start": 0.8410398960113525, "end": **********.469749, "relative_end": **********.469749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::switch", "start": **********.470285, "relative_start": 0.8415758609771729, "end": **********.470285, "relative_end": **********.470285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-reversed-inline-field", "start": **********.471092, "relative_start": 0.8423829078674316, "end": **********.471092, "relative_end": **********.471092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.471984, "relative_start": 0.8432748317718506, "end": **********.471984, "relative_end": **********.471984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.472655, "relative_start": 0.8439459800720215, "end": **********.472655, "relative_end": **********.472655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.47329, "relative_start": 0.844580888748169, "end": **********.47329, "relative_end": **********.47329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::textarea", "start": **********.474133, "relative_start": 0.8454239368438721, "end": **********.474133, "relative_end": **********.474133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.475434, "relative_start": 0.8467249870300293, "end": **********.475434, "relative_end": **********.475434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.476441, "relative_start": 0.8477318286895752, "end": **********.476441, "relative_end": **********.476441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.477102, "relative_start": 0.8483929634094238, "end": **********.477102, "relative_end": **********.477102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.477754, "relative_start": 0.8490450382232666, "end": **********.477754, "relative_end": **********.477754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.478726, "relative_start": 0.8500168323516846, "end": **********.478726, "relative_end": **********.478726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.479453, "relative_start": 0.8507440090179443, "end": **********.479453, "relative_end": **********.479453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.480079, "relative_start": 0.8513698577880859, "end": **********.480079, "relative_end": **********.480079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": **********.480815, "relative_start": 0.8521058559417725, "end": **********.480815, "relative_end": **********.480815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.481646, "relative_start": 0.8529369831085205, "end": **********.481646, "relative_end": **********.481646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.482598, "relative_start": 0.853888988494873, "end": **********.482598, "relative_end": **********.482598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.483411, "relative_start": 0.8547019958496094, "end": **********.483411, "relative_end": **********.483411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.48403, "relative_start": 0.855320930480957, "end": **********.48403, "relative_end": **********.48403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::date-picker.index", "start": **********.484691, "relative_start": 0.8559818267822266, "end": **********.484691, "relative_end": **********.484691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::date-picker.button", "start": **********.486177, "relative_start": 0.8574678897857666, "end": **********.486177, "relative_end": **********.486177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.calendar", "start": **********.487051, "relative_start": 0.8583419322967529, "end": **********.487051, "relative_end": **********.487051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::date-picker.selected", "start": **********.487614, "relative_start": 0.8589048385620117, "end": **********.487614, "relative_end": **********.487614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.488054, "relative_start": 0.8593449592590332, "end": **********.488054, "relative_end": **********.488054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.488544, "relative_start": 0.8598349094390869, "end": **********.488544, "relative_end": **********.488544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.489858, "relative_start": 0.8611488342285156, "end": **********.489858, "relative_end": **********.489858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.490765, "relative_start": 0.8620560169219971, "end": **********.490765, "relative_end": **********.490765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.491472, "relative_start": 0.8627629280090332, "end": **********.491472, "relative_end": **********.491472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.492794, "relative_start": 0.8640849590301514, "end": **********.492794, "relative_end": **********.492794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.493412, "relative_start": 0.8647029399871826, "end": **********.493412, "relative_end": **********.493412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.494007, "relative_start": 0.8652980327606201, "end": **********.494007, "relative_end": **********.494007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.495285, "relative_start": 0.8665759563446045, "end": **********.495285, "relative_end": **********.495285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.495891, "relative_start": 0.8671820163726807, "end": **********.495891, "relative_end": **********.495891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.496455, "relative_start": 0.8677458763122559, "end": **********.496455, "relative_end": **********.496455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.497353, "relative_start": 0.8686439990997314, "end": **********.497353, "relative_end": **********.497353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.498057, "relative_start": 0.8693478107452393, "end": **********.498057, "relative_end": **********.498057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.498865, "relative_start": 0.8701558113098145, "end": **********.498865, "relative_end": **********.498865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.49981, "relative_start": 0.871100902557373, "end": **********.49981, "relative_end": **********.49981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.500595, "relative_start": 0.8718860149383545, "end": **********.500595, "relative_end": **********.500595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.file-uploader", "start": **********.504093, "relative_start": 0.8753838539123535, "end": **********.504093, "relative_end": **********.504093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.505045, "relative_start": 0.876335859298706, "end": **********.505045, "relative_end": **********.505045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.arrow-up-to-line", "start": **********.50614, "relative_start": 0.8774309158325195, "end": **********.50614, "relative_end": **********.50614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.50697, "relative_start": 0.8782608509063721, "end": **********.50697, "relative_end": **********.50697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.510707, "relative_start": 0.88199782371521, "end": **********.510707, "relative_end": **********.510707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.511323, "relative_start": 0.8826138973236084, "end": **********.511323, "relative_end": **********.511323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.511811, "relative_start": 0.8831019401550293, "end": **********.511811, "relative_end": **********.511811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.512223, "relative_start": 0.8835139274597168, "end": **********.512223, "relative_end": **********.512223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.513381, "relative_start": 0.8846719264984131, "end": **********.513381, "relative_end": **********.513381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.513916, "relative_start": 0.885206937789917, "end": **********.513916, "relative_end": **********.513916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.trigger", "start": **********.514561, "relative_start": 0.8858518600463867, "end": **********.514561, "relative_end": **********.514561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.515321, "relative_start": 0.8866119384765625, "end": **********.515321, "relative_end": **********.515321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::subheading", "start": **********.516351, "relative_start": 0.8876419067382812, "end": **********.516351, "relative_end": **********.516351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.517186, "relative_start": 0.8884768486022949, "end": **********.517186, "relative_end": **********.517186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.517689, "relative_start": 0.8889799118041992, "end": **********.517689, "relative_end": **********.517689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.518886, "relative_start": 0.8901770114898682, "end": **********.518886, "relative_end": **********.518886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.519432, "relative_start": 0.8907229900360107, "end": **********.519432, "relative_end": **********.519432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.519955, "relative_start": 0.8912458419799805, "end": **********.519955, "relative_end": **********.519955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.520393, "relative_start": 0.89168381690979, "end": **********.520393, "relative_end": **********.520393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.52185, "relative_start": 0.8931410312652588, "end": **********.52185, "relative_end": **********.52185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.522367, "relative_start": 0.893657922744751, "end": **********.522367, "relative_end": **********.522367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.522828, "relative_start": 0.8941190242767334, "end": **********.522828, "relative_end": **********.522828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.523353, "relative_start": 0.894644021987915, "end": **********.523353, "relative_end": **********.523353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": **********.523849, "relative_start": 0.8951399326324463, "end": **********.523849, "relative_end": **********.523849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.52463, "relative_start": 0.895920991897583, "end": **********.52463, "relative_end": **********.52463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.525866, "relative_start": 0.8971569538116455, "end": **********.525866, "relative_end": **********.525866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.526379, "relative_start": 0.8976700305938721, "end": **********.526379, "relative_end": **********.526379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.526842, "relative_start": 0.8981330394744873, "end": **********.526842, "relative_end": **********.526842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.527384, "relative_start": 0.8986749649047852, "end": **********.527384, "relative_end": **********.527384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.527919, "relative_start": 0.8992099761962891, "end": **********.527919, "relative_end": **********.527919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.529134, "relative_start": 0.9004249572753906, "end": **********.529134, "relative_end": **********.529134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.530387, "relative_start": 0.9016778469085693, "end": **********.530387, "relative_end": **********.530387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.530957, "relative_start": 0.9022479057312012, "end": **********.530957, "relative_end": **********.530957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.531828, "relative_start": 0.9031188488006592, "end": **********.531828, "relative_end": **********.531828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.533528, "relative_start": 0.9048190116882324, "end": **********.533528, "relative_end": **********.533528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.534248, "relative_start": 0.9055390357971191, "end": **********.534248, "relative_end": **********.534248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.534777, "relative_start": 0.9060678482055664, "end": **********.534777, "relative_end": **********.534777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.535373, "relative_start": 0.9066638946533203, "end": **********.535373, "relative_end": **********.535373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.54546, "relative_start": 0.9167509078979492, "end": **********.54546, "relative_end": **********.54546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app", "start": **********.548507, "relative_start": 0.9197978973388672, "end": **********.548507, "relative_end": **********.548507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::main", "start": **********.549881, "relative_start": 0.9211719036102295, "end": **********.549881, "relative_end": **********.549881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app.sidebar", "start": **********.550772, "relative_start": 0.922062873840332, "end": **********.550772, "relative_end": **********.550772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.head", "start": **********.554669, "relative_start": 0.9259598255157471, "end": **********.554669, "relative_end": **********.554669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.toggle", "start": **********.556227, "relative_start": 0.9275178909301758, "end": **********.556227, "relative_end": **********.556227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.557065, "relative_start": 0.9283559322357178, "end": **********.557065, "relative_end": **********.557065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.5585, "relative_start": 0.9297909736633301, "end": **********.5585, "relative_end": **********.5585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.559089, "relative_start": 0.9303798675537109, "end": **********.559089, "relative_end": **********.559089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.55963, "relative_start": 0.9309208393096924, "end": **********.55963, "relative_end": **********.55963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.56024, "relative_start": 0.9315309524536133, "end": **********.56024, "relative_end": **********.56024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.app-logo", "start": **********.560975, "relative_start": 0.9322659969329834, "end": **********.560975, "relative_end": **********.560975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.562256, "relative_start": 0.9335470199584961, "end": **********.562256, "relative_end": **********.562256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.563406, "relative_start": 0.9346969127655029, "end": **********.563406, "relative_end": **********.563406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.layout-dashboard", "start": **********.564035, "relative_start": 0.9353258609771729, "end": **********.564035, "relative_end": **********.564035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.564918, "relative_start": 0.936208963394165, "end": **********.564918, "relative_end": **********.564918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.566165, "relative_start": 0.9374558925628662, "end": **********.566165, "relative_end": **********.566165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.567813, "relative_start": 0.9391038417816162, "end": **********.567813, "relative_end": **********.567813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.user", "start": **********.568716, "relative_start": 0.9400069713592529, "end": **********.568716, "relative_end": **********.568716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.56931, "relative_start": 0.9406008720397949, "end": **********.56931, "relative_end": **********.56931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.570125, "relative_start": 0.9414160251617432, "end": **********.570125, "relative_end": **********.570125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.571047, "relative_start": 0.9423379898071289, "end": **********.571047, "relative_end": **********.571047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.currency-dollar", "start": **********.571571, "relative_start": 0.9428620338439941, "end": **********.571571, "relative_end": **********.571571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.572029, "relative_start": 0.9433200359344482, "end": **********.572029, "relative_end": **********.572029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.572797, "relative_start": 0.9440879821777344, "end": **********.572797, "relative_end": **********.572797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.573711, "relative_start": 0.9450018405914307, "end": **********.573711, "relative_end": **********.573711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.scroll-text", "start": **********.574288, "relative_start": 0.9455788135528564, "end": **********.574288, "relative_end": **********.574288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.574859, "relative_start": 0.9461498260498047, "end": **********.574859, "relative_end": **********.574859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.575683, "relative_start": 0.9469740390777588, "end": **********.575683, "relative_end": **********.575683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.576618, "relative_start": 0.947908878326416, "end": **********.576618, "relative_end": **********.576618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.queue-list", "start": **********.577399, "relative_start": 0.9486899375915527, "end": **********.577399, "relative_end": **********.577399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.578013, "relative_start": 0.9493038654327393, "end": **********.578013, "relative_end": **********.578013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.578802, "relative_start": 0.9500930309295654, "end": **********.578802, "relative_end": **********.578802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.579723, "relative_start": 0.9510138034820557, "end": **********.579723, "relative_end": **********.579723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.tags", "start": **********.580272, "relative_start": 0.9515628814697266, "end": **********.580272, "relative_end": **********.580272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.580813, "relative_start": 0.952103853225708, "end": **********.580813, "relative_end": **********.580813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.group", "start": **********.58183, "relative_start": 0.9531209468841553, "end": **********.58183, "relative_end": **********.58183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.index", "start": **********.583082, "relative_start": 0.9543728828430176, "end": **********.583082, "relative_end": **********.583082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.584187, "relative_start": 0.9554779529571533, "end": **********.584187, "relative_end": **********.584187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.item", "start": **********.584842, "relative_start": 0.9561328887939453, "end": **********.584842, "relative_end": **********.584842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.585816, "relative_start": 0.9571068286895752, "end": **********.585816, "relative_end": **********.585816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.folder-git-2", "start": **********.586381, "relative_start": 0.9576718807220459, "end": **********.586381, "relative_end": **********.586381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.586925, "relative_start": 0.9582159519195557, "end": **********.586925, "relative_end": **********.586925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navlist.index", "start": **********.587514, "relative_start": 0.9588048458099365, "end": **********.587514, "relative_end": **********.587514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::profile", "start": **********.588575, "relative_start": 0.9598658084869385, "end": **********.588575, "relative_end": **********.588575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::avatar.index", "start": **********.589553, "relative_start": 0.9608440399169922, "end": **********.589553, "relative_end": **********.589553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.590788, "relative_start": 0.9620788097381592, "end": **********.590788, "relative_end": **********.590788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.591348, "relative_start": 0.9626388549804688, "end": **********.591348, "relative_end": **********.591348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.591931, "relative_start": 0.9632220268249512, "end": **********.591931, "relative_end": **********.591931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevrons-up-down", "start": **********.592527, "relative_start": 0.963817834854126, "end": **********.592527, "relative_end": **********.592527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.593492, "relative_start": 0.9647829532623291, "end": **********.593492, "relative_end": **********.593492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.594037, "relative_start": 0.9653279781341553, "end": **********.594037, "relative_end": **********.594037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.594787, "relative_start": 0.9660778045654297, "end": **********.594787, "relative_end": **********.594787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.595678, "relative_start": 0.9669690132141113, "end": **********.595678, "relative_end": **********.595678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.596481, "relative_start": 0.9677720069885254, "end": **********.596481, "relative_end": **********.596481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.597294, "relative_start": 0.9685850143432617, "end": **********.597294, "relative_end": **********.597294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.sun", "start": **********.598338, "relative_start": 0.9696288108825684, "end": **********.598338, "relative_end": **********.598338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.599853, "relative_start": 0.9711439609527588, "end": **********.599853, "relative_end": **********.599853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.60083, "relative_start": 0.972121000289917, "end": **********.60083, "relative_end": **********.60083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.601681, "relative_start": 0.9729719161987305, "end": **********.601681, "relative_end": **********.601681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.moon", "start": **********.602522, "relative_start": 0.9738128185272217, "end": **********.602522, "relative_end": **********.602522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.603146, "relative_start": 0.9744369983673096, "end": **********.603146, "relative_end": **********.603146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.603731, "relative_start": 0.9750218391418457, "end": **********.603731, "relative_end": **********.603731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.604518, "relative_start": 0.97580885887146, "end": **********.604518, "relative_end": **********.604518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.computer-desktop", "start": **********.605367, "relative_start": 0.9766578674316406, "end": **********.605367, "relative_end": **********.605367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.index", "start": **********.605996, "relative_start": 0.9772868156433105, "end": **********.605996, "relative_end": **********.605996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.variants.segmented", "start": **********.606792, "relative_start": 0.9780828952789307, "end": **********.606792, "relative_end": **********.606792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.607482, "relative_start": 0.9787728786468506, "end": **********.607482, "relative_end": **********.607482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.60839, "relative_start": 0.9796810150146484, "end": **********.60839, "relative_end": **********.60839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.608837, "relative_start": 0.9801278114318848, "end": **********.608837, "relative_end": **********.608837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.609692, "relative_start": 0.9809830188751221, "end": **********.609692, "relative_end": **********.609692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.610679, "relative_start": 0.9819698333740234, "end": **********.610679, "relative_end": **********.610679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.cog", "start": **********.611499, "relative_start": 0.9827899932861328, "end": **********.611499, "relative_end": **********.611499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.612139, "relative_start": 0.9834299087524414, "end": **********.612139, "relative_end": **********.612139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.612788, "relative_start": 0.9840788841247559, "end": **********.612788, "relative_end": **********.612788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.613154, "relative_start": 0.9844448566436768, "end": **********.613154, "relative_end": **********.613154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.613597, "relative_start": 0.9848878383636475, "end": **********.613597, "relative_end": **********.613597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.615143, "relative_start": 0.9864339828491211, "end": **********.615143, "relative_end": **********.615143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.616421, "relative_start": 0.9877119064331055, "end": **********.616421, "relative_end": **********.616421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.arrow-right-start-on-rectangle", "start": **********.617496, "relative_start": 0.9887869358062744, "end": **********.617496, "relative_end": **********.617496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.618165, "relative_start": 0.9894559383392334, "end": **********.618165, "relative_end": **********.618165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.index", "start": **********.618762, "relative_start": 0.9900529384613037, "end": **********.618762, "relative_end": **********.618762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown", "start": **********.619229, "relative_start": 0.9905200004577637, "end": **********.619229, "relative_end": **********.619229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.index", "start": **********.619732, "relative_start": 0.9910228252410889, "end": **********.619732, "relative_end": **********.619732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.backdrop", "start": **********.620512, "relative_start": 0.9918029308319092, "end": **********.620512, "relative_end": **********.620512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::sidebar.toggle", "start": **********.621177, "relative_start": 0.9924678802490234, "end": **********.621177, "relative_end": **********.621177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.621682, "relative_start": 0.9929728507995605, "end": **********.621682, "relative_end": **********.621682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.622924, "relative_start": 0.9942150115966797, "end": **********.622924, "relative_end": **********.622924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.panel-left", "start": **********.623485, "relative_start": 0.9947760105133057, "end": **********.623485, "relative_end": **********.623485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.624048, "relative_start": 0.9953389167785645, "end": **********.624048, "relative_end": **********.624048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.624593, "relative_start": 0.9958839416503906, "end": **********.624593, "relative_end": **********.624593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.625154, "relative_start": 0.9964449405670166, "end": **********.625154, "relative_end": **********.625154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::profile", "start": **********.625613, "relative_start": 0.9969038963317871, "end": **********.625613, "relative_end": **********.625613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::avatar.index", "start": **********.626336, "relative_start": 0.9976270198822021, "end": **********.626336, "relative_end": **********.626336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.627492, "relative_start": 0.9987828731536865, "end": **********.627492, "relative_end": **********.627492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.628056, "relative_start": 0.9993469715118408, "end": **********.628056, "relative_end": **********.628056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.628628, "relative_start": 0.9999189376831055, "end": **********.628628, "relative_end": **********.628628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevrons-up-down", "start": **********.629418, "relative_start": 1.000708818435669, "end": **********.629418, "relative_end": **********.629418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.630107, "relative_start": 1.0013978481292725, "end": **********.630107, "relative_end": **********.630107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.63047, "relative_start": 1.0017609596252441, "end": **********.63047, "relative_end": **********.63047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.630865, "relative_start": 1.0021560192108154, "end": **********.630865, "relative_end": **********.630865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.631586, "relative_start": 1.0028769969940186, "end": **********.631586, "relative_end": **********.631586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.632527, "relative_start": 1.0038180351257324, "end": **********.632527, "relative_end": **********.632527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.633688, "relative_start": 1.004978895187378, "end": **********.633688, "relative_end": **********.633688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.sun", "start": **********.634426, "relative_start": 1.0057170391082764, "end": **********.634426, "relative_end": **********.634426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.635013, "relative_start": 1.0063040256500244, "end": **********.635013, "relative_end": **********.635013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.63559, "relative_start": 1.0068809986114502, "end": **********.63559, "relative_end": **********.63559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.636309, "relative_start": 1.0075998306274414, "end": **********.636309, "relative_end": **********.636309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.moon", "start": **********.636807, "relative_start": 1.0080978870391846, "end": **********.636807, "relative_end": **********.636807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.index", "start": **********.637311, "relative_start": 1.0086019039154053, "end": **********.637311, "relative_end": **********.637311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.variants.segmented", "start": **********.637843, "relative_start": 1.0091338157653809, "end": **********.637843, "relative_end": **********.637843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.638556, "relative_start": 1.0098469257354736, "end": **********.638556, "relative_end": **********.638556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.computer-desktop", "start": **********.639062, "relative_start": 1.0103528499603271, "end": **********.639062, "relative_end": **********.639062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.index", "start": **********.639551, "relative_start": 1.0108418464660645, "end": **********.639551, "relative_end": **********.639551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::radio.group.variants.segmented", "start": **********.64006, "relative_start": 1.0113508701324463, "end": **********.64006, "relative_end": **********.64006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.640644, "relative_start": 1.0119349956512451, "end": **********.640644, "relative_end": **********.640644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.641514, "relative_start": 1.0128049850463867, "end": **********.641514, "relative_end": **********.641514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.641935, "relative_start": 1.01322603225708, "end": **********.641935, "relative_end": **********.641935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.64249, "relative_start": 1.0137808322906494, "end": **********.64249, "relative_end": **********.64249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.643362, "relative_start": 1.014652967453003, "end": **********.643362, "relative_end": **********.643362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.cog", "start": **********.643881, "relative_start": 1.015172004699707, "end": **********.643881, "relative_end": **********.643881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.644407, "relative_start": 1.015697956085205, "end": **********.644407, "relative_end": **********.644407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.radio.group", "start": **********.645432, "relative_start": 1.0167229175567627, "end": **********.645432, "relative_end": **********.645432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.separator", "start": **********.645881, "relative_start": 1.017171859741211, "end": **********.645881, "relative_end": **********.645881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::separator", "start": **********.646336, "relative_start": 1.0176270008087158, "end": **********.646336, "relative_end": **********.646336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.item", "start": **********.64699, "relative_start": 1.0182809829711914, "end": **********.64699, "relative_end": **********.64699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.648164, "relative_start": 1.0194549560546875, "end": **********.648164, "relative_end": **********.648164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.arrow-right-start-on-rectangle", "start": **********.648979, "relative_start": 1.0202698707580566, "end": **********.648979, "relative_end": **********.648979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.649716, "relative_start": 1.0210068225860596, "end": **********.649716, "relative_end": **********.649716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::menu.index", "start": **********.650422, "relative_start": 1.0217130184173584, "end": **********.650422, "relative_end": **********.650422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown", "start": **********.65094, "relative_start": 1.022230863571167, "end": **********.65094, "relative_end": **********.65094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::header", "start": **********.651447, "relative_start": 1.022737979888916, "end": **********.651447, "relative_end": **********.651447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::toast.index", "start": **********.652139, "relative_start": 1.0234298706054688, "end": **********.652139, "relative_end": **********.652139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.65656, "relative_start": 1.027850866317749, "end": **********.656731, "relative_end": **********.656731, "duration": 0.0001709461212158203, "duration_str": "171μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 31756552, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 495, "nb_templates": 495, "templates": [{"name": "1x livewire.transaction-form", "param_count": null, "params": [], "start": **********.229499, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/transaction-form.blade.phplivewire.transaction-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Ftransaction-form.blade.php&line=1", "ajax": false, "filename": "transaction-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.transaction-form"}, {"name": "6x ********************************::heading", "param_count": null, "params": [], "start": **********.236387, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.php********************************::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::heading"}, {"name": "16x ********************************::label", "param_count": null, "params": [], "start": **********.237806, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.php********************************::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 16, "name_original": "********************************::label"}, {"name": "15x ********************************::select.option.index", "param_count": null, "params": [], "start": **********.238484, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/option/index.blade.php********************************::select.option.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foption%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 15, "name_original": "********************************::select.option.index"}, {"name": "15x ********************************::select.option.variants.custom", "param_count": null, "params": [], "start": **********.239534, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/option/variants/custom.blade.php********************************::select.option.variants.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foption%2Fvariants%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}, "render_count": 15, "name_original": "********************************::select.option.variants.custom"}, {"name": "15x ********************************::select.indicator.index", "param_count": null, "params": [], "start": **********.240306, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/indicator/index.blade.php********************************::select.indicator.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findicator%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 15, "name_original": "********************************::select.indicator.index"}, {"name": "15x ********************************::select.indicator.variants.check", "param_count": null, "params": [], "start": **********.240975, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/indicator/variants/check.blade.php********************************::select.indicator.variants.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findicator%2Fvariants%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}, "render_count": 15, "name_original": "********************************::select.indicator.variants.check"}, {"name": "45x ********************************::icon.index", "param_count": null, "params": [], "start": **********.241707, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.php********************************::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 45, "name_original": "********************************::icon.index"}, {"name": "15x ********************************::icon.check", "param_count": null, "params": [], "start": **********.24253, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/check.blade.php********************************::icon.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}, "render_count": 15, "name_original": "********************************::icon.check"}, {"name": "26x ********************************::with-field", "param_count": null, "params": [], "start": **********.243465, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.php********************************::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}, "render_count": 26, "name_original": "********************************::with-field"}, {"name": "2x ********************************::select.index", "param_count": null, "params": [], "start": **********.262799, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/index.blade.php********************************::select.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::select.index"}, {"name": "2x ********************************::select.variants.listbox", "param_count": null, "params": [], "start": **********.263532, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/variants/listbox.blade.php********************************::select.variants.listbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fvariants%2Flistbox.blade.php&line=1", "ajax": false, "filename": "listbox.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::select.variants.listbox"}, {"name": "2x ********************************::select.button", "param_count": null, "params": [], "start": **********.264475, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/button.blade.php********************************::select.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::select.button"}, {"name": "2x ********************************::select.selected", "param_count": null, "params": [], "start": **********.265877, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/selected.blade.php********************************::select.selected", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fselected.blade.php&line=1", "ajax": false, "filename": "selected.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::select.selected"}, {"name": "9x ********************************::icon.x-mark", "param_count": null, "params": [], "start": **********.269953, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/x-mark.blade.php********************************::icon.x-mark", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fx-mark.blade.php&line=1", "ajax": false, "filename": "x-mark.blade.php", "line": "?"}, "render_count": 9, "name_original": "********************************::icon.x-mark"}, {"name": "24x ********************************::button.index", "param_count": null, "params": [], "start": **********.270598, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.php********************************::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 24, "name_original": "********************************::button.index"}, {"name": "37x ********************************::button-or-link", "param_count": null, "params": [], "start": **********.272534, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.php********************************::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}, "render_count": 37, "name_original": "********************************::button-or-link"}, {"name": "26x ********************************::with-tooltip", "param_count": null, "params": [], "start": **********.273294, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.php********************************::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}, "render_count": 26, "name_original": "********************************::with-tooltip"}, {"name": "2x ********************************::icon.chevron-down", "param_count": null, "params": [], "start": **********.274781, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-down.blade.php********************************::icon.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.chevron-down"}, {"name": "2x ********************************::select.options", "param_count": null, "params": [], "start": **********.275571, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/options.blade.php********************************::select.options", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foptions.blade.php&line=1", "ajax": false, "filename": "options.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::select.options"}, {"name": "15x ********************************::error", "param_count": null, "params": [], "start": **********.277572, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.php********************************::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 15, "name_original": "********************************::error"}, {"name": "15x ********************************::field", "param_count": null, "params": [], "start": **********.278412, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.php********************************::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 15, "name_original": "********************************::field"}, {"name": "39x components.option", "param_count": null, "params": [], "start": **********.279742, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/option.blade.phpcomponents.option", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Foption.blade.php&line=1", "ajax": false, "filename": "option.blade.php", "line": "?"}, "render_count": 39, "name_original": "components.option"}, {"name": "4x components.select", "param_count": null, "params": [], "start": **********.282012, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/select.blade.phpcomponents.select", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 4, "name_original": "components.select"}, {"name": "1x components.categories", "param_count": null, "params": [], "start": **********.284704, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/categories.blade.phpcomponents.categories", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcategories.blade.php&line=1", "ajax": false, "filename": "categories.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.categories"}, {"name": "2x ********************************::icon.plus", "param_count": null, "params": [], "start": **********.302007, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/plus.blade.php********************************::icon.plus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fplus.blade.php&line=1", "ajax": false, "filename": "plus.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.plus"}, {"name": "3x ********************************::modal.trigger", "param_count": null, "params": [], "start": **********.305738, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/trigger.blade.php********************************::modal.trigger", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Ftrigger.blade.php&line=1", "ajax": false, "filename": "trigger.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::modal.trigger"}, {"name": "1x livewire.category-form", "param_count": null, "params": [], "start": **********.309049, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/category-form.blade.phplivewire.category-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fcategory-form.blade.php&line=1", "ajax": false, "filename": "category-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.category-form"}, {"name": "4x ********************************::input.index", "param_count": null, "params": [], "start": **********.311889, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.php********************************::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::input.index"}, {"name": "5x ********************************::spacer", "param_count": null, "params": [], "start": **********.385309, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/spacer.blade.php********************************::spacer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fspacer.blade.php&line=1", "ajax": false, "filename": "spacer.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::spacer"}, {"name": "6x ********************************::modal.close", "param_count": null, "params": [], "start": **********.388189, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/close.blade.php********************************::modal.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::modal.close"}, {"name": "6x ********************************::icon.loading", "param_count": null, "params": [], "start": **********.390502, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.php********************************::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::icon.loading"}, {"name": "3x ********************************::modal.index", "param_count": null, "params": [], "start": **********.39252, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/index.blade.php********************************::modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::modal.index"}, {"name": "1x components.tags", "param_count": null, "params": [], "start": **********.402323, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/tags.blade.phpcomponents.tags", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftags.blade.php&line=1", "ajax": false, "filename": "tags.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.tags"}, {"name": "1x livewire.tag-form", "param_count": null, "params": [], "start": **********.410375, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/tag-form.blade.phplivewire.tag-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Ftag-form.blade.php&line=1", "ajax": false, "filename": "tag-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.tag-form"}, {"name": "1x ********************************::badge.close", "param_count": null, "params": [], "start": **********.429849, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/badge/close.blade.php********************************::badge.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbadge%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::badge.close"}, {"name": "1x ********************************::badge.index", "param_count": null, "params": [], "start": **********.43216, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/badge/index.blade.php********************************::badge.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbadge%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::badge.index"}, {"name": "1x ********************************::button-or-div", "param_count": null, "params": [], "start": **********.434141, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-div.blade.php********************************::button-or-div", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-div.blade.php&line=1", "ajax": false, "filename": "button-or-div.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::button-or-div"}, {"name": "2x ********************************::icon.currency-dollar", "param_count": null, "params": [], "start": **********.442117, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/currency-dollar.blade.php********************************::icon.currency-dollar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcurrency-dollar.blade.php&line=1", "ajax": false, "filename": "currency-dollar.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.currency-dollar"}, {"name": "2x ********************************::date-picker.index", "param_count": null, "params": [], "start": **********.445737, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/date-picker/index.blade.php********************************::date-picker.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fdate-picker%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::date-picker.index"}, {"name": "2x ********************************::date-picker.button", "param_count": null, "params": [], "start": **********.447378, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/date-picker/button.blade.php********************************::date-picker.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fdate-picker%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::date-picker.button"}, {"name": "2x ********************************::icon.calendar", "param_count": null, "params": [], "start": **********.449027, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/calendar.blade.php********************************::icon.calendar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcalendar.blade.php&line=1", "ajax": false, "filename": "calendar.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.calendar"}, {"name": "2x ********************************::date-picker.selected", "param_count": null, "params": [], "start": **********.450027, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/date-picker/selected.blade.php********************************::date-picker.selected", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fdate-picker%2Fselected.blade.php&line=1", "ajax": false, "filename": "selected.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::date-picker.selected"}, {"name": "2x ********************************::switch", "param_count": null, "params": [], "start": **********.463446, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/switch.blade.php********************************::switch", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::switch"}, {"name": "2x ********************************::with-reversed-inline-field", "param_count": null, "params": [], "start": **********.466631, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-reversed-inline-field.blade.php********************************::with-reversed-inline-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-reversed-inline-field.blade.php&line=1", "ajax": false, "filename": "with-reversed-inline-field.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::with-reversed-inline-field"}, {"name": "1x ********************************::textarea", "param_count": null, "params": [], "start": **********.474068, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/textarea.blade.php********************************::textarea", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::textarea"}, {"name": "3x components.card", "param_count": null, "params": [], "start": **********.477691, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 3, "name_original": "components.card"}, {"name": "3x ********************************::card.index", "param_count": null, "params": [], "start": **********.478657, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/card/index.blade.php********************************::card.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::card.index"}, {"name": "1x livewire.file-uploader", "param_count": null, "params": [], "start": **********.504025, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/file-uploader.blade.phplivewire.file-uploader", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Ffile-uploader.blade.php&line=1", "ajax": false, "filename": "file-uploader.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.file-uploader"}, {"name": "1x ********************************::icon.arrow-up-to-line", "param_count": null, "params": [], "start": **********.506068, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/arrow-up-to-line.blade.php********************************::icon.arrow-up-to-line", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Farrow-up-to-line.blade.php&line=1", "ajax": false, "filename": "arrow-up-to-line.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.arrow-up-to-line"}, {"name": "1x ********************************::subheading", "param_count": null, "params": [], "start": **********.516294, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/subheading.blade.php********************************::subheading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsubheading.blade.php&line=1", "ajax": false, "filename": "subheading.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::subheading"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.54539, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x components.layouts.app", "param_count": null, "params": [], "start": **********.5484, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app.blade.phpcomponents.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app"}, {"name": "1x ********************************::main", "param_count": null, "params": [], "start": **********.549815, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/main.blade.php********************************::main", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmain.blade.php&line=1", "ajax": false, "filename": "main.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::main"}, {"name": "1x components.layouts.app.sidebar", "param_count": null, "params": [], "start": **********.550706, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app/sidebar.blade.phpcomponents.layouts.app.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app.sidebar"}, {"name": "1x partials.head", "param_count": null, "params": [], "start": **********.554606, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/partials/head.blade.phppartials.head", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fpartials%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.head"}, {"name": "2x ********************************::sidebar.toggle", "param_count": null, "params": [], "start": **********.556153, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/toggle.blade.php********************************::sidebar.toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Ftoggle.blade.php&line=1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::sidebar.toggle"}, {"name": "1x components.app-logo", "param_count": null, "params": [], "start": **********.560914, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/app-logo.blade.phpcomponents.app-logo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fapp-logo.blade.php&line=1", "ajax": false, "filename": "app-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.app-logo"}, {"name": "7x ********************************::navlist.item", "param_count": null, "params": [], "start": **********.562194, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/item.blade.php********************************::navlist.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 7, "name_original": "********************************::navlist.item"}, {"name": "1x ********************************::icon.layout-dashboard", "param_count": null, "params": [], "start": **********.563973, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/layout-dashboard.blade.php********************************::icon.layout-dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Flayout-dashboard.blade.php&line=1", "ajax": false, "filename": "layout-dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.layout-dashboard"}, {"name": "1x ********************************::icon.user", "param_count": null, "params": [], "start": **********.568659, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/user.blade.php********************************::icon.user", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.user"}, {"name": "1x ********************************::icon.scroll-text", "param_count": null, "params": [], "start": **********.574221, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/scroll-text.blade.php********************************::icon.scroll-text", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fscroll-text.blade.php&line=1", "ajax": false, "filename": "scroll-text.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.scroll-text"}, {"name": "1x ********************************::icon.queue-list", "param_count": null, "params": [], "start": **********.577342, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/queue-list.blade.php********************************::icon.queue-list", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fqueue-list.blade.php&line=1", "ajax": false, "filename": "queue-list.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.queue-list"}, {"name": "1x ********************************::icon.tags", "param_count": null, "params": [], "start": **********.580218, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/tags.blade.php********************************::icon.tags", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ftags.blade.php&line=1", "ajax": false, "filename": "tags.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.tags"}, {"name": "1x ********************************::navlist.group", "param_count": null, "params": [], "start": **********.58172, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/navlist/group.blade.php********************************::navlist.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::navlist.group"}, {"name": "2x ********************************::navlist.index", "param_count": null, "params": [], "start": **********.582984, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/index.blade.php********************************::navlist.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::navlist.index"}, {"name": "1x ********************************::icon.folder-git-2", "param_count": null, "params": [], "start": **********.586328, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/folder-git-2.blade.php********************************::icon.folder-git-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ffolder-git-2.blade.php&line=1", "ajax": false, "filename": "folder-git-2.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.folder-git-2"}, {"name": "2x ********************************::profile", "param_count": null, "params": [], "start": **********.58852, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/profile.blade.php********************************::profile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fprofile.blade.php&line=1", "ajax": false, "filename": "profile.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::profile"}, {"name": "2x ********************************::avatar.index", "param_count": null, "params": [], "start": **********.589496, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/avatar/index.blade.php********************************::avatar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Favatar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::avatar.index"}, {"name": "2x ********************************::icon.chevrons-up-down", "param_count": null, "params": [], "start": **********.59247, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/chevrons-up-down.blade.php********************************::icon.chevrons-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevrons-up-down.blade.php&line=1", "ajax": false, "filename": "chevrons-up-down.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.chevrons-up-down"}, {"name": "4x ********************************::menu.radio.group", "param_count": null, "params": [], "start": **********.593434, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/radio/group.blade.php********************************::menu.radio.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fradio%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::menu.radio.group"}, {"name": "6x ********************************::menu.separator", "param_count": null, "params": [], "start": **********.593979, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/separator.blade.php********************************::menu.separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::menu.separator"}, {"name": "6x ********************************::separator", "param_count": null, "params": [], "start": **********.594718, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/separator.blade.php********************************::separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::separator"}, {"name": "6x ********************************::radio.index", "param_count": null, "params": [], "start": **********.59562, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/index.blade.php********************************::radio.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::radio.index"}, {"name": "6x ********************************::radio.variants.segmented", "param_count": null, "params": [], "start": **********.596423, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/variants/segmented.blade.php********************************::radio.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::radio.variants.segmented"}, {"name": "2x ********************************::icon.sun", "param_count": null, "params": [], "start": **********.598236, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/sun.blade.php********************************::icon.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.sun"}, {"name": "2x ********************************::icon.moon", "param_count": null, "params": [], "start": **********.602462, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/moon.blade.php********************************::icon.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.moon"}, {"name": "2x ********************************::icon.computer-desktop", "param_count": null, "params": [], "start": **********.605306, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/computer-desktop.blade.php********************************::icon.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.computer-desktop"}, {"name": "2x ********************************::radio.group.index", "param_count": null, "params": [], "start": **********.605935, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/index.blade.php********************************::radio.group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::radio.group.index"}, {"name": "2x ********************************::radio.group.variants.segmented", "param_count": null, "params": [], "start": **********.606716, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/variants/segmented.blade.php********************************::radio.group.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::radio.group.variants.segmented"}, {"name": "4x ********************************::menu.item", "param_count": null, "params": [], "start": **********.609632, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/item.blade.php********************************::menu.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::menu.item"}, {"name": "2x ********************************::icon.cog", "param_count": null, "params": [], "start": **********.611437, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/cog.blade.php********************************::icon.cog", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcog.blade.php&line=1", "ajax": false, "filename": "cog.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.cog"}, {"name": "2x ********************************::icon.arrow-right-start-on-rectangle", "param_count": null, "params": [], "start": **********.617435, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/arrow-right-start-on-rectangle.blade.php********************************::icon.arrow-right-start-on-rectangle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Farrow-right-start-on-rectangle.blade.php&line=1", "ajax": false, "filename": "arrow-right-start-on-rectangle.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.arrow-right-start-on-rectangle"}, {"name": "2x ********************************::menu.index", "param_count": null, "params": [], "start": **********.618705, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/index.blade.php********************************::menu.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::menu.index"}, {"name": "2x ********************************::dropdown", "param_count": null, "params": [], "start": **********.619174, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/dropdown.blade.php********************************::dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::dropdown"}, {"name": "1x ********************************::sidebar.index", "param_count": null, "params": [], "start": **********.619678, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/index.blade.php********************************::sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::sidebar.index"}, {"name": "1x ********************************::sidebar.backdrop", "param_count": null, "params": [], "start": **********.620442, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/backdrop.blade.php********************************::sidebar.backdrop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Fbackdrop.blade.php&line=1", "ajax": false, "filename": "backdrop.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::sidebar.backdrop"}, {"name": "1x ********************************::icon.panel-left", "param_count": null, "params": [], "start": **********.62343, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/panel-left.blade.php********************************::icon.panel-left", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fpanel-left.blade.php&line=1", "ajax": false, "filename": "panel-left.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.panel-left"}, {"name": "1x ********************************::header", "param_count": null, "params": [], "start": **********.651392, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/header.blade.php********************************::header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::header"}, {"name": "1x ********************************::toast.index", "param_count": null, "params": [], "start": **********.652082, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/toast/index.blade.php********************************::toast.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ftoast%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::toast.index"}]}, "queries": {"count": 11, "nb_statements": 10, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02608, "accumulated_duration_str": "26.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.095479, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr' limit 1", "type": "query", "params": [], "bindings": ["kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.106321, "duration": 0.01982, "duration_str": "19.82ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 75.997}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.156889, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 75.997, "width_percent": 1.956}, {"sql": "select * from `transactions` where `slug` = 'cole-llc' limit 1", "type": "query", "params": [], "bindings": ["cole-llc"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 119}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 72}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 31}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php", "line": 211}], "start": **********.165631, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:119", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FDrawer%2FImplicitRouteBinding.php&line=119", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "119"}, "connection": "daily", "explain": null, "start_percent": 77.952, "width_percent": 2.837}, {"sql": "select * from `accounts` where `accounts`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Policies/TransactionPolicy.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Policies\\TransactionPolicy.php", "line": 33}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 818}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 771}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 552}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 447}], "start": **********.172122, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "TransactionPolicy.php:33", "source": {"index": 21, "namespace": null, "name": "app/Policies/TransactionPolicy.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Policies\\TransactionPolicy.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FPolicies%2FTransactionPolicy.php&line=33", "ajax": false, "filename": "TransactionPolicy.php", "line": "33"}, "connection": "daily", "explain": null, "start_percent": 80.79, "width_percent": 2.416}, {"sql": "select `id`, `name` from `accounts` where `accounts`.`user_id` = 1 and `accounts`.`user_id` is not null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 148}, {"index": 17, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 112}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.186578, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "TransactionForm.php:148", "source": {"index": 16, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=148", "ajax": false, "filename": "TransactionForm.php", "line": "148"}, "connection": "daily", "explain": null, "start_percent": 83.206, "width_percent": 2.722}, {"sql": "select `id`, `name`, `parent_id` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null and `parent_id` is null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 163}, {"index": 17, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 113}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.190214, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "TransactionForm.php:163", "source": {"index": 16, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=163", "ajax": false, "filename": "TransactionForm.php", "line": "163"}, "connection": "daily", "explain": null, "start_percent": 85.928, "width_percent": 2.914}, {"sql": "select * from `categories` where `categories`.`parent_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 163}, {"index": 22, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 113}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.193584, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "TransactionForm.php:163", "source": {"index": 21, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=163", "ajax": false, "filename": "TransactionForm.php", "line": "163"}, "connection": "daily", "explain": null, "start_percent": 88.842, "width_percent": 2.991}, {"sql": "select `id`, `name` from `tags` where `tags`.`user_id` = 1 and `tags`.`user_id` is not null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 202}, {"index": 18, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 115}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.204818, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "TransactionForm.php:202", "source": {"index": 17, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 202}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=202", "ajax": false, "filename": "TransactionForm.php", "line": "202"}, "connection": "daily", "explain": null, "start_percent": 91.833, "width_percent": 2.569}, {"sql": "select `tags`.*, `tag_transaction`.`transaction_id` as `pivot_transaction_id`, `tag_transaction`.`tag_id` as `pivot_tag_id` from `tags` inner join `tag_transaction` on `tags`.`id` = `tag_transaction`.`tag_id` where `tag_transaction`.`transaction_id` = 39", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 132}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.211319, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "TransactionForm.php:132", "source": {"index": 20, "namespace": null, "name": "app/Livewire/TransactionForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\TransactionForm.php", "line": 132}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=132", "ajax": false, "filename": "TransactionForm.php", "line": "132"}, "connection": "daily", "explain": null, "start_percent": 94.402, "width_percent": 2.722}, {"sql": "select `id`, `name` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null and `parent_id` is null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/CategoryForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryForm.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/helpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\helpers.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportComputed/BaseComputed.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\BaseComputed.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportComputed/BaseComputed.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\BaseComputed.php", "line": 60}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/EventBus.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\EventBus.php", "line": 60}], "start": **********.318068, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "CategoryForm.php:74", "source": {"index": 16, "namespace": null, "name": "app/Livewire/CategoryForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryForm.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryForm.php&line=74", "ajax": false, "filename": "CategoryForm.php", "line": "74"}, "connection": "daily", "explain": null, "start_percent": 97.124, "width_percent": 2.876}]}, "models": {"data": {"App\\Models\\Category": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Account": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FAccount.php&line=1", "ajax": false, "filename": "Account.php", "line": "?"}}, "App\\Models\\Tag": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FTag.php&line=1", "ajax": false, "filename": "Tag.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Transaction": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FTransaction.php&line=1", "ajax": false, "filename": "Transaction.php", "line": "?"}}}, "count": 50, "is_counter": true}, "livewire": {"data": {"transaction-form #NmKqptpiBC46pbH3royI": "array:4 [\n  \"data\" => array:20 [\n    \"account\" => App\\Models\\Account {#1631\n      #connection: \"mysql\"\n      #table: \"accounts\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 4\n        \"user_id\" => 1\n        \"type\" => \"credit_card\"\n        \"name\" => \"Credit Card\"\n        \"slug\" => \"credit-card\"\n        \"balance\" => 98456.77\n        \"initial_balance\" => 100000.0\n        \"created_at\" => \"2025-05-25 09:03:42\"\n        \"updated_at\" => \"2025-05-25 09:17:42\"\n      ]\n      #original: array:9 [\n        \"id\" => 4\n        \"user_id\" => 1\n        \"type\" => \"credit_card\"\n        \"name\" => \"Credit Card\"\n        \"slug\" => \"credit-card\"\n        \"balance\" => 98456.77\n        \"initial_balance\" => 100000.0\n        \"created_at\" => \"2025-05-25 09:03:42\"\n        \"updated_at\" => \"2025-05-25 09:17:42\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:1 [\n        \"type\" => \"App\\Enums\\AccountType\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:6 [\n        0 => \"user_id\"\n        1 => \"type\"\n        2 => \"name\"\n        3 => \"slug\"\n        4 => \"balance\"\n        5 => \"initial_balance\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"transaction\" => App\\Models\\Transaction {#1643\n      #connection: \"mysql\"\n      #table: \"transactions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:18 [\n        \"id\" => 39\n        \"account_id\" => 4\n        \"category_id\" => 13\n        \"type\" => \"credit\"\n        \"transfer_to\" => null\n        \"amount\" => 32.6\n        \"payee\" => \"Cole LLC\"\n        \"slug\" => \"cole-llc\"\n        \"date\" => \"2025-05-24\"\n        \"notes\" => \"Ab adipisci ut aperiam reiciendis iure. Consequatur quibusdam est quia. Voluptas qui sunt molestiae nobis autem. Enim et voluptate vitae beatae quo. Odit laboriosam saepe cumque cum harum consequatur. Facere voluptatibus incidunt numquam veniam similique cumque ipsum.\"\n        \"attachments\" => null\n        \"status\" => 0\n        \"is_recurring\" => 0\n        \"frequency\" => \"month\"\n        \"recurring_end\" => \"2025-06-24\"\n        \"parent_id\" => null\n        \"created_at\" => \"2025-05-25 09:03:45\"\n        \"updated_at\" => \"2025-05-25 09:03:45\"\n      ]\n      #original: array:18 [\n        \"id\" => 39\n        \"account_id\" => 4\n        \"category_id\" => 13\n        \"type\" => \"credit\"\n        \"transfer_to\" => null\n        \"amount\" => 32.6\n        \"payee\" => \"Cole LLC\"\n        \"slug\" => \"cole-llc\"\n        \"date\" => \"2025-05-24\"\n        \"notes\" => \"Ab adipisci ut aperiam reiciendis iure. Consequatur quibusdam est quia. Voluptas qui sunt molestiae nobis autem. Enim et voluptate vitae beatae quo. Odit laboriosam saepe cumque cum harum consequatur. Facere voluptatibus incidunt numquam veniam similique cumque ipsum.\"\n        \"attachments\" => null\n        \"status\" => 0\n        \"is_recurring\" => 0\n        \"frequency\" => \"month\"\n        \"recurring_end\" => \"2025-06-24\"\n        \"parent_id\" => null\n        \"created_at\" => \"2025-05-25 09:03:45\"\n        \"updated_at\" => \"2025-05-25 09:03:45\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:7 [\n        \"type\" => \"App\\Enums\\TransactionType\"\n        \"date\" => \"date\"\n        \"attachments\" => \"array\"\n        \"status\" => \"bool\"\n        \"is_recurring\" => \"bool\"\n        \"frequency\" => \"App\\Enums\\RecurringFrequency\"\n        \"recurring_end\" => \"date\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:2 [\n        \"account\" => App\\Models\\Account {#1631}\n        \"tags\" => Illuminate\\Database\\Eloquent\\Collection {#1711\n          #items: array:2 [\n            0 => App\\Models\\Tag {#1799\n              #connection: \"mysql\"\n              #table: \"tags\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 1\n                \"user_id\" => 1\n                \"name\" => \"Groceries\"\n                \"created_at\" => \"2025-05-25 09:03:46\"\n                \"updated_at\" => \"2025-05-25 09:03:46\"\n              ]\n              #original: array:7 [\n                \"id\" => 1\n                \"user_id\" => 1\n                \"name\" => \"Groceries\"\n                \"created_at\" => \"2025-05-25 09:03:46\"\n                \"updated_at\" => \"2025-05-25 09:03:46\"\n                \"pivot_transaction_id\" => 39\n                \"pivot_tag_id\" => 1\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1672\n                  #connection: \"mysql\"\n                  #table: \"tag_transaction\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"transaction_id\" => 39\n                    \"tag_id\" => 1\n                  ]\n                  #original: array:2 [\n                    \"transaction_id\" => 39\n                    \"tag_id\" => 1\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: App\\Models\\Transaction {#1643}\n                  +pivotRelated: App\\Models\\Tag {#1682\n                    #connection: \"mysql\"\n                    #table: null\n                    #primaryKey: \"id\"\n                    #keyType: \"int\"\n                    +incrementing: true\n                    #with: []\n                    #withCount: []\n                    +preventsLazyLoading: false\n                    #perPage: 15\n                    +exists: false\n                    +wasRecentlyCreated: false\n                    #escapeWhenCastingToString: false\n                    #attributes: []\n                    #original: []\n                    #changes: []\n                    #previous: []\n                    #casts: []\n                    #classCastCache: []\n                    #attributeCastCache: []\n                    #dateFormat: null\n                    #appends: []\n                    #dispatchesEvents: []\n                    #observables: []\n                    #relations: []\n                    #touches: []\n                    #relationAutoloadCallback: null\n                    #relationAutoloadContext: null\n                    +timestamps: true\n                    +usesUniqueIds: false\n                    #hidden: []\n                    #visible: []\n                    #fillable: array:1 [\n                      0 => \"name\"\n                    ]\n                    #guarded: array:1 [\n                      0 => \"*\"\n                    ]\n                  }\n                  #foreignKey: \"transaction_id\"\n                  #relatedKey: \"tag_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:1 [\n                0 => \"name\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n            }\n            1 => App\\Models\\Tag {#2196\n              #connection: \"mysql\"\n              #table: \"tags\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 3\n                \"user_id\" => 1\n                \"name\" => \"Entertainment\"\n                \"created_at\" => \"2025-05-25 09:03:46\"\n                \"updated_at\" => \"2025-05-25 09:03:46\"\n              ]\n              #original: array:7 [\n                \"id\" => 3\n                \"user_id\" => 1\n                \"name\" => \"Entertainment\"\n                \"created_at\" => \"2025-05-25 09:03:46\"\n                \"updated_at\" => \"2025-05-25 09:03:46\"\n                \"pivot_transaction_id\" => 39\n                \"pivot_tag_id\" => 3\n              ]\n              #changes: []\n              #previous: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1794\n                  #connection: \"mysql\"\n                  #table: \"tag_transaction\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [\n                    \"transaction_id\" => 39\n                    \"tag_id\" => 3\n                  ]\n                  #original: array:2 [\n                    \"transaction_id\" => 39\n                    \"tag_id\" => 3\n                  ]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: App\\Models\\Transaction {#1643}\n                  +pivotRelated: App\\Models\\Tag {#1682}\n                  #foreignKey: \"transaction_id\"\n                  #relatedKey: \"tag_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:1 [\n                0 => \"name\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:15 [\n        0 => \"account_id\"\n        1 => \"category_id\"\n        2 => \"type\"\n        3 => \"transfer_to\"\n        4 => \"amount\"\n        5 => \"payee\"\n        6 => \"slug\"\n        7 => \"date\"\n        8 => \"notes\"\n        9 => \"attachments\"\n        10 => \"status\"\n        11 => \"is_recurring\"\n        12 => \"frequency\"\n        13 => \"recurring_end\"\n        14 => \"parent_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"accounts\" => Illuminate\\Database\\Eloquent\\Collection {#1766\n      #items: array:5 [\n        0 => App\\Models\\Account {#1738\n          #connection: \"mysql\"\n          #table: \"accounts\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 4\n            \"name\" => \"Credit Card\"\n          ]\n          #original: array:2 [\n            \"id\" => 4\n            \"name\" => \"Credit Card\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"type\" => \"App\\Enums\\AccountType\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"user_id\"\n            1 => \"type\"\n            2 => \"name\"\n            3 => \"slug\"\n            4 => \"balance\"\n            5 => \"initial_balance\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        1 => App\\Models\\Account {#1739\n          #connection: \"mysql\"\n          #table: \"accounts\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 3\n            \"name\" => \"Investment\"\n          ]\n          #original: array:2 [\n            \"id\" => 3\n            \"name\" => \"Investment\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"type\" => \"App\\Enums\\AccountType\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"user_id\"\n            1 => \"type\"\n            2 => \"name\"\n            3 => \"slug\"\n            4 => \"balance\"\n            5 => \"initial_balance\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        2 => App\\Models\\Account {#1706\n          #connection: \"mysql\"\n          #table: \"accounts\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 1\n            \"name\" => \"Loan\"\n          ]\n          #original: array:2 [\n            \"id\" => 1\n            \"name\" => \"Loan\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"type\" => \"App\\Enums\\AccountType\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"user_id\"\n            1 => \"type\"\n            2 => \"name\"\n            3 => \"slug\"\n            4 => \"balance\"\n            5 => \"initial_balance\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        3 => App\\Models\\Account {#1707\n          #connection: \"mysql\"\n          #table: \"accounts\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 2\n            \"name\" => \"Loan\"\n          ]\n          #original: array:2 [\n            \"id\" => 2\n            \"name\" => \"Loan\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"type\" => \"App\\Enums\\AccountType\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"user_id\"\n            1 => \"type\"\n            2 => \"name\"\n            3 => \"slug\"\n            4 => \"balance\"\n            5 => \"initial_balance\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        4 => App\\Models\\Account {#1708\n          #connection: \"mysql\"\n          #table: \"accounts\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 5\n            \"name\" => \"Tanvir Hossen Bappy\"\n          ]\n          #original: array:2 [\n            \"id\" => 5\n            \"name\" => \"Tanvir Hossen Bappy\"\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"type\" => \"App\\Enums\\AccountType\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"user_id\"\n            1 => \"type\"\n            2 => \"name\"\n            3 => \"slug\"\n            4 => \"balance\"\n            5 => \"initial_balance\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"categories\" => array:10 [\n      0 => array:4 [\n        \"id\" => 1\n        \"name\" => \"Auto & Transport 2\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 11\n            \"user_id\" => 1\n            \"name\" => \"Car Insurance\"\n            \"parent_id\" => 1\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 12\n            \"user_id\" => 1\n            \"name\" => \"Car Payment\"\n            \"parent_id\" => 1\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      1 => array:4 [\n        \"id\" => 2\n        \"name\" => \"Food\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 13\n            \"user_id\" => 1\n            \"name\" => \"Fast Food\"\n            \"parent_id\" => 2\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 14\n            \"user_id\" => 1\n            \"name\" => \"Restaurants\"\n            \"parent_id\" => 2\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      2 => array:4 [\n        \"id\" => 4\n        \"name\" => \"Health\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 17\n            \"user_id\" => 1\n            \"name\" => \"Doctor\"\n            \"parent_id\" => 4\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 18\n            \"user_id\" => 1\n            \"name\" => \"Pharmacy\"\n            \"parent_id\" => 4\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      3 => array:4 [\n        \"id\" => 3\n        \"name\" => \"Home\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 15\n            \"user_id\" => 1\n            \"name\" => \"Mortgage\"\n            \"parent_id\" => 3\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 16\n            \"user_id\" => 1\n            \"name\" => \"Rent\"\n            \"parent_id\" => 3\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      4 => array:4 [\n        \"id\" => 5\n        \"name\" => \"Personal Care\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 19\n            \"user_id\" => 1\n            \"name\" => \"Haircut\"\n            \"parent_id\" => 5\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 20\n            \"user_id\" => 1\n            \"name\" => \"Laundry\"\n            \"parent_id\" => 5\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      5 => array:4 [\n        \"id\" => 6\n        \"name\" => \"Personal Income\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 22\n            \"user_id\" => 1\n            \"name\" => \"Bonus\"\n            \"parent_id\" => 6\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 21\n            \"user_id\" => 1\n            \"name\" => \"Paycheck\"\n            \"parent_id\" => 6\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      6 => array:4 [\n        \"id\" => 7\n        \"name\" => \"Pets\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 23\n            \"user_id\" => 1\n            \"name\" => \"Pet Food\"\n            \"parent_id\" => 7\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 24\n            \"user_id\" => 1\n            \"name\" => \"Veterinary\"\n            \"parent_id\" => 7\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      7 => array:4 [\n        \"id\" => 8\n        \"name\" => \"Shopping\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 25\n            \"user_id\" => 1\n            \"name\" => \"Clothing\"\n            \"parent_id\" => 8\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 26\n            \"user_id\" => 1\n            \"name\" => \"Gifts\"\n            \"parent_id\" => 8\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      8 => array:4 [\n        \"id\" => 9\n        \"name\" => \"Travel\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 28\n            \"user_id\" => 1\n            \"name\" => \"Airfare\"\n            \"parent_id\" => 9\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 27\n            \"user_id\" => 1\n            \"name\" => \"Hotel\"\n            \"parent_id\" => 9\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n      9 => array:4 [\n        \"id\" => 10\n        \"name\" => \"Utilities\"\n        \"parent_id\" => null\n        \"children\" => array:2 [\n          0 => array:6 [\n            \"id\" => 30\n            \"user_id\" => 1\n            \"name\" => \"Electric\"\n            \"parent_id\" => 10\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n          1 => array:6 [\n            \"id\" => 29\n            \"user_id\" => 1\n            \"name\" => \"Gas\"\n            \"parent_id\" => 10\n            \"created_at\" => \"2025-05-25T09:03:42.000000Z\"\n            \"updated_at\" => \"2025-05-25T09:03:42.000000Z\"\n          ]\n        ]\n      ]\n    ]\n    \"account_id\" => 4\n    \"transfer_to\" => null\n    \"payee\" => \"Cole LLC\"\n    \"transaction_types\" => array:5 [\n      0 => App\\Enums\\TransactionType {#1714\n        +name: \"CREDIT\"\n        +value: \"credit\"\n      }\n      1 => App\\Enums\\TransactionType {#1678\n        +name: \"DEBIT\"\n        +value: \"debit\"\n      }\n      2 => App\\Enums\\TransactionType {#1762\n        +name: \"DEPOSIT\"\n        +value: \"deposit\"\n      }\n      3 => App\\Enums\\TransactionType {#1795\n        +name: \"TRANSFER\"\n        +value: \"transfer\"\n      }\n      4 => App\\Enums\\TransactionType {#1797\n        +name: \"WITHDRAWAL\"\n        +value: \"withdrawal\"\n      }\n    ]\n    \"type\" => App\\Enums\\TransactionType {#1714}\n    \"amount\" => 32.6\n    \"category_id\" => 13\n    \"date\" => Illuminate\\Support\\Carbon @********** {#1679\n      #endOfTime: false\n      #startOfTime: false\n      #constructedObjectId: \"000000000000068f0000000000000000\"\n      -clock: null\n      #localMonthsOverflow: null\n      #localYearsOverflow: null\n      #localStrictModeEnabled: null\n      #localHumanDiffOptions: null\n      #localToStringFormat: null\n      #localSerializer: null\n      #localMacros: null\n      #localGenericMacros: null\n      #localFormatFunction: null\n      #localTranslator: null\n      #dumpProperties: array:3 [\n        0 => \"date\"\n        1 => \"timezone_type\"\n        2 => \"timezone\"\n      ]\n      #dumpLocale: null\n      #dumpDateProperties: null\n      date: 2025-05-24 00:00:00.0 UTC (+00:00)\n    }\n    \"user_tags\" => array:3 [\n      0 => \"Bills\"\n      1 => \"Entertainment\"\n      2 => \"Groceries\"\n    ]\n    \"tags\" => array:2 [\n      0 => \"Groceries\"\n      1 => \"Entertainment\"\n    ]\n    \"notes\" => \"Ab adipisci ut aperiam reiciendis iure. Consequatur quibusdam est quia. Voluptas qui sunt molestiae nobis autem. Enim et voluptate vitae beatae quo. Odit laboriosam saepe cumque cum harum consequatur. Facere voluptatibus incidunt numquam veniam similique cumque ipsum.\"\n    \"attachments\" => []\n    \"status\" => false\n    \"is_recurring\" => false\n    \"frequency\" => App\\Enums\\RecurringFrequency {#1695\n      +name: \"MONTHLY\"\n      +value: \"month\"\n    }\n    \"recurring_end\" => Illuminate\\Support\\Carbon @1750723200 {#1761\n      #endOfTime: false\n      #startOfTime: false\n      #constructedObjectId: \"00000000000006e10000000000000000\"\n      -clock: null\n      #localMonthsOverflow: null\n      #localYearsOverflow: null\n      #localStrictModeEnabled: null\n      #localHumanDiffOptions: null\n      #localToStringFormat: null\n      #localSerializer: null\n      #localMacros: null\n      #localGenericMacros: null\n      #localFormatFunction: null\n      #localTranslator: null\n      #dumpProperties: array:3 [\n        0 => \"date\"\n        1 => \"timezone_type\"\n        2 => \"timezone\"\n      ]\n      #dumpLocale: null\n      #dumpDateProperties: null\n      date: 2025-06-24 00:00:00.0 UTC (+00:00)\n    }\n  ]\n  \"name\" => \"transaction-form\"\n  \"component\" => \"App\\Livewire\\TransactionForm\"\n  \"id\" => \"NmKqptpiBC46pbH3royI\"\n]", "category-form #AUbUgA7luKrSuWqVTGmo": "array:4 [\n  \"data\" => array:4 [\n    \"show_category_form\" => false\n    \"category\" => null\n    \"parent_id\" => null\n    \"name\" => \"\"\n  ]\n  \"name\" => \"category-form\"\n  \"component\" => \"App\\Livewire\\CategoryForm\"\n  \"id\" => \"AUbUgA7luKrSuWqVTGmo\"\n]", "tag-form #xQjTztE5DoK3SoHt9mdK": "array:4 [\n  \"data\" => array:3 [\n    \"show_tag_form\" => false\n    \"tag\" => null\n    \"name\" => \"\"\n  ]\n  \"name\" => \"tag-form\"\n  \"component\" => \"App\\Livewire\\TagForm\"\n  \"id\" => \"xQjTztE5DoK3SoHt9mdK\"\n]", "file-uploader #1xhGJkMGUpDhZJ6xfpTT": "array:4 [\n  \"data\" => array:6 [\n    \"input_uuid\" => \"\"\n    \"files\" => null\n    \"uploaded_files\" => Illuminate\\Support\\Collection {#2335\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n    \"selected_file\" => \"\"\n    \"s3_path\" => \"files\"\n    \"disabled\" => false\n  ]\n  \"name\" => \"file-uploader\"\n  \"component\" => \"App\\Livewire\\FileUploader\"\n  \"id\" => \"1xhGJkMGUpDhZJ6xfpTT\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => update,\n  target => App\\Models\\Transaction(id=39),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Transaction)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1226542355 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Transaction(id=39)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">App\\Models\\Transaction(id=39)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"37 characters\">[0 =&gt; Object(App\\Models\\Transaction)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226542355\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.177273, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/transaction-form/cole-llc", "action_name": "edit-transaction", "controller_action": "App\\Livewire\\TransactionForm", "uri": "GET transaction-form/{transaction}", "controller": "App\\Livewire\\TransactionForm@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=306\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FTransactionForm.php&line=306\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/TransactionForm.php:306-309</a>", "middleware": "web, auth, can:update,transaction", "duration": "1.03s", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-765229406 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-765229406\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-886799438 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-886799438\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-825093067 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire-navigate</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/transactions</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"751 characters\">PHPSESSID=aa0mh29tpvppc77i3l7peen7g0; XSRF-TOKEN=eyJpdiI6IjRURUVvV0VXMlpESmgxL1Z0eWd1cVE9PSIsInZhbHVlIjoiWjV5blZiaG4rdVRKaHJzTGwxdUQ1dVEzWG5kQTJDanU0VUpZNzlhcFF4TmJQMXEvR1dRR1lLU2k4Wng0ZnU0MlpZN292S0FjeVI0NWdRVVBsbTJBOGFMQ2NkMzJFd3ZUUlErYVdDcEpEM3laWUtSLzJPUGd5ZGtNdmZrTVdNWjkiLCJtYWMiOiJiODIxODc4N2E2OTFmNzYwM2M5ZDczODRjYWNiZTg0NDJiZGRhNmJkYzUwMjdlY2JkY2QyYTczZjIwNDBkYjlkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkJMMUVBdkdmaDNXU1NwdVdjU29SRFE9PSIsInZhbHVlIjoiRGdVM2ozM0tQM29SdUVNWWI2QmtCR3NQeCtEME4rSFdnR2tJV2FKMksrSFg2SFQ5SGh3RVRuRmR2YW9Xc3hBcm01c2lOZWVOZlRJZUQvUUVUbUlxWE0vTmlOZVhkZ2sxUEI0Yzh4QzJZRUdPUWs3TzFsbDVTb3Q3SGR1b1RUMksiLCJtYWMiOiI5NjhhMThjNmNhYTJiYjY5N2I0Y2I4NTIxZWVhZGUxZmVjYWMyODZiZTcwYzJkZDEwZDYwMGM0Nzk2NTRiYmUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-825093067\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-231662744 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-231662744\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-706392621 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 25 May 2025 10:11:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-706392621\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-708252435 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/transactions</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708252435\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/transaction-form/cole-llc", "action_name": "edit-transaction", "controller_action": "App\\Livewire\\TransactionForm"}, "badge": null}}