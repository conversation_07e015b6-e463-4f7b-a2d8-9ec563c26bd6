{"__meta": {"id": "01JW3ETHZ5H8F4YKX9KV18T38C", "datetime": "2025-05-25 10:11:43", "utime": **********.206115, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.669731, "end": **********.20615, "duration": 0.536419153213501, "duration_str": "536ms", "measures": [{"label": "Booting", "start": **********.669731, "relative_start": 0, "end": **********.96181, "relative_end": **********.96181, "duration": 0.*****************, "duration_str": "292ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.961824, "relative_start": 0.*****************, "end": **********.206154, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "244ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.985598, "relative_start": 0.*****************, "end": **********.988833, "relative_end": **********.988833, "duration": 0.**************, "duration_str": "3.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.category-form", "start": **********.11146, "relative_start": 0.****************, "end": **********.11146, "relative_end": **********.11146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::heading", "start": **********.115224, "relative_start": 0.****************, "end": **********.115224, "relative_end": **********.115224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.116948, "relative_start": 0.***********98633, "end": **********.116948, "relative_end": **********.116948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::input.index", "start": **********.117901, "relative_start": 0.44817018508911133, "end": **********.117901, "relative_end": **********.117901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.120408, "relative_start": 0.45067715644836426, "end": **********.120408, "relative_end": **********.120408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.121732, "relative_start": 0.45200109481811523, "end": **********.121732, "relative_end": **********.121732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.12259, "relative_start": 0.45285916328430176, "end": **********.12259, "relative_end": **********.12259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::label", "start": **********.123404, "relative_start": 0.4536731243133545, "end": **********.123404, "relative_end": **********.123404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.130895, "relative_start": 0.4611639976501465, "end": **********.130895, "relative_end": **********.130895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.132216, "relative_start": 0.46248507499694824, "end": **********.132216, "relative_end": **********.132216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.133435, "relative_start": 0.46370410919189453, "end": **********.133435, "relative_end": **********.133435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.134454, "relative_start": 0.4647231101989746, "end": **********.134454, "relative_end": **********.134454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.135492, "relative_start": 0.4657611846923828, "end": **********.135492, "relative_end": **********.135492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.13652, "relative_start": 0.46678900718688965, "end": **********.13652, "relative_end": **********.13652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.137179, "relative_start": 0.46744799613952637, "end": **********.137179, "relative_end": **********.137179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.138032, "relative_start": 0.46830105781555176, "end": **********.138032, "relative_end": **********.138032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.138659, "relative_start": 0.46892809867858887, "end": **********.138659, "relative_end": **********.138659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.139309, "relative_start": 0.4695780277252197, "end": **********.139309, "relative_end": **********.139309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.140035, "relative_start": 0.470304012298584, "end": **********.140035, "relative_end": **********.140035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.140647, "relative_start": 0.4709160327911377, "end": **********.140647, "relative_end": **********.140647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.141408, "relative_start": 0.4716770648956299, "end": **********.141408, "relative_end": **********.141408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.142096, "relative_start": 0.472365140914917, "end": **********.142096, "relative_end": **********.142096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.142992, "relative_start": 0.47326111793518066, "end": **********.142992, "relative_end": **********.142992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.143613, "relative_start": 0.47388219833374023, "end": **********.143613, "relative_end": **********.143613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.144254, "relative_start": 0.47452306747436523, "end": **********.144254, "relative_end": **********.144254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.144785, "relative_start": 0.4750540256500244, "end": **********.144785, "relative_end": **********.144785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.145188, "relative_start": 0.47545719146728516, "end": **********.145188, "relative_end": **********.145188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.145662, "relative_start": 0.47593116760253906, "end": **********.145662, "relative_end": **********.145662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.146124, "relative_start": 0.4763929843902588, "end": **********.146124, "relative_end": **********.146124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.146931, "relative_start": 0.4772000312805176, "end": **********.146931, "relative_end": **********.146931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.147548, "relative_start": 0.4778170585632324, "end": **********.147548, "relative_end": **********.147548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.14821, "relative_start": 0.47847914695739746, "end": **********.14821, "relative_end": **********.14821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.148697, "relative_start": 0.47896599769592285, "end": **********.148697, "relative_end": **********.148697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.149074, "relative_start": 0.4793431758880615, "end": **********.149074, "relative_end": **********.149074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.149552, "relative_start": 0.47982120513916016, "end": **********.149552, "relative_end": **********.149552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.150009, "relative_start": 0.48027801513671875, "end": **********.150009, "relative_end": **********.150009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.15084, "relative_start": 0.4811091423034668, "end": **********.15084, "relative_end": **********.15084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.151461, "relative_start": 0.48172998428344727, "end": **********.151461, "relative_end": **********.151461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.152101, "relative_start": 0.48237013816833496, "end": **********.152101, "relative_end": **********.152101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.152605, "relative_start": 0.48287415504455566, "end": **********.152605, "relative_end": **********.152605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.152985, "relative_start": 0.48325419425964355, "end": **********.152985, "relative_end": **********.152985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.153476, "relative_start": 0.48374509811401367, "end": **********.153476, "relative_end": **********.153476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.153978, "relative_start": 0.48424720764160156, "end": **********.153978, "relative_end": **********.153978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.154826, "relative_start": 0.4850950241088867, "end": **********.154826, "relative_end": **********.154826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.155438, "relative_start": 0.48570704460144043, "end": **********.155438, "relative_end": **********.155438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.156214, "relative_start": 0.486483097076416, "end": **********.156214, "relative_end": **********.156214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.157197, "relative_start": 0.48746609687805176, "end": **********.157197, "relative_end": **********.157197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.157667, "relative_start": 0.48793601989746094, "end": **********.157667, "relative_end": **********.157667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.158201, "relative_start": 0.48847007751464844, "end": **********.158201, "relative_end": **********.158201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.158689, "relative_start": 0.48895812034606934, "end": **********.158689, "relative_end": **********.158689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.159522, "relative_start": 0.4897911548614502, "end": **********.159522, "relative_end": **********.159522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.160161, "relative_start": 0.4904301166534424, "end": **********.160161, "relative_end": **********.160161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.160801, "relative_start": 0.491070032119751, "end": **********.160801, "relative_end": **********.160801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.161296, "relative_start": 0.4915649890899658, "end": **********.161296, "relative_end": **********.161296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.161679, "relative_start": 0.49194812774658203, "end": **********.161679, "relative_end": **********.161679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.16215, "relative_start": 0.4924190044403076, "end": **********.16215, "relative_end": **********.16215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.162624, "relative_start": 0.4928929805755615, "end": **********.162624, "relative_end": **********.162624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.163445, "relative_start": 0.4937140941619873, "end": **********.163445, "relative_end": **********.163445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.164074, "relative_start": 0.4943430423736572, "end": **********.164074, "relative_end": **********.164074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.164717, "relative_start": 0.49498605728149414, "end": **********.164717, "relative_end": **********.164717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.165219, "relative_start": 0.49548816680908203, "end": **********.165219, "relative_end": **********.165219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.16559, "relative_start": 0.49585914611816406, "end": **********.16559, "relative_end": **********.16559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.166101, "relative_start": 0.4963700771331787, "end": **********.166101, "relative_end": **********.166101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.16659, "relative_start": 0.496859073638916, "end": **********.16659, "relative_end": **********.16659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.167411, "relative_start": 0.4976801872253418, "end": **********.167411, "relative_end": **********.167411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.16804, "relative_start": 0.4983091354370117, "end": **********.16804, "relative_end": **********.16804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.168703, "relative_start": 0.49897217750549316, "end": **********.168703, "relative_end": **********.168703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.169254, "relative_start": 0.4995231628417969, "end": **********.169254, "relative_end": **********.169254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.169659, "relative_start": 0.49992799758911133, "end": **********.169659, "relative_end": **********.169659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.17016, "relative_start": 0.5004291534423828, "end": **********.17016, "relative_end": **********.17016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.170645, "relative_start": 0.5009140968322754, "end": **********.170645, "relative_end": **********.170645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.index", "start": **********.171782, "relative_start": 0.5020511150360107, "end": **********.171782, "relative_end": **********.171782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.option.variants.custom", "start": **********.172464, "relative_start": 0.5027329921722412, "end": **********.172464, "relative_end": **********.172464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.index", "start": **********.173314, "relative_start": 0.5035831928253174, "end": **********.173314, "relative_end": **********.173314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.indicator.variants.check", "start": **********.17403, "relative_start": 0.5042991638183594, "end": **********.17403, "relative_end": **********.17403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.174441, "relative_start": 0.5047101974487305, "end": **********.174441, "relative_end": **********.174441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.check", "start": **********.174898, "relative_start": 0.5051670074462891, "end": **********.174898, "relative_end": **********.174898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.175398, "relative_start": 0.5056672096252441, "end": **********.175398, "relative_end": **********.175398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.index", "start": **********.176202, "relative_start": 0.5064711570739746, "end": **********.176202, "relative_end": **********.176202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.variants.listbox", "start": **********.177135, "relative_start": 0.507404088973999, "end": **********.177135, "relative_end": **********.177135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.button", "start": **********.178374, "relative_start": 0.5086431503295898, "end": **********.178374, "relative_end": **********.178374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.selected", "start": **********.179424, "relative_start": 0.5096931457519531, "end": **********.179424, "relative_end": **********.179424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.18066, "relative_start": 0.5109291076660156, "end": **********.18066, "relative_end": **********.18066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.181312, "relative_start": 0.5115811824798584, "end": **********.181312, "relative_end": **********.181312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.183122, "relative_start": 0.5133910179138184, "end": **********.183122, "relative_end": **********.183122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.183827, "relative_start": 0.5140960216522217, "end": **********.183827, "relative_end": **********.183827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.chevron-down", "start": **********.184755, "relative_start": 0.5150241851806641, "end": **********.184755, "relative_end": **********.184755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::select.options", "start": **********.185771, "relative_start": 0.5160400867462158, "end": **********.185771, "relative_end": **********.185771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-field", "start": **********.18656, "relative_start": 0.5168290138244629, "end": **********.18656, "relative_end": **********.18656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::error", "start": **********.187556, "relative_start": 0.5178251266479492, "end": **********.187556, "relative_end": **********.187556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::field", "start": **********.188126, "relative_start": 0.518395185470581, "end": **********.188126, "relative_end": **********.188126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::spacer", "start": **********.188903, "relative_start": 0.519172191619873, "end": **********.188903, "relative_end": **********.188903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.189485, "relative_start": 0.51975417137146, "end": **********.189485, "relative_end": **********.189485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.190942, "relative_start": 0.5212111473083496, "end": **********.190942, "relative_end": **********.190942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.191455, "relative_start": 0.5217239856719971, "end": **********.191455, "relative_end": **********.191455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.192011, "relative_start": 0.522280216217041, "end": **********.192011, "relative_end": **********.192011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.192414, "relative_start": 0.5226831436157227, "end": **********.192414, "relative_end": **********.192414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.19381, "relative_start": 0.5240790843963623, "end": **********.19381, "relative_end": **********.19381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.194694, "relative_start": 0.5249631404876709, "end": **********.194694, "relative_end": **********.194694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.195324, "relative_start": 0.5255930423736572, "end": **********.195324, "relative_end": **********.195324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.195799, "relative_start": 0.5260682106018066, "end": **********.195799, "relative_end": **********.195799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.loading", "start": **********.196268, "relative_start": 0.5265371799468994, "end": **********.196268, "relative_end": **********.196268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.index", "start": **********.196725, "relative_start": 0.526993989944458, "end": **********.196725, "relative_end": **********.196725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button.index", "start": **********.197927, "relative_start": 0.5281960964202881, "end": **********.197927, "relative_end": **********.197927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.index", "start": **********.199038, "relative_start": 0.5293071269989014, "end": **********.199038, "relative_end": **********.199038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::icon.x-mark", "start": **********.199474, "relative_start": 0.5297431945800781, "end": **********.199474, "relative_end": **********.199474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button-or-link", "start": **********.199883, "relative_start": 0.5301520824432373, "end": **********.199883, "relative_end": **********.199883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::with-tooltip", "start": **********.200344, "relative_start": 0.5306131839752197, "end": **********.200344, "relative_end": **********.200344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close", "start": **********.200857, "relative_start": 0.5311260223388672, "end": **********.200857, "relative_end": **********.200857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.202259, "relative_start": 0.5325281620025635, "end": **********.203361, "relative_end": **********.203361, "duration": 0.0011019706726074219, "duration_str": "1.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 29023696, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 109, "nb_templates": 109, "templates": [{"name": "1x livewire.category-form", "param_count": null, "params": [], "start": **********.111388, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/category-form.blade.phplivewire.category-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fcategory-form.blade.php&line=1", "ajax": false, "filename": "category-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.category-form"}, {"name": "1x ********************************::heading", "param_count": null, "params": [], "start": **********.115163, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.php********************************::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::heading"}, {"name": "2x ********************************::label", "param_count": null, "params": [], "start": **********.11688, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.php********************************::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::label"}, {"name": "1x ********************************::input.index", "param_count": null, "params": [], "start": **********.117827, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.php********************************::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::input.index"}, {"name": "12x ********************************::with-field", "param_count": null, "params": [], "start": **********.120348, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.php********************************::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}, "render_count": 12, "name_original": "********************************::with-field"}, {"name": "2x ********************************::error", "param_count": null, "params": [], "start": **********.121675, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.php********************************::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::error"}, {"name": "2x ********************************::field", "param_count": null, "params": [], "start": **********.12253, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.php********************************::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::field"}, {"name": "10x ********************************::select.option.index", "param_count": null, "params": [], "start": **********.130838, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/option/index.blade.php********************************::select.option.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foption%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::select.option.index"}, {"name": "10x ********************************::select.option.variants.custom", "param_count": null, "params": [], "start": **********.132159, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/option/variants/custom.blade.php********************************::select.option.variants.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foption%2Fvariants%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::select.option.variants.custom"}, {"name": "10x ********************************::select.indicator.index", "param_count": null, "params": [], "start": **********.133377, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/indicator/index.blade.php********************************::select.indicator.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findicator%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::select.indicator.index"}, {"name": "10x ********************************::select.indicator.variants.check", "param_count": null, "params": [], "start": **********.134394, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/indicator/variants/check.blade.php********************************::select.indicator.variants.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findicator%2Fvariants%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::select.indicator.variants.check"}, {"name": "12x ********************************::icon.index", "param_count": null, "params": [], "start": **********.135432, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.php********************************::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 12, "name_original": "********************************::icon.index"}, {"name": "10x ********************************::icon.check", "param_count": null, "params": [], "start": **********.136463, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/check.blade.php********************************::icon.check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcheck.blade.php&line=1", "ajax": false, "filename": "check.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::icon.check"}, {"name": "1x ********************************::select.index", "param_count": null, "params": [], "start": **********.176147, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/index.blade.php********************************::select.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.index"}, {"name": "1x ********************************::select.variants.listbox", "param_count": null, "params": [], "start": **********.17707, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/variants/listbox.blade.php********************************::select.variants.listbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fvariants%2Flistbox.blade.php&line=1", "ajax": false, "filename": "listbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.variants.listbox"}, {"name": "1x ********************************::select.button", "param_count": null, "params": [], "start": **********.178322, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/button.blade.php********************************::select.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.button"}, {"name": "1x ********************************::select.selected", "param_count": null, "params": [], "start": **********.179374, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/selected.blade.php********************************::select.selected", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Fselected.blade.php&line=1", "ajax": false, "filename": "selected.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.selected"}, {"name": "2x ********************************::icon.x-mark", "param_count": null, "params": [], "start": **********.180607, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/x-mark.blade.php********************************::icon.x-mark", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fx-mark.blade.php&line=1", "ajax": false, "filename": "x-mark.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.x-mark"}, {"name": "4x ********************************::button.index", "param_count": null, "params": [], "start": **********.181231, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.php********************************::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::button.index"}, {"name": "4x ********************************::button-or-link", "param_count": null, "params": [], "start": **********.183069, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.php********************************::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::button-or-link"}, {"name": "4x ********************************::with-tooltip", "param_count": null, "params": [], "start": **********.183774, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.php********************************::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::with-tooltip"}, {"name": "1x ********************************::icon.chevron-down", "param_count": null, "params": [], "start": **********.184701, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-down.blade.php********************************::icon.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::icon.chevron-down"}, {"name": "1x ********************************::select.options", "param_count": null, "params": [], "start": **********.185714, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/select/options.blade.php********************************::select.options", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fselect%2Foptions.blade.php&line=1", "ajax": false, "filename": "options.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::select.options"}, {"name": "1x ********************************::spacer", "param_count": null, "params": [], "start": **********.188854, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/spacer.blade.php********************************::spacer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fspacer.blade.php&line=1", "ajax": false, "filename": "spacer.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::spacer"}, {"name": "2x ********************************::modal.close", "param_count": null, "params": [], "start": **********.191962, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/close.blade.php********************************::modal.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.close"}, {"name": "2x ********************************::icon.loading", "param_count": null, "params": [], "start": **********.194642, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.php********************************::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::icon.loading"}, {"name": "1x ********************************::modal.index", "param_count": null, "params": [], "start": **********.196674, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/index.blade.php********************************::modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::modal.index"}]}, "queries": {"count": 6, "nb_statements": 5, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.019129999999999998, "accumulated_duration_str": "19.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.012502, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr' limit 1", "type": "query", "params": [], "bindings": ["kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.0223331, "duration": 0.01627, "duration_str": "16.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 85.05}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.074767, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 85.05, "width_percent": 3.241}, {"sql": "select * from `transactions` where `slug` = 'cole-llc' limit 1", "type": "query", "params": [], "bindings": ["cole-llc"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 119}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 72}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 31}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php", "line": 211}], "start": **********.0840158, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:119", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Drawer/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Drawer\\ImplicitRouteBinding.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FDrawer%2FImplicitRouteBinding.php&line=119", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "119"}, "connection": "daily", "explain": null, "start_percent": 88.291, "width_percent": 3.868}, {"sql": "select * from `accounts` where `accounts`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Policies/TransactionPolicy.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Policies\\TransactionPolicy.php", "line": 33}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 818}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 771}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 552}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 447}], "start": **********.0898588, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "TransactionPolicy.php:33", "source": {"index": 21, "namespace": null, "name": "app/Policies/TransactionPolicy.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Policies\\TransactionPolicy.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FPolicies%2FTransactionPolicy.php&line=33", "ajax": false, "filename": "TransactionPolicy.php", "line": "33"}, "connection": "daily", "explain": null, "start_percent": 92.159, "width_percent": 4.13}, {"sql": "select `id`, `name` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null and `parent_id` is null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/CategoryForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryForm.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/helpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\helpers.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportComputed/BaseComputed.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\BaseComputed.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportComputed/BaseComputed.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\BaseComputed.php", "line": 60}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/EventBus.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\EventBus.php", "line": 60}], "start": **********.126956, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "CategoryForm.php:74", "source": {"index": 16, "namespace": null, "name": "app/Livewire/CategoryForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryForm.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryForm.php&line=74", "ajax": false, "filename": "CategoryForm.php", "line": "74"}, "connection": "daily", "explain": null, "start_percent": 96.289, "width_percent": 3.711}]}, "models": {"data": {"App\\Models\\Category": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Transaction": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FTransaction.php&line=1", "ajax": false, "filename": "Transaction.php", "line": "?"}}, "App\\Models\\Account": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FAccount.php&line=1", "ajax": false, "filename": "Account.php", "line": "?"}}}, "count": 13, "is_counter": true}, "livewire": {"data": {"category-form #AUbUgA7luKrSuWqVTGmo": "array:4 [\n  \"data\" => array:4 [\n    \"show_category_form\" => false\n    \"category\" => null\n    \"parent_id\" => null\n    \"name\" => \"\"\n  ]\n  \"name\" => \"category-form\"\n  \"component\" => \"App\\Livewire\\CategoryForm\"\n  \"id\" => \"AUbUgA7luKrSuWqVTGmo\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => update,\n  target => App\\Models\\Transaction(id=39),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Transaction)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Transaction(id=39)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">App\\Models\\Transaction(id=39)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"37 characters\">[0 =&gt; Object(App\\Models\\Transaction)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.09558, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\CategoryForm@resetForm<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryForm.php&line=60\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryForm.php&line=60\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/CategoryForm.php:60-63</a>", "middleware": "web", "duration": "539ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-777048881 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-777048881\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1534301612 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"334 characters\">{&quot;data&quot;:{&quot;show_category_form&quot;:false,&quot;category&quot;:null,&quot;parent_id&quot;:null,&quot;name&quot;:&quot;&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;AUbUgA7luKrSuWqVTGmo&quot;,&quot;name&quot;:&quot;category-form&quot;,&quot;path&quot;:&quot;transaction-form\\/cole-llc&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;14f078b5b1b4458c14f98a47b6fb4508375007a97f1e65bfc4285435ec2ab785&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"9 characters\">resetForm</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1534301612\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-194020116 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">533</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/transaction-form/cole-llc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"751 characters\">PHPSESSID=aa0mh29tpvppc77i3l7peen7g0; XSRF-TOKEN=eyJpdiI6IktaSzlOcmhjUnlQWGRCdzV3ZnVvK3c9PSIsInZhbHVlIjoiN3F2MGxNb2Rpemw3ak1TVWg5elVUZ3ZIR2dzVWhFV0RFdHpwcWc4bHdTMnc4bldBMkpKUWlRNGZ2ZG92d0JtUlVpRUdoOXVzc2VNMXZqMTJjWlpWcWRlaXVvMEtRaXhpajVHSXNBaHlVN2ZWQUQvTFZKMGJrY05PMzNnR3FWdkoiLCJtYWMiOiI3ODM1Yjk5MzY3NzM2NDQzOGFhOTc4YzcyMDdjMDQxY2NmZTUxM2I2MDk1NGIxNTc0MmM0ZGYxNTkyZDYzNTgwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkN4OXZ5ZURtVFgxZExWK09VdzhnVEE9PSIsInZhbHVlIjoiMjNrQzFvcnJ5dEZnK2JkaEtaa3FGU3QyU0FlWVdDOWt0Y2h4OGFOY0FlR2VwTUdKejJQd2FnSyszN0ErT0dtSGNQZnVQbDBtSTlGd1pkZmZSZGFHejBmYXI3L09MMjhlL2dUN2dXMWhpWUVxNWxOTE83V1kyS05JbndRUVE2d1QiLCJtYWMiOiJjM2FlY2FiNjc4Mjg1MGIxZTRjYmE0YTFiOTliOGY5YTNmOTE0NjE2NTUzNmE3NDUyMDZlMmJmZTE3MDdlYzcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-194020116\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-905813317 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kPg5JrDQCOasNWOhWL3SdoOXwcapmGCATkYdBqzr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-905813317\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2060523837 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 25 May 2025 10:11:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2060523837\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1840016291 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NM6KtcnEE72z0y9G4M2rtTR3ZD12OoAKUdvbXyTT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/transaction-form/cole-llc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840016291\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}