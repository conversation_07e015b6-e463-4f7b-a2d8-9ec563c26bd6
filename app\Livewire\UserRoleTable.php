<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Models\User;
use Flux\Flux;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\WithPagination;
use Illuminate\Contracts\View\View;
use Illuminate\Contracts\Database\Eloquent\Builder;

class UserRoleTable extends Component
{
    use WithPagination;

    public string $search = '';

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    #[On('user-roles-updated')]
    public function render(): View
    {
        return view('livewire.user-role-table', [
            'users' => User::query()
                ->with('roles')
                ->when(strlen($this->search) >= 1, function (Builder $query): void {
                    $query->where('name', 'like', "%{$this->search}%")
                          ->orWhere('email', 'like', "%{$this->search}%");
                })
                ->orderBy('name')
                ->paginate(15),
        ]);
    }
}
