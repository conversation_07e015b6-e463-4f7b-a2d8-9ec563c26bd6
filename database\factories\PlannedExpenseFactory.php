<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PlannedExpense>
 */
class PlannedExpenseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => Category::first()->name,
            'slug' => Str::slug(Category::first()->name),
            'category_id' => Category::first(),
            'monthly_amount' => $this->faker->randomFloat(2, 0, 100),
        ];
    }
}
