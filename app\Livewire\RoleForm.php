<?php

declare(strict_types=1);

namespace App\Livewire;

use Flux\Flux;
use Livewire\Component;
use Spatie\Permission\Models\Role;
use Illuminate\Contracts\View\View;
use Spatie\Permission\Models\Permission;

class RoleForm extends Component
{
    public bool $show_role_form = false;

    public ?Role $role = null;

    public string $name = '';

    public string $guard_name = 'web';

    public array $permissions = [];

    protected function rules(): array
    {
        return [
            'name' => [
                'required_if:show_role_form,true',
                'string',
                'max:255',
                'unique:roles,name' . ($this->role ? ','. $this->role->id : ''),
            ],
            'guard_name' => ['required_if:show_role_form,true', 'string', 'max:255'],
            'permissions' => ['array'],
            'permissions.*' => ['integer', 'exists:permissions,id'],
        ];
    }

    protected function messages(): array
    {
        return [
            'name.unique' => 'The role name has already been taken.',
            'permissions.*.exists' => 'One or more selected permissions are invalid.',
        ];
    }

    public function mount(): void
    {
        if ($this->role) {
            $this->name = $this->role->name;
            $this->guard_name = $this->role->guard_name;

            // Clean up and get only valid permissions that still exist
            $this->cleanupRolePermissions();

            try {
                $this->permissions = $this->role->fresh()->permissions->pluck('id')->toArray();
            } catch (\Exception $e) {
                $this->permissions = [];
            }
        }
    }

    private function cleanupRolePermissions(): void
    {
        if (!$this->role) {
            return;
        }

        try {
            // Get all valid permission IDs
            $validPermissionIds = Permission::pluck('id')->toArray();

            // Get current role permission IDs
            $currentPermissionIds = $this->role->permissions->pluck('id')->toArray();

            // Find invalid permission IDs
            $invalidPermissionIds = array_diff($currentPermissionIds, $validPermissionIds);

            if (!empty($invalidPermissionIds)) {
                // Remove invalid permissions from the role
                $this->role->permissions()->detach($invalidPermissionIds);
            }
        } catch (\Exception $e) {
            // If there's any error, just continue
        }
    }

    public function submit(): void
    {
        $validated_data = $this->validate();

        if ($this->role) {
            $this->role->update([
                'name' => $validated_data['name'],
                'guard_name' => $validated_data['guard_name'],
            ]);
            $roleModel = $this->role;
        } else {
            $roleModel = Role::create([
                'name' => $validated_data['name'],
                'guard_name' => $validated_data['guard_name'],
            ]);
        }

        // Sync permissions
        if ($roleModel) {
            $roleModel->syncPermissions($validated_data['permissions']);
        }

        $this->dispatch('role-saved');

        if (!$this->role) {
            $this->reset(['name', 'guard_name', 'permissions']);
        }

        Flux::toast(
            variant: 'success',
            text: 'Role successfully ' . ($this->role ? 'updated' : 'created'),
        );

        Flux::modals()->close();
    }

    public function render(): View
    {
        return view('livewire.role-form', [
            'availablePermissions' => Permission::orderBy('name')->get(),
        ]);
    }
}
