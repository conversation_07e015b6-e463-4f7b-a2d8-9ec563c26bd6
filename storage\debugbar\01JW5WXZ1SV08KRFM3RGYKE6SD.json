{"__meta": {"id": "01JW5WXZ1SV08KRFM3RGYKE6SD", "datetime": "2025-05-26 08:56:43", "utime": **********.835404, "method": "GET", "uri": "/categories", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748249769.499892, "end": **********.835434, "duration": 34.33554196357727, "duration_str": "34.34s", "measures": [{"label": "Booting", "start": 1748249769.499892, "relative_start": 0, "end": **********.264875, "relative_end": **********.264875, "duration": 0.****************, "duration_str": "765ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.264901, "relative_start": 0.****************, "end": **********.835453, "relative_end": 1.9073486328125e-05, "duration": 33.************, "duration_str": "33.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.350782, "relative_start": 0.****************, "end": **********.359003, "relative_end": **********.359003, "duration": 0.008221149444580078, "duration_str": "8.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.category-table", "start": **********.479352, "relative_start": 0.****************, "end": **********.479352, "relative_end": **********.479352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": **********.107406, "relative_start": 5.***************, "end": **********.107406, "relative_end": **********.107406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.260781, "relative_start": 5.760889053344727, "end": **********.260781, "relative_end": **********.260781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249776.409767, "relative_start": 6.90987491607666, "end": 1748249776.409767, "relative_end": 1748249776.409767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "start": 1748249776.76163, "relative_start": 7.261738061904907, "end": 1748249776.76163, "relative_end": 1748249776.76163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249776.881617, "relative_start": 7.381725072860718, "end": 1748249776.881617, "relative_end": 1748249776.881617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249776.9747, "relative_start": 7.474807977676392, "end": 1748249776.9747, "relative_end": 1748249776.9747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249777.23573, "relative_start": 7.735837936401367, "end": 1748249777.23573, "relative_end": 1748249777.23573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": 1748249777.285107, "relative_start": 7.785214900970459, "end": 1748249777.285107, "relative_end": 1748249777.285107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249779.369558, "relative_start": 9.86966609954834, "end": 1748249779.369558, "relative_end": 1748249779.369558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "start": 1748249779.371226, "relative_start": 9.871334075927734, "end": 1748249779.371226, "relative_end": 1748249779.371226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249779.499726, "relative_start": 9.999834060668945, "end": 1748249779.499726, "relative_end": 1748249779.499726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249779.501746, "relative_start": 10.001853942871094, "end": 1748249779.501746, "relative_end": 1748249779.501746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "start": 1748249779.614999, "relative_start": 10.11510705947876, "end": 1748249779.614999, "relative_end": 1748249779.614999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249779.897269, "relative_start": 10.397377014160156, "end": 1748249779.897269, "relative_end": 1748249779.897269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249779.998253, "relative_start": 10.498361110687256, "end": 1748249779.998253, "relative_end": 1748249779.998253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249780.000571, "relative_start": 10.500679016113281, "end": 1748249780.000571, "relative_end": 1748249780.000571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249780.001823, "relative_start": 10.501930952072144, "end": 1748249780.001823, "relative_end": 1748249780.001823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": 1748249780.002743, "relative_start": 10.502851009368896, "end": 1748249780.002743, "relative_end": 1748249780.002743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": 1748249781.070687, "relative_start": 11.570795059204102, "end": 1748249781.070687, "relative_end": 1748249781.070687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": 1748249781.237543, "relative_start": 11.737651109695435, "end": 1748249781.237543, "relative_end": 1748249781.237543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.column", "start": 1748249781.238727, "relative_start": 11.738835096359253, "end": 1748249781.238727, "relative_end": 1748249781.238727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.columns", "start": 1748249781.240052, "relative_start": 11.74015998840332, "end": 1748249781.240052, "relative_end": 1748249781.240052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249781.326913, "relative_start": 11.82702112197876, "end": 1748249781.326913, "relative_end": 1748249781.326913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249781.432631, "relative_start": 11.932739019393921, "end": 1748249781.432631, "relative_end": 1748249781.432631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249781.440314, "relative_start": 11.940422058105469, "end": 1748249781.440314, "relative_end": 1748249781.440314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249781.44285, "relative_start": 11.942958116531372, "end": 1748249781.44285, "relative_end": 1748249781.44285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249781.444498, "relative_start": 11.944606065750122, "end": 1748249781.444498, "relative_end": 1748249781.444498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249781.62257, "relative_start": 12.12267804145813, "end": 1748249781.62257, "relative_end": 1748249781.62257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249781.623703, "relative_start": 12.12381100654602, "end": 1748249781.623703, "relative_end": 1748249781.623703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249781.624651, "relative_start": 12.124758958816528, "end": 1748249781.624651, "relative_end": 1748249781.624651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249781.625523, "relative_start": 12.125631093978882, "end": 1748249781.625523, "relative_end": 1748249781.625523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249781.628565, "relative_start": 12.128673076629639, "end": 1748249781.628565, "relative_end": 1748249781.628565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249781.630215, "relative_start": 12.130322933197021, "end": 1748249781.630215, "relative_end": 1748249781.630215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249781.798686, "relative_start": 12.298794031143188, "end": 1748249781.798686, "relative_end": 1748249781.798686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249781.801539, "relative_start": 12.301646947860718, "end": 1748249781.801539, "relative_end": 1748249781.801539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249781.802764, "relative_start": 12.302871942520142, "end": 1748249781.802764, "relative_end": 1748249781.802764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249781.803742, "relative_start": 12.303849935531616, "end": 1748249781.803742, "relative_end": 1748249781.803742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249781.806296, "relative_start": 12.306404113769531, "end": 1748249781.806296, "relative_end": 1748249781.806296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249782.060082, "relative_start": 12.560189962387085, "end": 1748249782.060082, "relative_end": 1748249782.060082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249782.364991, "relative_start": 12.86509895324707, "end": 1748249782.364991, "relative_end": 1748249782.364991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249782.474532, "relative_start": 12.974639892578125, "end": 1748249782.474532, "relative_end": 1748249782.474532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249782.475875, "relative_start": 12.975982904434204, "end": 1748249782.475875, "relative_end": 1748249782.475875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249782.477349, "relative_start": 12.977457046508789, "end": 1748249782.477349, "relative_end": 1748249782.477349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249782.681411, "relative_start": 13.181519031524658, "end": 1748249782.681411, "relative_end": 1748249782.681411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249782.687175, "relative_start": 13.187283039093018, "end": 1748249782.687175, "relative_end": 1748249782.687175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249782.688226, "relative_start": 13.188333988189697, "end": 1748249782.688226, "relative_end": 1748249782.688226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249782.689197, "relative_start": 13.189305067062378, "end": 1748249782.689197, "relative_end": 1748249782.689197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249782.690378, "relative_start": 13.190485954284668, "end": 1748249782.690378, "relative_end": 1748249782.690378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249782.692208, "relative_start": 13.192316055297852, "end": 1748249782.692208, "relative_end": 1748249782.692208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.408984, "relative_start": 13.90909194946289, "end": 1748249783.408984, "relative_end": 1748249783.408984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.411187, "relative_start": 13.911294937133789, "end": 1748249783.411187, "relative_end": 1748249783.411187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249783.412085, "relative_start": 13.912193059921265, "end": 1748249783.412085, "relative_end": 1748249783.412085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.412849, "relative_start": 13.912956953048706, "end": 1748249783.412849, "relative_end": 1748249783.412849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.413762, "relative_start": 13.913870096206665, "end": 1748249783.413762, "relative_end": 1748249783.413762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.414653, "relative_start": 13.914761066436768, "end": 1748249783.414653, "relative_end": 1748249783.414653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.415084, "relative_start": 13.915191888809204, "end": 1748249783.415084, "relative_end": 1748249783.415084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249783.417691, "relative_start": 13.91779899597168, "end": 1748249783.417691, "relative_end": 1748249783.417691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.504442, "relative_start": 14.004549980163574, "end": 1748249783.504442, "relative_end": 1748249783.504442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.505289, "relative_start": 14.005397081375122, "end": 1748249783.505289, "relative_end": 1748249783.505289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.506324, "relative_start": 14.006432056427002, "end": 1748249783.506324, "relative_end": 1748249783.506324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.508555, "relative_start": 14.008662939071655, "end": 1748249783.508555, "relative_end": 1748249783.508555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249783.509762, "relative_start": 14.009870052337646, "end": 1748249783.509762, "relative_end": 1748249783.509762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.510851, "relative_start": 14.010958909988403, "end": 1748249783.510851, "relative_end": 1748249783.510851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.511901, "relative_start": 14.012008905410767, "end": 1748249783.511901, "relative_end": 1748249783.511901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.5134, "relative_start": 14.013508081436157, "end": 1748249783.5134, "relative_end": 1748249783.5134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.514324, "relative_start": 14.014431953430176, "end": 1748249783.514324, "relative_end": 1748249783.514324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.518895, "relative_start": 14.019002914428711, "end": 1748249783.518895, "relative_end": 1748249783.518895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249783.519829, "relative_start": 14.01993703842163, "end": 1748249783.519829, "relative_end": 1748249783.519829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.520715, "relative_start": 14.020823001861572, "end": 1748249783.520715, "relative_end": 1748249783.520715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.521674, "relative_start": 14.021781921386719, "end": 1748249783.521674, "relative_end": 1748249783.521674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.522578, "relative_start": 14.022686004638672, "end": 1748249783.522578, "relative_end": 1748249783.522578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249783.52337, "relative_start": 14.023478031158447, "end": 1748249783.52337, "relative_end": 1748249783.52337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249783.524267, "relative_start": 14.024374961853027, "end": 1748249783.524267, "relative_end": 1748249783.524267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249783.524984, "relative_start": 14.025091886520386, "end": 1748249783.524984, "relative_end": 1748249783.524984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.526039, "relative_start": 14.02614688873291, "end": 1748249783.526039, "relative_end": 1748249783.526039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.528389, "relative_start": 14.028496980667114, "end": 1748249783.528389, "relative_end": 1748249783.528389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.5293, "relative_start": 14.029407978057861, "end": 1748249783.5293, "relative_end": 1748249783.5293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.530181, "relative_start": 14.030288934707642, "end": 1748249783.530181, "relative_end": 1748249783.530181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.530686, "relative_start": 14.030793905258179, "end": 1748249783.530686, "relative_end": 1748249783.530686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.533692, "relative_start": 14.033799886703491, "end": 1748249783.533692, "relative_end": 1748249783.533692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249783.534778, "relative_start": 14.034886121749878, "end": 1748249783.534778, "relative_end": 1748249783.534778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.535718, "relative_start": 14.035825967788696, "end": 1748249783.535718, "relative_end": 1748249783.535718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.53668, "relative_start": 14.036787986755371, "end": 1748249783.53668, "relative_end": 1748249783.53668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249783.537551, "relative_start": 14.037658929824829, "end": 1748249783.537551, "relative_end": 1748249783.537551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.538959, "relative_start": 14.039067029953003, "end": 1748249783.538959, "relative_end": 1748249783.538959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.541295, "relative_start": 14.04140305519104, "end": 1748249783.541295, "relative_end": 1748249783.541295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249783.542212, "relative_start": 14.042320013046265, "end": 1748249783.542212, "relative_end": 1748249783.542212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.543041, "relative_start": 14.0431489944458, "end": 1748249783.543041, "relative_end": 1748249783.543041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.543943, "relative_start": 14.044050931930542, "end": 1748249783.543943, "relative_end": 1748249783.543943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.54482, "relative_start": 14.044928073883057, "end": 1748249783.54482, "relative_end": 1748249783.54482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.545297, "relative_start": 14.04540491104126, "end": 1748249783.545297, "relative_end": 1748249783.545297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249783.546007, "relative_start": 14.046114921569824, "end": 1748249783.546007, "relative_end": 1748249783.546007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.546839, "relative_start": 14.046947002410889, "end": 1748249783.546839, "relative_end": 1748249783.546839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.547596, "relative_start": 14.047703981399536, "end": 1748249783.547596, "relative_end": 1748249783.547596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.550311, "relative_start": 14.050419092178345, "end": 1748249783.550311, "relative_end": 1748249783.550311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.552924, "relative_start": 14.053031921386719, "end": 1748249783.552924, "relative_end": 1748249783.552924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249783.553835, "relative_start": 14.053942918777466, "end": 1748249783.553835, "relative_end": 1748249783.553835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.554675, "relative_start": 14.05478310585022, "end": 1748249783.554675, "relative_end": 1748249783.554675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.555661, "relative_start": 14.055768966674805, "end": 1748249783.555661, "relative_end": 1748249783.555661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.559555, "relative_start": 14.05966305732727, "end": 1748249783.559555, "relative_end": 1748249783.559555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.5605, "relative_start": 14.06060791015625, "end": 1748249783.5605, "relative_end": 1748249783.5605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.562919, "relative_start": 14.063026905059814, "end": 1748249783.562919, "relative_end": 1748249783.562919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249783.56384, "relative_start": 14.063947916030884, "end": 1748249783.56384, "relative_end": 1748249783.56384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.564708, "relative_start": 14.064815998077393, "end": 1748249783.564708, "relative_end": 1748249783.564708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.567975, "relative_start": 14.068083047866821, "end": 1748249783.567975, "relative_end": 1748249783.567975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.568985, "relative_start": 14.069092988967896, "end": 1748249783.568985, "relative_end": 1748249783.568985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249783.569853, "relative_start": 14.069961071014404, "end": 1748249783.569853, "relative_end": 1748249783.569853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249783.570793, "relative_start": 14.070900917053223, "end": 1748249783.570793, "relative_end": 1748249783.570793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249783.571515, "relative_start": 14.071623086929321, "end": 1748249783.571515, "relative_end": 1748249783.571515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.572215, "relative_start": 14.072323083877563, "end": 1748249783.572215, "relative_end": 1748249783.572215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.574106, "relative_start": 14.074213981628418, "end": 1748249783.574106, "relative_end": 1748249783.574106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.574918, "relative_start": 14.075026035308838, "end": 1748249783.574918, "relative_end": 1748249783.574918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.57566, "relative_start": 14.075767993927002, "end": 1748249783.57566, "relative_end": 1748249783.57566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.576113, "relative_start": 14.076220989227295, "end": 1748249783.576113, "relative_end": 1748249783.576113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.577707, "relative_start": 14.077815055847168, "end": 1748249783.577707, "relative_end": 1748249783.577707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249783.578399, "relative_start": 14.07850694656372, "end": 1748249783.578399, "relative_end": 1748249783.578399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.579033, "relative_start": 14.079140901565552, "end": 1748249783.579033, "relative_end": 1748249783.579033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.57973, "relative_start": 14.079838037490845, "end": 1748249783.57973, "relative_end": 1748249783.57973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249783.580471, "relative_start": 14.080579042434692, "end": 1748249783.580471, "relative_end": 1748249783.580471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.581554, "relative_start": 14.081661939620972, "end": 1748249783.581554, "relative_end": 1748249783.581554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.586103, "relative_start": 14.08621096611023, "end": 1748249783.586103, "relative_end": 1748249783.586103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249783.586812, "relative_start": 14.086920022964478, "end": 1748249783.586812, "relative_end": 1748249783.586812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.587485, "relative_start": 14.087593078613281, "end": 1748249783.587485, "relative_end": 1748249783.587485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.588649, "relative_start": 14.088757038116455, "end": 1748249783.588649, "relative_end": 1748249783.588649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.589634, "relative_start": 14.089741945266724, "end": 1748249783.589634, "relative_end": 1748249783.589634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.590147, "relative_start": 14.09025502204895, "end": 1748249783.590147, "relative_end": 1748249783.590147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249783.590841, "relative_start": 14.090949058532715, "end": 1748249783.590841, "relative_end": 1748249783.590841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.591699, "relative_start": 14.091806888580322, "end": 1748249783.591699, "relative_end": 1748249783.591699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.592532, "relative_start": 14.092639923095703, "end": 1748249783.592532, "relative_end": 1748249783.592532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.594302, "relative_start": 14.094409942626953, "end": 1748249783.594302, "relative_end": 1748249783.594302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.596697, "relative_start": 14.096805095672607, "end": 1748249783.596697, "relative_end": 1748249783.596697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249783.597577, "relative_start": 14.097685098648071, "end": 1748249783.597577, "relative_end": 1748249783.597577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.598798, "relative_start": 14.09890604019165, "end": 1748249783.598798, "relative_end": 1748249783.598798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.600112, "relative_start": 14.100219964981079, "end": 1748249783.600112, "relative_end": 1748249783.600112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.601327, "relative_start": 14.10143494606018, "end": 1748249783.601327, "relative_end": 1748249783.601327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.602246, "relative_start": 14.102354049682617, "end": 1748249783.602246, "relative_end": 1748249783.602246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.604621, "relative_start": 14.104728937149048, "end": 1748249783.604621, "relative_end": 1748249783.604621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249783.605636, "relative_start": 14.105743885040283, "end": 1748249783.605636, "relative_end": 1748249783.605636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.606467, "relative_start": 14.106575012207031, "end": 1748249783.606467, "relative_end": 1748249783.606467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.607388, "relative_start": 14.1074960231781, "end": 1748249783.607388, "relative_end": 1748249783.607388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.608335, "relative_start": 14.108443021774292, "end": 1748249783.608335, "relative_end": 1748249783.608335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249783.609151, "relative_start": 14.109258890151978, "end": 1748249783.609151, "relative_end": 1748249783.609151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249783.610064, "relative_start": 14.110172033309937, "end": 1748249783.610064, "relative_end": 1748249783.610064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249783.610839, "relative_start": 14.110946893692017, "end": 1748249783.610839, "relative_end": 1748249783.610839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.611463, "relative_start": 14.111571073532104, "end": 1748249783.611463, "relative_end": 1748249783.611463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.613748, "relative_start": 14.113856077194214, "end": 1748249783.613748, "relative_end": 1748249783.613748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.614717, "relative_start": 14.114825010299683, "end": 1748249783.614717, "relative_end": 1748249783.614717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.618147, "relative_start": 14.118254899978638, "end": 1748249783.618147, "relative_end": 1748249783.618147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.618808, "relative_start": 14.118916034698486, "end": 1748249783.618808, "relative_end": 1748249783.618808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.622318, "relative_start": 14.12242603302002, "end": 1748249783.622318, "relative_end": 1748249783.622318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249783.623377, "relative_start": 14.123485088348389, "end": 1748249783.623377, "relative_end": 1748249783.623377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.62479, "relative_start": 14.124897956848145, "end": 1748249783.62479, "relative_end": 1748249783.62479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.626202, "relative_start": 14.126310110092163, "end": 1748249783.626202, "relative_end": 1748249783.626202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249783.627399, "relative_start": 14.127506971359253, "end": 1748249783.627399, "relative_end": 1748249783.627399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.62906, "relative_start": 14.129168033599854, "end": 1748249783.62906, "relative_end": 1748249783.62906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.631582, "relative_start": 14.13169002532959, "end": 1748249783.631582, "relative_end": 1748249783.631582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249783.634603, "relative_start": 14.134711027145386, "end": 1748249783.634603, "relative_end": 1748249783.634603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.635537, "relative_start": 14.135644912719727, "end": 1748249783.635537, "relative_end": 1748249783.635537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.636623, "relative_start": 14.136730909347534, "end": 1748249783.636623, "relative_end": 1748249783.636623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.63757, "relative_start": 14.137677907943726, "end": 1748249783.63757, "relative_end": 1748249783.63757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.638079, "relative_start": 14.138186931610107, "end": 1748249783.638079, "relative_end": 1748249783.638079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249783.638867, "relative_start": 14.138974905014038, "end": 1748249783.638867, "relative_end": 1748249783.638867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.639674, "relative_start": 14.139781951904297, "end": 1748249783.639674, "relative_end": 1748249783.639674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.640477, "relative_start": 14.140584945678711, "end": 1748249783.640477, "relative_end": 1748249783.640477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.642153, "relative_start": 14.142261028289795, "end": 1748249783.642153, "relative_end": 1748249783.642153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.644531, "relative_start": 14.144639015197754, "end": 1748249783.644531, "relative_end": 1748249783.644531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249783.645444, "relative_start": 14.145551919937134, "end": 1748249783.645444, "relative_end": 1748249783.645444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.646228, "relative_start": 14.146336078643799, "end": 1748249783.646228, "relative_end": 1748249783.646228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.647164, "relative_start": 14.147272109985352, "end": 1748249783.647164, "relative_end": 1748249783.647164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.648082, "relative_start": 14.148190021514893, "end": 1748249783.648082, "relative_end": 1748249783.648082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.649297, "relative_start": 14.149405002593994, "end": 1748249783.649297, "relative_end": 1748249783.649297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.652045, "relative_start": 14.152153015136719, "end": 1748249783.652045, "relative_end": 1748249783.652045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249783.653132, "relative_start": 14.153239965438843, "end": 1748249783.653132, "relative_end": 1748249783.653132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.65404, "relative_start": 14.15414810180664, "end": 1748249783.65404, "relative_end": 1748249783.65404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.654972, "relative_start": 14.155080080032349, "end": 1748249783.654972, "relative_end": 1748249783.654972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.655915, "relative_start": 14.156023025512695, "end": 1748249783.655915, "relative_end": 1748249783.655915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249783.656744, "relative_start": 14.156852006912231, "end": 1748249783.656744, "relative_end": 1748249783.656744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249783.657696, "relative_start": 14.157804012298584, "end": 1748249783.657696, "relative_end": 1748249783.657696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249783.658443, "relative_start": 14.15855097770691, "end": 1748249783.658443, "relative_end": 1748249783.658443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.658994, "relative_start": 14.159101963043213, "end": 1748249783.658994, "relative_end": 1748249783.658994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.661287, "relative_start": 14.161395072937012, "end": 1748249783.661287, "relative_end": 1748249783.661287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.662287, "relative_start": 14.162395000457764, "end": 1748249783.662287, "relative_end": 1748249783.662287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.663219, "relative_start": 14.163326978683472, "end": 1748249783.663219, "relative_end": 1748249783.663219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.663775, "relative_start": 14.163882970809937, "end": 1748249783.663775, "relative_end": 1748249783.663775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.667348, "relative_start": 14.167455911636353, "end": 1748249783.667348, "relative_end": 1748249783.667348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249783.66886, "relative_start": 14.168967962265015, "end": 1748249783.66886, "relative_end": 1748249783.66886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.670054, "relative_start": 14.170161962509155, "end": 1748249783.670054, "relative_end": 1748249783.670054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.671139, "relative_start": 14.171247005462646, "end": 1748249783.671139, "relative_end": 1748249783.671139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249783.672289, "relative_start": 14.172396898269653, "end": 1748249783.672289, "relative_end": 1748249783.672289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.673827, "relative_start": 14.173934936523438, "end": 1748249783.673827, "relative_end": 1748249783.673827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.676059, "relative_start": 14.176167011260986, "end": 1748249783.676059, "relative_end": 1748249783.676059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249783.676985, "relative_start": 14.177093029022217, "end": 1748249783.676985, "relative_end": 1748249783.676985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.677863, "relative_start": 14.177970886230469, "end": 1748249783.677863, "relative_end": 1748249783.677863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.678877, "relative_start": 14.178985118865967, "end": 1748249783.678877, "relative_end": 1748249783.678877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.679756, "relative_start": 14.179863929748535, "end": 1748249783.679756, "relative_end": 1748249783.679756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.680247, "relative_start": 14.180355072021484, "end": 1748249783.680247, "relative_end": 1748249783.680247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249783.681022, "relative_start": 14.181129932403564, "end": 1748249783.681022, "relative_end": 1748249783.681022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.685548, "relative_start": 14.185656070709229, "end": 1748249783.685548, "relative_end": 1748249783.685548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.68648, "relative_start": 14.186588048934937, "end": 1748249783.68648, "relative_end": 1748249783.68648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.688294, "relative_start": 14.188401937484741, "end": 1748249783.688294, "relative_end": 1748249783.688294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.690605, "relative_start": 14.190712928771973, "end": 1748249783.690605, "relative_end": 1748249783.690605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249783.691574, "relative_start": 14.19168210029602, "end": 1748249783.691574, "relative_end": 1748249783.691574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.692462, "relative_start": 14.192569971084595, "end": 1748249783.692462, "relative_end": 1748249783.692462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.693345, "relative_start": 14.193453073501587, "end": 1748249783.693345, "relative_end": 1748249783.693345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.694294, "relative_start": 14.194401979446411, "end": 1748249783.694294, "relative_end": 1748249783.694294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.695107, "relative_start": 14.195214986801147, "end": 1748249783.695107, "relative_end": 1748249783.695107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.697358, "relative_start": 14.197465896606445, "end": 1748249783.697358, "relative_end": 1748249783.697358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249783.698279, "relative_start": 14.198386907577515, "end": 1748249783.698279, "relative_end": 1748249783.698279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.702389, "relative_start": 14.202497005462646, "end": 1748249783.702389, "relative_end": 1748249783.702389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.703382, "relative_start": 14.203490018844604, "end": 1748249783.703382, "relative_end": 1748249783.703382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.704358, "relative_start": 14.204466104507446, "end": 1748249783.704358, "relative_end": 1748249783.704358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249783.705343, "relative_start": 14.205451011657715, "end": 1748249783.705343, "relative_end": 1748249783.705343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249783.707054, "relative_start": 14.207161903381348, "end": 1748249783.707054, "relative_end": 1748249783.707054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249783.707983, "relative_start": 14.208091020584106, "end": 1748249783.707983, "relative_end": 1748249783.707983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.708614, "relative_start": 14.208722114562988, "end": 1748249783.708614, "relative_end": 1748249783.708614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.711007, "relative_start": 14.21111512184143, "end": 1748249783.711007, "relative_end": 1748249783.711007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.711981, "relative_start": 14.21208906173706, "end": 1748249783.711981, "relative_end": 1748249783.711981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.71291, "relative_start": 14.21301794052124, "end": 1748249783.71291, "relative_end": 1748249783.71291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.71346, "relative_start": 14.213567972183228, "end": 1748249783.71346, "relative_end": 1748249783.71346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.71877, "relative_start": 14.218878030776978, "end": 1748249783.71877, "relative_end": 1748249783.71877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249783.71975, "relative_start": 14.219857931137085, "end": 1748249783.71975, "relative_end": 1748249783.71975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.720623, "relative_start": 14.220731019973755, "end": 1748249783.720623, "relative_end": 1748249783.720623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.721668, "relative_start": 14.221776008605957, "end": 1748249783.721668, "relative_end": 1748249783.721668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249783.722656, "relative_start": 14.222764015197754, "end": 1748249783.722656, "relative_end": 1748249783.722656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.724194, "relative_start": 14.224302053451538, "end": 1748249783.724194, "relative_end": 1748249783.724194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.726521, "relative_start": 14.22662901878357, "end": 1748249783.726521, "relative_end": 1748249783.726521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249783.727431, "relative_start": 14.2275390625, "end": 1748249783.727431, "relative_end": 1748249783.727431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.728241, "relative_start": 14.228348970413208, "end": 1748249783.728241, "relative_end": 1748249783.728241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.72918, "relative_start": 14.229288101196289, "end": 1748249783.72918, "relative_end": 1748249783.72918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.730086, "relative_start": 14.230194091796875, "end": 1748249783.730086, "relative_end": 1748249783.730086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.730595, "relative_start": 14.230703115463257, "end": 1748249783.730595, "relative_end": 1748249783.730595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249783.731672, "relative_start": 14.231780052185059, "end": 1748249783.731672, "relative_end": 1748249783.731672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.734481, "relative_start": 14.234589099884033, "end": 1748249783.734481, "relative_end": 1748249783.734481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.735782, "relative_start": 14.235889911651611, "end": 1748249783.735782, "relative_end": 1748249783.735782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.737591, "relative_start": 14.237699031829834, "end": 1748249783.737591, "relative_end": 1748249783.737591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.739957, "relative_start": 14.240065097808838, "end": 1748249783.739957, "relative_end": 1748249783.739957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249783.740706, "relative_start": 14.240813970565796, "end": 1748249783.740706, "relative_end": 1748249783.740706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.741477, "relative_start": 14.24158501625061, "end": 1748249783.741477, "relative_end": 1748249783.741477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.742388, "relative_start": 14.242496013641357, "end": 1748249783.742388, "relative_end": 1748249783.742388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.743449, "relative_start": 14.24355697631836, "end": 1748249783.743449, "relative_end": 1748249783.743449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.744147, "relative_start": 14.244255065917969, "end": 1748249783.744147, "relative_end": 1748249783.744147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.746406, "relative_start": 14.246514081954956, "end": 1748249783.746406, "relative_end": 1748249783.746406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249783.747655, "relative_start": 14.24776291847229, "end": 1748249783.747655, "relative_end": 1748249783.747655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.750928, "relative_start": 14.251035928726196, "end": 1748249783.750928, "relative_end": 1748249783.750928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.752496, "relative_start": 14.252604007720947, "end": 1748249783.752496, "relative_end": 1748249783.752496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.753463, "relative_start": 14.253571033477783, "end": 1748249783.753463, "relative_end": 1748249783.753463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249783.754303, "relative_start": 14.254410982131958, "end": 1748249783.754303, "relative_end": 1748249783.754303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249783.755223, "relative_start": 14.255331039428711, "end": 1748249783.755223, "relative_end": 1748249783.755223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249783.755951, "relative_start": 14.256058931350708, "end": 1748249783.755951, "relative_end": 1748249783.755951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.756544, "relative_start": 14.256652116775513, "end": 1748249783.756544, "relative_end": 1748249783.756544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.758925, "relative_start": 14.259032964706421, "end": 1748249783.758925, "relative_end": 1748249783.758925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.759923, "relative_start": 14.26003098487854, "end": 1748249783.759923, "relative_end": 1748249783.759923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.760848, "relative_start": 14.260956048965454, "end": 1748249783.760848, "relative_end": 1748249783.760848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.761397, "relative_start": 14.261504888534546, "end": 1748249783.761397, "relative_end": 1748249783.761397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.764174, "relative_start": 14.264281988143921, "end": 1748249783.764174, "relative_end": 1748249783.764174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249783.765937, "relative_start": 14.266045093536377, "end": 1748249783.765937, "relative_end": 1748249783.765937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.768592, "relative_start": 14.268699884414673, "end": 1748249783.768592, "relative_end": 1748249783.768592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.769443, "relative_start": 14.269551038742065, "end": 1748249783.769443, "relative_end": 1748249783.769443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249783.770231, "relative_start": 14.270339012145996, "end": 1748249783.770231, "relative_end": 1748249783.770231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.771304, "relative_start": 14.271411895751953, "end": 1748249783.771304, "relative_end": 1748249783.771304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.773041, "relative_start": 14.273149013519287, "end": 1748249783.773041, "relative_end": 1748249783.773041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249783.773783, "relative_start": 14.273890972137451, "end": 1748249783.773783, "relative_end": 1748249783.773783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.774422, "relative_start": 14.274529933929443, "end": 1748249783.774422, "relative_end": 1748249783.774422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.775164, "relative_start": 14.275271892547607, "end": 1748249783.775164, "relative_end": 1748249783.775164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.775952, "relative_start": 14.276060104370117, "end": 1748249783.775952, "relative_end": 1748249783.775952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.776372, "relative_start": 14.276479959487915, "end": 1748249783.776372, "relative_end": 1748249783.776372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249783.776956, "relative_start": 14.277064085006714, "end": 1748249783.776956, "relative_end": 1748249783.776956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.777564, "relative_start": 14.277672052383423, "end": 1748249783.777564, "relative_end": 1748249783.777564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.77821, "relative_start": 14.278317928314209, "end": 1748249783.77821, "relative_end": 1748249783.77821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.779947, "relative_start": 14.280055046081543, "end": 1748249783.779947, "relative_end": 1748249783.779947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.78417, "relative_start": 14.28427791595459, "end": 1748249783.78417, "relative_end": 1748249783.78417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249783.785295, "relative_start": 14.28540301322937, "end": 1748249783.785295, "relative_end": 1748249783.785295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.786218, "relative_start": 14.286325931549072, "end": 1748249783.786218, "relative_end": 1748249783.786218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.787235, "relative_start": 14.28734302520752, "end": 1748249783.787235, "relative_end": 1748249783.787235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.788103, "relative_start": 14.288211107254028, "end": 1748249783.788103, "relative_end": 1748249783.788103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.788897, "relative_start": 14.289005041122437, "end": 1748249783.788897, "relative_end": 1748249783.788897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.791072, "relative_start": 14.291179895401001, "end": 1748249783.791072, "relative_end": 1748249783.791072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249783.791931, "relative_start": 14.292038917541504, "end": 1748249783.791931, "relative_end": 1748249783.791931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.792717, "relative_start": 14.292824983596802, "end": 1748249783.792717, "relative_end": 1748249783.792717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.793625, "relative_start": 14.2937331199646, "end": 1748249783.793625, "relative_end": 1748249783.793625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.794742, "relative_start": 14.29485011100769, "end": 1748249783.794742, "relative_end": 1748249783.794742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249783.795724, "relative_start": 14.29583191871643, "end": 1748249783.795724, "relative_end": 1748249783.795724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249783.796731, "relative_start": 14.296838998794556, "end": 1748249783.796731, "relative_end": 1748249783.796731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249783.797566, "relative_start": 14.29767394065857, "end": 1748249783.797566, "relative_end": 1748249783.797566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.798199, "relative_start": 14.298306941986084, "end": 1748249783.798199, "relative_end": 1748249783.798199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.802177, "relative_start": 14.302284955978394, "end": 1748249783.802177, "relative_end": 1748249783.802177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.803255, "relative_start": 14.30336308479309, "end": 1748249783.803255, "relative_end": 1748249783.803255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.804217, "relative_start": 14.304325103759766, "end": 1748249783.804217, "relative_end": 1748249783.804217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.80481, "relative_start": 14.304918050765991, "end": 1748249783.80481, "relative_end": 1748249783.80481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.807278, "relative_start": 14.307385921478271, "end": 1748249783.807278, "relative_end": 1748249783.807278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249783.808206, "relative_start": 14.308314085006714, "end": 1748249783.808206, "relative_end": 1748249783.808206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.809081, "relative_start": 14.309189081192017, "end": 1748249783.809081, "relative_end": 1748249783.809081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.810089, "relative_start": 14.310197114944458, "end": 1748249783.810089, "relative_end": 1748249783.810089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249783.811378, "relative_start": 14.311486005783081, "end": 1748249783.811378, "relative_end": 1748249783.811378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.8128, "relative_start": 14.312907934188843, "end": 1748249783.8128, "relative_end": 1748249783.8128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.81571, "relative_start": 14.315818071365356, "end": 1748249783.81571, "relative_end": 1748249783.81571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249783.818457, "relative_start": 14.318564891815186, "end": 1748249783.818457, "relative_end": 1748249783.818457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.819299, "relative_start": 14.319406986236572, "end": 1748249783.819299, "relative_end": 1748249783.819299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.820135, "relative_start": 14.320243120193481, "end": 1748249783.820135, "relative_end": 1748249783.820135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.820861, "relative_start": 14.320969104766846, "end": 1748249783.820861, "relative_end": 1748249783.820861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.82129, "relative_start": 14.32139801979065, "end": 1748249783.82129, "relative_end": 1748249783.82129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249783.821884, "relative_start": 14.321991920471191, "end": 1748249783.821884, "relative_end": 1748249783.821884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.822874, "relative_start": 14.3229820728302, "end": 1748249783.822874, "relative_end": 1748249783.822874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.823663, "relative_start": 14.323770999908447, "end": 1748249783.823663, "relative_end": 1748249783.823663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.825463, "relative_start": 14.325571060180664, "end": 1748249783.825463, "relative_end": 1748249783.825463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.828177, "relative_start": 14.328284978866577, "end": 1748249783.828177, "relative_end": 1748249783.828177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249783.829153, "relative_start": 14.329261064529419, "end": 1748249783.829153, "relative_end": 1748249783.829153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.829988, "relative_start": 14.330096006393433, "end": 1748249783.829988, "relative_end": 1748249783.829988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.830965, "relative_start": 14.33107304573059, "end": 1748249783.830965, "relative_end": 1748249783.830965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.8346, "relative_start": 14.334707975387573, "end": 1748249783.8346, "relative_end": 1748249783.8346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.835632, "relative_start": 14.335740089416504, "end": 1748249783.835632, "relative_end": 1748249783.835632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.838069, "relative_start": 14.338176965713501, "end": 1748249783.838069, "relative_end": 1748249783.838069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249783.83895, "relative_start": 14.339057922363281, "end": 1748249783.83895, "relative_end": 1748249783.83895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.839791, "relative_start": 14.339899063110352, "end": 1748249783.839791, "relative_end": 1748249783.839791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.840661, "relative_start": 14.340769052505493, "end": 1748249783.840661, "relative_end": 1748249783.840661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.842334, "relative_start": 14.342442035675049, "end": 1748249783.842334, "relative_end": 1748249783.842334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249783.843725, "relative_start": 14.343832969665527, "end": 1748249783.843725, "relative_end": 1748249783.843725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249783.844687, "relative_start": 14.344794988632202, "end": 1748249783.844687, "relative_end": 1748249783.844687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249783.84546, "relative_start": 14.34556794166565, "end": 1748249783.84546, "relative_end": 1748249783.84546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.846041, "relative_start": 14.34614896774292, "end": 1748249783.846041, "relative_end": 1748249783.846041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.8491, "relative_start": 14.349208116531372, "end": 1748249783.8491, "relative_end": 1748249783.8491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.852072, "relative_start": 14.352180004119873, "end": 1748249783.852072, "relative_end": 1748249783.852072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.852878, "relative_start": 14.352986097335815, "end": 1748249783.852878, "relative_end": 1748249783.852878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.853344, "relative_start": 14.35345196723938, "end": 1748249783.853344, "relative_end": 1748249783.853344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.855007, "relative_start": 14.355114936828613, "end": 1748249783.855007, "relative_end": 1748249783.855007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249783.855767, "relative_start": 14.355875015258789, "end": 1748249783.855767, "relative_end": 1748249783.855767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.857233, "relative_start": 14.357341051101685, "end": 1748249783.857233, "relative_end": 1748249783.857233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.858458, "relative_start": 14.358566045761108, "end": 1748249783.858458, "relative_end": 1748249783.858458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249783.859402, "relative_start": 14.359509944915771, "end": 1748249783.859402, "relative_end": 1748249783.859402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.860871, "relative_start": 14.360979080200195, "end": 1748249783.860871, "relative_end": 1748249783.860871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.86317, "relative_start": 14.363277912139893, "end": 1748249783.86317, "relative_end": 1748249783.86317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249783.864072, "relative_start": 14.364180088043213, "end": 1748249783.864072, "relative_end": 1748249783.864072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.864944, "relative_start": 14.365051984786987, "end": 1748249783.864944, "relative_end": 1748249783.864944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.868247, "relative_start": 14.36835503578186, "end": 1748249783.868247, "relative_end": 1748249783.868247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.869251, "relative_start": 14.369359016418457, "end": 1748249783.869251, "relative_end": 1748249783.869251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.869793, "relative_start": 14.369900941848755, "end": 1748249783.869793, "relative_end": 1748249783.869793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249783.870507, "relative_start": 14.370615005493164, "end": 1748249783.870507, "relative_end": 1748249783.870507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.871293, "relative_start": 14.371401071548462, "end": 1748249783.871293, "relative_end": 1748249783.871293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.872054, "relative_start": 14.372162103652954, "end": 1748249783.872054, "relative_end": 1748249783.872054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.873651, "relative_start": 14.373759031295776, "end": 1748249783.873651, "relative_end": 1748249783.873651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.875871, "relative_start": 14.375978946685791, "end": 1748249783.875871, "relative_end": 1748249783.875871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249783.87663, "relative_start": 14.37673807144165, "end": 1748249783.87663, "relative_end": 1748249783.87663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.877268, "relative_start": 14.377376079559326, "end": 1748249783.877268, "relative_end": 1748249783.877268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.878012, "relative_start": 14.378119945526123, "end": 1748249783.878012, "relative_end": 1748249783.878012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.878719, "relative_start": 14.378827095031738, "end": 1748249783.878719, "relative_end": 1748249783.878719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.879403, "relative_start": 14.37951111793518, "end": 1748249783.879403, "relative_end": 1748249783.879403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.881282, "relative_start": 14.38139009475708, "end": 1748249783.881282, "relative_end": 1748249783.881282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249783.884995, "relative_start": 14.385102987289429, "end": 1748249783.884995, "relative_end": 1748249783.884995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.886086, "relative_start": 14.386193990707397, "end": 1748249783.886086, "relative_end": 1748249783.886086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.887007, "relative_start": 14.387115001678467, "end": 1748249783.887007, "relative_end": 1748249783.887007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.888074, "relative_start": 14.388181924819946, "end": 1748249783.888074, "relative_end": 1748249783.888074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249783.888932, "relative_start": 14.389039993286133, "end": 1748249783.888932, "relative_end": 1748249783.888932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249783.890047, "relative_start": 14.39015507698059, "end": 1748249783.890047, "relative_end": 1748249783.890047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249783.890847, "relative_start": 14.390954971313477, "end": 1748249783.890847, "relative_end": 1748249783.890847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.891368, "relative_start": 14.391475915908813, "end": 1748249783.891368, "relative_end": 1748249783.891368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.893036, "relative_start": 14.393143892288208, "end": 1748249783.893036, "relative_end": 1748249783.893036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.893858, "relative_start": 14.39396595954895, "end": 1748249783.893858, "relative_end": 1748249783.893858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.894602, "relative_start": 14.394710063934326, "end": 1748249783.894602, "relative_end": 1748249783.894602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.895026, "relative_start": 14.395133972167969, "end": 1748249783.895026, "relative_end": 1748249783.895026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.896695, "relative_start": 14.39680290222168, "end": 1748249783.896695, "relative_end": 1748249783.896695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249783.897378, "relative_start": 14.397485971450806, "end": 1748249783.897378, "relative_end": 1748249783.897378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.898009, "relative_start": 14.398117065429688, "end": 1748249783.898009, "relative_end": 1748249783.898009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.901537, "relative_start": 14.401644945144653, "end": 1748249783.901537, "relative_end": 1748249783.901537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249783.902611, "relative_start": 14.402719020843506, "end": 1748249783.902611, "relative_end": 1748249783.902611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.904188, "relative_start": 14.404295921325684, "end": 1748249783.904188, "relative_end": 1748249783.904188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.905997, "relative_start": 14.406105041503906, "end": 1748249783.905997, "relative_end": 1748249783.905997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249783.906887, "relative_start": 14.406995058059692, "end": 1748249783.906887, "relative_end": 1748249783.906887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.907707, "relative_start": 14.407814979553223, "end": 1748249783.907707, "relative_end": 1748249783.907707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.90863, "relative_start": 14.408737897872925, "end": 1748249783.90863, "relative_end": 1748249783.90863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.909566, "relative_start": 14.409673929214478, "end": 1748249783.909566, "relative_end": 1748249783.909566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.91007, "relative_start": 14.410177946090698, "end": 1748249783.91007, "relative_end": 1748249783.91007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249783.910821, "relative_start": 14.410928964614868, "end": 1748249783.910821, "relative_end": 1748249783.910821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.911613, "relative_start": 14.411720991134644, "end": 1748249783.911613, "relative_end": 1748249783.911613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.912405, "relative_start": 14.412513017654419, "end": 1748249783.912405, "relative_end": 1748249783.912405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.914169, "relative_start": 14.414277076721191, "end": 1748249783.914169, "relative_end": 1748249783.914169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.917484, "relative_start": 14.41759204864502, "end": 1748249783.917484, "relative_end": 1748249783.917484, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249783.918532, "relative_start": 14.418639898300171, "end": 1748249783.918532, "relative_end": 1748249783.918532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.91942, "relative_start": 14.419528007507324, "end": 1748249783.91942, "relative_end": 1748249783.91942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.920593, "relative_start": 14.420701026916504, "end": 1748249783.920593, "relative_end": 1748249783.920593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.921597, "relative_start": 14.4217050075531, "end": 1748249783.921597, "relative_end": 1748249783.921597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.922401, "relative_start": 14.422508955001831, "end": 1748249783.922401, "relative_end": 1748249783.922401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.924745, "relative_start": 14.424853086471558, "end": 1748249783.924745, "relative_end": 1748249783.924745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249783.925476, "relative_start": 14.425584077835083, "end": 1748249783.925476, "relative_end": 1748249783.925476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.926091, "relative_start": 14.426198959350586, "end": 1748249783.926091, "relative_end": 1748249783.926091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.926772, "relative_start": 14.426880121231079, "end": 1748249783.926772, "relative_end": 1748249783.926772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.92764, "relative_start": 14.427747964859009, "end": 1748249783.92764, "relative_end": 1748249783.92764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249783.928692, "relative_start": 14.428800106048584, "end": 1748249783.928692, "relative_end": 1748249783.928692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249783.929931, "relative_start": 14.430038928985596, "end": 1748249783.929931, "relative_end": 1748249783.929931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249783.930726, "relative_start": 14.4308340549469, "end": 1748249783.930726, "relative_end": 1748249783.930726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.931309, "relative_start": 14.431416988372803, "end": 1748249783.931309, "relative_end": 1748249783.931309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.935333, "relative_start": 14.435441017150879, "end": 1748249783.935333, "relative_end": 1748249783.935333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.936613, "relative_start": 14.436721086502075, "end": 1748249783.936613, "relative_end": 1748249783.936613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.937441, "relative_start": 14.437549114227295, "end": 1748249783.937441, "relative_end": 1748249783.937441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.937904, "relative_start": 14.438011884689331, "end": 1748249783.937904, "relative_end": 1748249783.937904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.939874, "relative_start": 14.439981937408447, "end": 1748249783.939874, "relative_end": 1748249783.939874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249783.940597, "relative_start": 14.440705060958862, "end": 1748249783.940597, "relative_end": 1748249783.940597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.941284, "relative_start": 14.441391944885254, "end": 1748249783.941284, "relative_end": 1748249783.941284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.942048, "relative_start": 14.442156076431274, "end": 1748249783.942048, "relative_end": 1748249783.942048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249783.942787, "relative_start": 14.44289493560791, "end": 1748249783.942787, "relative_end": 1748249783.942787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.944017, "relative_start": 14.444124937057495, "end": 1748249783.944017, "relative_end": 1748249783.944017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.946045, "relative_start": 14.446152925491333, "end": 1748249783.946045, "relative_end": 1748249783.946045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249783.946781, "relative_start": 14.44688892364502, "end": 1748249783.946781, "relative_end": 1748249783.946781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.947488, "relative_start": 14.447596073150635, "end": 1748249783.947488, "relative_end": 1748249783.947488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.948273, "relative_start": 14.448380947113037, "end": 1748249783.948273, "relative_end": 1748249783.948273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.952046, "relative_start": 14.45215392112732, "end": 1748249783.952046, "relative_end": 1748249783.952046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.952459, "relative_start": 14.452567100524902, "end": 1748249783.952459, "relative_end": 1748249783.952459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249783.952991, "relative_start": 14.453099012374878, "end": 1748249783.952991, "relative_end": 1748249783.952991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.953565, "relative_start": 14.453672885894775, "end": 1748249783.953565, "relative_end": 1748249783.953565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.95408, "relative_start": 14.454188108444214, "end": 1748249783.95408, "relative_end": 1748249783.95408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.955172, "relative_start": 14.455280065536499, "end": 1748249783.955172, "relative_end": 1748249783.955172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.956651, "relative_start": 14.456758975982666, "end": 1748249783.956651, "relative_end": 1748249783.956651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249783.957248, "relative_start": 14.457355976104736, "end": 1748249783.957248, "relative_end": 1748249783.957248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.957801, "relative_start": 14.457909107208252, "end": 1748249783.957801, "relative_end": 1748249783.957801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.958473, "relative_start": 14.45858097076416, "end": 1748249783.958473, "relative_end": 1748249783.958473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.959076, "relative_start": 14.459183931350708, "end": 1748249783.959076, "relative_end": 1748249783.959076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.959664, "relative_start": 14.459772109985352, "end": 1748249783.959664, "relative_end": 1748249783.959664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.961136, "relative_start": 14.461244106292725, "end": 1748249783.961136, "relative_end": 1748249783.961136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249783.961746, "relative_start": 14.461853981018066, "end": 1748249783.961746, "relative_end": 1748249783.961746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.962334, "relative_start": 14.46244192123413, "end": 1748249783.962334, "relative_end": 1748249783.962334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.962994, "relative_start": 14.463102102279663, "end": 1748249783.962994, "relative_end": 1748249783.962994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.963608, "relative_start": 14.46371603012085, "end": 1748249783.963608, "relative_end": 1748249783.963608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249783.964174, "relative_start": 14.464282035827637, "end": 1748249783.964174, "relative_end": 1748249783.964174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249783.964794, "relative_start": 14.4649019241333, "end": 1748249783.964794, "relative_end": 1748249783.964794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249783.968198, "relative_start": 14.468306064605713, "end": 1748249783.968198, "relative_end": 1748249783.968198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.968838, "relative_start": 14.468945980072021, "end": 1748249783.968838, "relative_end": 1748249783.968838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.971071, "relative_start": 14.471179008483887, "end": 1748249783.971071, "relative_end": 1748249783.971071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.97195, "relative_start": 14.472058057785034, "end": 1748249783.97195, "relative_end": 1748249783.97195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.972779, "relative_start": 14.47288703918457, "end": 1748249783.972779, "relative_end": 1748249783.972779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.973171, "relative_start": 14.473278999328613, "end": 1748249783.973171, "relative_end": 1748249783.973171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.974735, "relative_start": 14.47484302520752, "end": 1748249783.974735, "relative_end": 1748249783.974735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249783.97555, "relative_start": 14.475657939910889, "end": 1748249783.97555, "relative_end": 1748249783.97555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.976282, "relative_start": 14.47638988494873, "end": 1748249783.976282, "relative_end": 1748249783.976282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.977107, "relative_start": 14.477215051651001, "end": 1748249783.977107, "relative_end": 1748249783.977107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249783.978006, "relative_start": 14.478113889694214, "end": 1748249783.978006, "relative_end": 1748249783.978006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.979351, "relative_start": 14.479459047317505, "end": 1748249783.979351, "relative_end": 1748249783.979351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.981431, "relative_start": 14.481539011001587, "end": 1748249783.981431, "relative_end": 1748249783.981431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249783.98368, "relative_start": 14.483788013458252, "end": 1748249783.98368, "relative_end": 1748249783.98368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.984787, "relative_start": 14.48489499092102, "end": 1748249783.984787, "relative_end": 1748249783.984787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.985864, "relative_start": 14.485971927642822, "end": 1748249783.985864, "relative_end": 1748249783.985864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249783.987089, "relative_start": 14.487196922302246, "end": 1748249783.987089, "relative_end": 1748249783.987089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.987557, "relative_start": 14.487664937973022, "end": 1748249783.987557, "relative_end": 1748249783.987557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249783.988171, "relative_start": 14.488279104232788, "end": 1748249783.988171, "relative_end": 1748249783.988171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.988748, "relative_start": 14.488856077194214, "end": 1748249783.988748, "relative_end": 1748249783.988748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249783.98931, "relative_start": 14.489418029785156, "end": 1748249783.98931, "relative_end": 1748249783.98931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.990624, "relative_start": 14.490731954574585, "end": 1748249783.990624, "relative_end": 1748249783.990624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249783.992898, "relative_start": 14.493005990982056, "end": 1748249783.992898, "relative_end": 1748249783.992898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249783.994133, "relative_start": 14.494240999221802, "end": 1748249783.994133, "relative_end": 1748249783.994133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249783.995188, "relative_start": 14.495296001434326, "end": 1748249783.995188, "relative_end": 1748249783.995188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249783.996185, "relative_start": 14.496293067932129, "end": 1748249783.996185, "relative_end": 1748249783.996185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249783.997372, "relative_start": 14.497479915618896, "end": 1748249783.997372, "relative_end": 1748249783.997372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249783.998885, "relative_start": 14.498992919921875, "end": 1748249783.998885, "relative_end": 1748249783.998885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249784.00285, "relative_start": 14.502958059310913, "end": 1748249784.00285, "relative_end": 1748249784.00285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249784.003785, "relative_start": 14.50389289855957, "end": 1748249784.003785, "relative_end": 1748249784.003785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.004582, "relative_start": 14.504689931869507, "end": 1748249784.004582, "relative_end": 1748249784.004582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.005511, "relative_start": 14.505619049072266, "end": 1748249784.005511, "relative_end": 1748249784.005511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249784.006397, "relative_start": 14.506505012512207, "end": 1748249784.006397, "relative_end": 1748249784.006397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249784.007163, "relative_start": 14.50727105140686, "end": 1748249784.007163, "relative_end": 1748249784.007163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249784.008002, "relative_start": 14.508110046386719, "end": 1748249784.008002, "relative_end": 1748249784.008002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249784.008734, "relative_start": 14.50884199142456, "end": 1748249784.008734, "relative_end": 1748249784.008734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249784.009344, "relative_start": 14.509452104568481, "end": 1748249784.009344, "relative_end": 1748249784.009344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.01155, "relative_start": 14.511657953262329, "end": 1748249784.01155, "relative_end": 1748249784.01155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.012458, "relative_start": 14.512566089630127, "end": 1748249784.012458, "relative_end": 1748249784.012458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249784.013918, "relative_start": 14.514025926589966, "end": 1748249784.013918, "relative_end": 1748249784.013918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249784.014541, "relative_start": 14.514648914337158, "end": 1748249784.014541, "relative_end": 1748249784.014541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249784.020649, "relative_start": 14.520756959915161, "end": 1748249784.020649, "relative_end": 1748249784.020649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249784.021616, "relative_start": 14.521723985671997, "end": 1748249784.021616, "relative_end": 1748249784.021616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.022392, "relative_start": 14.522500038146973, "end": 1748249784.022392, "relative_end": 1748249784.022392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.02306, "relative_start": 14.523168087005615, "end": 1748249784.02306, "relative_end": 1748249784.02306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249784.023714, "relative_start": 14.52382206916809, "end": 1748249784.023714, "relative_end": 1748249784.023714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249784.024656, "relative_start": 14.524764060974121, "end": 1748249784.024656, "relative_end": 1748249784.024656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249784.02618, "relative_start": 14.526288032531738, "end": 1748249784.02618, "relative_end": 1748249784.02618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249784.026802, "relative_start": 14.526910066604614, "end": 1748249784.026802, "relative_end": 1748249784.026802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.02738, "relative_start": 14.527487993240356, "end": 1748249784.02738, "relative_end": 1748249784.02738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.027984, "relative_start": 14.52809190750122, "end": 1748249784.027984, "relative_end": 1748249784.027984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249784.028594, "relative_start": 14.528702020645142, "end": 1748249784.028594, "relative_end": 1748249784.028594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249784.028939, "relative_start": 14.529047012329102, "end": 1748249784.028939, "relative_end": 1748249784.028939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249784.02999, "relative_start": 14.530097961425781, "end": 1748249784.02999, "relative_end": 1748249784.02999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249784.030853, "relative_start": 14.530961036682129, "end": 1748249784.030853, "relative_end": 1748249784.030853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249784.03161, "relative_start": 14.531718015670776, "end": 1748249784.03161, "relative_end": 1748249784.03161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249784.035103, "relative_start": 14.535211086273193, "end": 1748249784.035103, "relative_end": 1748249784.035103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249784.036739, "relative_start": 14.536847114562988, "end": 1748249784.036739, "relative_end": 1748249784.036739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249784.037444, "relative_start": 14.537552118301392, "end": 1748249784.037444, "relative_end": 1748249784.037444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.038116, "relative_start": 14.5382239818573, "end": 1748249784.038116, "relative_end": 1748249784.038116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.038867, "relative_start": 14.53897500038147, "end": 1748249784.038867, "relative_end": 1748249784.038867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249784.039531, "relative_start": 14.539638996124268, "end": 1748249784.039531, "relative_end": 1748249784.039531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249784.040143, "relative_start": 14.540251016616821, "end": 1748249784.040143, "relative_end": 1748249784.040143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249784.041869, "relative_start": 14.541976928710938, "end": 1748249784.041869, "relative_end": 1748249784.041869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249784.042616, "relative_start": 14.542723894119263, "end": 1748249784.042616, "relative_end": 1748249784.042616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.043235, "relative_start": 14.54334306716919, "end": 1748249784.043235, "relative_end": 1748249784.043235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.043842, "relative_start": 14.543950080871582, "end": 1748249784.043842, "relative_end": 1748249784.043842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249784.044445, "relative_start": 14.54455304145813, "end": 1748249784.044445, "relative_end": 1748249784.044445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249784.044954, "relative_start": 14.545062065124512, "end": 1748249784.044954, "relative_end": 1748249784.044954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249784.045884, "relative_start": 14.545991897583008, "end": 1748249784.045884, "relative_end": 1748249784.045884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249784.046409, "relative_start": 14.54651689529419, "end": 1748249784.046409, "relative_end": 1748249784.046409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249784.046781, "relative_start": 14.546889066696167, "end": 1748249784.046781, "relative_end": 1748249784.046781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.048131, "relative_start": 14.54823899269104, "end": 1748249784.048131, "relative_end": 1748249784.048131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.049959, "relative_start": 14.550066947937012, "end": 1748249784.049959, "relative_end": 1748249784.049959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249784.050882, "relative_start": 14.550990104675293, "end": 1748249784.050882, "relative_end": 1748249784.050882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249784.051333, "relative_start": 14.551440954208374, "end": 1748249784.051333, "relative_end": 1748249784.051333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249784.052845, "relative_start": 14.552953004837036, "end": 1748249784.052845, "relative_end": 1748249784.052845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249784.053418, "relative_start": 14.553525924682617, "end": 1748249784.053418, "relative_end": 1748249784.053418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.05394, "relative_start": 14.55404806137085, "end": 1748249784.05394, "relative_end": 1748249784.05394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.054536, "relative_start": 14.554644107818604, "end": 1748249784.054536, "relative_end": 1748249784.054536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249784.05515, "relative_start": 14.55525803565979, "end": 1748249784.05515, "relative_end": 1748249784.05515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249784.056013, "relative_start": 14.556121110916138, "end": 1748249784.056013, "relative_end": 1748249784.056013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249784.057406, "relative_start": 14.557513952255249, "end": 1748249784.057406, "relative_end": 1748249784.057406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249784.057952, "relative_start": 14.558059930801392, "end": 1748249784.057952, "relative_end": 1748249784.057952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.058474, "relative_start": 14.558582067489624, "end": 1748249784.058474, "relative_end": 1748249784.058474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.059063, "relative_start": 14.559170961380005, "end": 1748249784.059063, "relative_end": 1748249784.059063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249784.059623, "relative_start": 14.559731006622314, "end": 1748249784.059623, "relative_end": 1748249784.059623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249784.059956, "relative_start": 14.56006407737732, "end": 1748249784.059956, "relative_end": 1748249784.059956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249784.060435, "relative_start": 14.560543060302734, "end": 1748249784.060435, "relative_end": 1748249784.060435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249784.06095, "relative_start": 14.561058044433594, "end": 1748249784.06095, "relative_end": 1748249784.06095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249784.061836, "relative_start": 14.561944007873535, "end": 1748249784.061836, "relative_end": 1748249784.061836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249784.062724, "relative_start": 14.562832117080688, "end": 1748249784.062724, "relative_end": 1748249784.062724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249784.064744, "relative_start": 14.564851999282837, "end": 1748249784.064744, "relative_end": 1748249784.064744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "start": 1748249784.066205, "relative_start": 14.566313028335571, "end": 1748249784.066205, "relative_end": 1748249784.066205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.067137, "relative_start": 14.56724500656128, "end": 1748249784.067137, "relative_end": 1748249784.067137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.067992, "relative_start": 14.568099975585938, "end": 1748249784.067992, "relative_end": 1748249784.067992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249784.068599, "relative_start": 14.56870698928833, "end": 1748249784.068599, "relative_end": 1748249784.068599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249784.069145, "relative_start": 14.569252967834473, "end": 1748249784.069145, "relative_end": 1748249784.069145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249784.070468, "relative_start": 14.570575952529907, "end": 1748249784.070468, "relative_end": 1748249784.070468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "start": 1748249784.071014, "relative_start": 14.57112193107605, "end": 1748249784.071014, "relative_end": 1748249784.071014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.071541, "relative_start": 14.571649074554443, "end": 1748249784.071541, "relative_end": 1748249784.071541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.072139, "relative_start": 14.57224702835083, "end": 1748249784.072139, "relative_end": 1748249784.072139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "start": 1748249784.072693, "relative_start": 14.572801113128662, "end": 1748249784.072693, "relative_end": 1748249784.072693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249784.07321, "relative_start": 14.573318004608154, "end": 1748249784.07321, "relative_end": 1748249784.07321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "start": 1748249784.073782, "relative_start": 14.573889970779419, "end": 1748249784.073782, "relative_end": 1748249784.073782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249784.07427, "relative_start": 14.57437801361084, "end": 1748249784.07427, "relative_end": 1748249784.07427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249784.074638, "relative_start": 14.574745893478394, "end": 1748249784.074638, "relative_end": 1748249784.074638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.075924, "relative_start": 14.576031923294067, "end": 1748249784.075924, "relative_end": 1748249784.075924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.076519, "relative_start": 14.576627016067505, "end": 1748249784.076519, "relative_end": 1748249784.076519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249784.077183, "relative_start": 14.577291011810303, "end": 1748249784.077183, "relative_end": 1748249784.077183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249784.07764, "relative_start": 14.57774806022644, "end": 1748249784.07764, "relative_end": 1748249784.07764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249784.079076, "relative_start": 14.57918405532837, "end": 1748249784.079076, "relative_end": 1748249784.079076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249784.079649, "relative_start": 14.57975697517395, "end": 1748249784.079649, "relative_end": 1748249784.079649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.080192, "relative_start": 14.580300092697144, "end": 1748249784.080192, "relative_end": 1748249784.080192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.080773, "relative_start": 14.580881118774414, "end": 1748249784.080773, "relative_end": 1748249784.080773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249784.081357, "relative_start": 14.581465005874634, "end": 1748249784.081357, "relative_end": 1748249784.081357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249784.08473, "relative_start": 14.584837913513184, "end": 1748249784.08473, "relative_end": 1748249784.08473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249784.086941, "relative_start": 14.587049007415771, "end": 1748249784.086941, "relative_end": 1748249784.086941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249784.087679, "relative_start": 14.58778691291809, "end": 1748249784.087679, "relative_end": 1748249784.087679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249784.088347, "relative_start": 14.588454961776733, "end": 1748249784.088347, "relative_end": 1748249784.088347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249784.089137, "relative_start": 14.589245080947876, "end": 1748249784.089137, "relative_end": 1748249784.089137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249784.090005, "relative_start": 14.590112924575806, "end": 1748249784.090005, "relative_end": 1748249784.090005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.cell", "start": 1748249784.090616, "relative_start": 14.590723991394043, "end": 1748249784.090616, "relative_end": 1748249784.090616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.row", "start": 1748249784.091489, "relative_start": 14.591597080230713, "end": 1748249784.091489, "relative_end": 1748249784.091489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.rows", "start": 1748249784.092276, "relative_start": 14.592384099960327, "end": 1748249784.092276, "relative_end": 1748249784.092276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.table.index", "start": 1748249784.151481, "relative_start": 14.651588916778564, "end": 1748249784.151481, "relative_end": 1748249784.151481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": 1748249784.220116, "relative_start": 14.720223903656006, "end": 1748249784.220116, "relative_end": 1748249784.220116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": 1748249784.451042, "relative_start": 14.951149940490723, "end": 1748249784.451042, "relative_end": 1748249784.451042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "start": 1748249784.770581, "relative_start": 15.270689010620117, "end": 1748249784.770581, "relative_end": 1748249784.770581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.category-form", "start": 1748249784.813287, "relative_start": 15.313395023345947, "end": 1748249784.813287, "relative_end": 1748249784.813287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "start": 1748249787.510858, "relative_start": 18.010966062545776, "end": 1748249787.510858, "relative_end": 1748249787.510858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249787.512648, "relative_start": 18.01275610923767, "end": 1748249787.512648, "relative_end": 1748249787.512648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "start": 1748249787.596752, "relative_start": 18.0968599319458, "end": 1748249787.596752, "relative_end": 1748249787.596752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": 1748249787.601113, "relative_start": 18.101221084594727, "end": 1748249787.601113, "relative_end": 1748249787.601113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249787.602313, "relative_start": 18.102421045303345, "end": 1748249787.602313, "relative_end": 1748249787.602313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249787.862707, "relative_start": 18.362814903259277, "end": 1748249787.862707, "relative_end": 1748249787.862707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::label", "start": 1748249787.935164, "relative_start": 18.435271978378296, "end": 1748249787.935164, "relative_end": 1748249787.935164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249787.940759, "relative_start": 18.440866947174072, "end": 1748249787.940759, "relative_end": 1748249787.940759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249788.007401, "relative_start": 18.507508993148804, "end": 1748249788.007401, "relative_end": 1748249788.007401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249788.00814, "relative_start": 18.50824809074402, "end": 1748249788.00814, "relative_end": 1748249788.00814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249788.00894, "relative_start": 18.509047985076904, "end": 1748249788.00894, "relative_end": 1748249788.00894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249788.00971, "relative_start": 18.509818077087402, "end": 1748249788.00971, "relative_end": 1748249788.00971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249788.010511, "relative_start": 18.510618925094604, "end": 1748249788.010511, "relative_end": 1748249788.010511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249788.011277, "relative_start": 18.511384963989258, "end": 1748249788.011277, "relative_end": 1748249788.011277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249788.011999, "relative_start": 18.512106895446777, "end": 1748249788.011999, "relative_end": 1748249788.011999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249788.012871, "relative_start": 18.51297903060913, "end": 1748249788.012871, "relative_end": 1748249788.012871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.option", "start": 1748249788.01372, "relative_start": 18.51382803916931, "end": 1748249788.01372, "relative_end": 1748249788.01372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": 1748249788.017133, "relative_start": 18.51724100112915, "end": 1748249788.017133, "relative_end": 1748249788.017133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::error", "start": 1748249788.140311, "relative_start": 18.640419006347656, "end": 1748249788.140311, "relative_end": 1748249788.140311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::field", "start": 1748249788.141071, "relative_start": 18.641179084777832, "end": 1748249788.141071, "relative_end": 1748249788.141071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249788.141659, "relative_start": 18.641767024993896, "end": 1748249788.141659, "relative_end": 1748249788.141659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249788.142136, "relative_start": 18.64224410057068, "end": 1748249788.142136, "relative_end": 1748249788.142136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249788.144429, "relative_start": 18.6445369720459, "end": 1748249788.144429, "relative_end": 1748249788.144429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249788.145138, "relative_start": 18.645246028900146, "end": 1748249788.145138, "relative_end": 1748249788.145138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249788.145805, "relative_start": 18.645912885665894, "end": 1748249788.145805, "relative_end": 1748249788.145805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249788.146176, "relative_start": 18.646284103393555, "end": 1748249788.146176, "relative_end": 1748249788.146176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249788.149461, "relative_start": 18.649569034576416, "end": 1748249788.149461, "relative_end": 1748249788.149461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249788.150145, "relative_start": 18.65025305747986, "end": 1748249788.150145, "relative_end": 1748249788.150145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249788.150974, "relative_start": 18.651082038879395, "end": 1748249788.150974, "relative_end": 1748249788.150974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249788.152111, "relative_start": 18.65221905708313, "end": 1748249788.152111, "relative_end": 1748249788.152111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "start": 1748249788.152821, "relative_start": 18.652929067611694, "end": 1748249788.152821, "relative_end": 1748249788.152821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "start": 1748249788.154547, "relative_start": 18.65465497970581, "end": 1748249788.154547, "relative_end": 1748249788.154547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249788.155651, "relative_start": 18.65575909614563, "end": 1748249788.155651, "relative_end": 1748249788.155651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249788.157065, "relative_start": 18.657172918319702, "end": 1748249788.157065, "relative_end": 1748249788.157065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249788.158151, "relative_start": 18.65825891494751, "end": 1748249788.158151, "relative_end": 1748249788.158151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249788.158989, "relative_start": 18.65909695625305, "end": 1748249788.158989, "relative_end": 1748249788.158989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249788.15982, "relative_start": 18.6599280834198, "end": 1748249788.15982, "relative_end": 1748249788.15982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "start": 1748249788.160611, "relative_start": 18.66071891784668, "end": 1748249788.160611, "relative_end": 1748249788.160611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": 1748249788.185214, "relative_start": 18.685322046279907, "end": 1748249788.185214, "relative_end": 1748249788.185214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app", "start": 1748249788.302978, "relative_start": 18.803086042404175, "end": 1748249788.302978, "relative_end": 1748249788.302978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::main", "start": 1748249788.626738, "relative_start": 19.126846075057983, "end": 1748249788.626738, "relative_end": 1748249788.626738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app.sidebar", "start": 1748249788.696534, "relative_start": 19.19664192199707, "end": 1748249788.696534, "relative_end": 1748249788.696534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.head", "start": 1748249796.985183, "relative_start": 27.485291004180908, "end": 1748249796.985183, "relative_end": 1748249796.985183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle", "start": 1748249797.03968, "relative_start": 27.539788007736206, "end": 1748249797.03968, "relative_end": 1748249797.03968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": 1748249797.286575, "relative_start": 27.786683082580566, "end": 1748249797.286575, "relative_end": 1748249797.286575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249797.288265, "relative_start": 27.78837299346924, "end": 1748249797.288265, "relative_end": 1748249797.288265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "start": 1748249797.288926, "relative_start": 27.789033889770508, "end": 1748249797.288926, "relative_end": 1748249797.288926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249797.289481, "relative_start": 27.789588928222656, "end": 1748249797.289481, "relative_end": 1748249797.289481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249797.29013, "relative_start": 27.79023790359497, "end": 1748249797.29013, "relative_end": 1748249797.29013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.app-logo", "start": 1748249797.291972, "relative_start": 27.79207992553711, "end": 1748249797.291972, "relative_end": 1748249797.291972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249797.339429, "relative_start": 27.839536905288696, "end": 1748249797.339429, "relative_end": 1748249797.339429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249797.944035, "relative_start": 28.444143056869507, "end": 1748249797.944035, "relative_end": 1748249797.944035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.layout-dashboard", "start": 1748249797.945236, "relative_start": 28.44534397125244, "end": 1748249797.945236, "relative_end": 1748249797.945236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249798.00287, "relative_start": 28.502978086471558, "end": 1748249798.00287, "relative_end": 1748249798.00287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249798.004026, "relative_start": 28.504133939743042, "end": 1748249798.004026, "relative_end": 1748249798.004026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249798.005435, "relative_start": 28.505542993545532, "end": 1748249798.005435, "relative_end": 1748249798.005435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.user", "start": 1748249798.006499, "relative_start": 28.506607055664062, "end": 1748249798.006499, "relative_end": 1748249798.006499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249798.108454, "relative_start": 28.608561992645264, "end": 1748249798.108454, "relative_end": 1748249798.108454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249798.1097, "relative_start": 28.60980796813965, "end": 1748249798.1097, "relative_end": 1748249798.1097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249798.110724, "relative_start": 28.61083197593689, "end": 1748249798.110724, "relative_end": 1748249798.110724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.currency-dollar", "start": 1748249798.111915, "relative_start": 28.61202311515808, "end": 1748249798.111915, "relative_end": 1748249798.111915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249798.215269, "relative_start": 28.71537709236145, "end": 1748249798.215269, "relative_end": 1748249798.215269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249798.216236, "relative_start": 28.716344118118286, "end": 1748249798.216236, "relative_end": 1748249798.216236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249798.217248, "relative_start": 28.717355966567993, "end": 1748249798.217248, "relative_end": 1748249798.217248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.scroll-text", "start": 1748249798.21795, "relative_start": 28.718058109283447, "end": 1748249798.21795, "relative_end": 1748249798.21795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249798.277816, "relative_start": 28.777924060821533, "end": 1748249798.277816, "relative_end": 1748249798.277816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249798.280149, "relative_start": 28.780256986618042, "end": 1748249798.280149, "relative_end": 1748249798.280149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249798.281363, "relative_start": 28.781471014022827, "end": 1748249798.281363, "relative_end": 1748249798.281363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.queue-list", "start": 1748249798.282359, "relative_start": 28.782466888427734, "end": 1748249798.282359, "relative_end": 1748249798.282359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249798.387574, "relative_start": 28.88768196105957, "end": 1748249798.387574, "relative_end": 1748249798.387574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249798.388547, "relative_start": 28.888654947280884, "end": 1748249798.388547, "relative_end": 1748249798.388547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249798.389534, "relative_start": 28.889642000198364, "end": 1748249798.389534, "relative_end": 1748249798.389534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.tags", "start": 1748249798.390256, "relative_start": 28.890363931655884, "end": 1748249798.390256, "relative_end": 1748249798.390256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249798.453517, "relative_start": 28.953624963760376, "end": 1748249798.453517, "relative_end": 1748249798.453517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group", "start": 1748249798.454406, "relative_start": 28.954514026641846, "end": 1748249798.454406, "relative_end": 1748249798.454406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249798.80106, "relative_start": 29.301167964935303, "end": 1748249798.80106, "relative_end": 1748249798.80106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249798.803158, "relative_start": 29.303266048431396, "end": 1748249798.803158, "relative_end": 1748249798.803158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.shield-check", "start": 1748249798.80576, "relative_start": 29.305867910385132, "end": 1748249798.80576, "relative_end": 1748249798.80576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249798.950762, "relative_start": 29.450870037078857, "end": 1748249798.950762, "relative_end": 1748249798.950762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249798.952725, "relative_start": 29.4528329372406, "end": 1748249798.952725, "relative_end": 1748249798.952725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249798.956188, "relative_start": 29.45629596710205, "end": 1748249798.956188, "relative_end": 1748249798.956188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.key", "start": 1748249798.958199, "relative_start": 29.458307027816772, "end": 1748249798.958199, "relative_end": 1748249798.958199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249799.14127, "relative_start": 29.641377925872803, "end": 1748249799.14127, "relative_end": 1748249799.14127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249799.143298, "relative_start": 29.64340591430664, "end": 1748249799.143298, "relative_end": 1748249799.143298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249799.148029, "relative_start": 29.648137092590332, "end": 1748249799.148029, "relative_end": 1748249799.148029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.users", "start": 1748249799.149902, "relative_start": 29.650010108947754, "end": 1748249799.149902, "relative_end": 1748249799.149902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249799.332152, "relative_start": 29.83225989341736, "end": 1748249799.332152, "relative_end": 1748249799.332152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group", "start": 1748249799.332857, "relative_start": 29.83296489715576, "end": 1748249799.332857, "relative_end": 1748249799.332857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-down", "start": 1748249799.333604, "relative_start": 29.833712100982666, "end": 1748249799.333604, "relative_end": 1748249799.333604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-right", "start": 1748249799.444312, "relative_start": 29.944420099258423, "end": 1748249799.444312, "relative_end": 1748249799.444312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index", "start": 1748249799.539209, "relative_start": 30.0393168926239, "end": 1748249799.539209, "relative_end": 1748249799.539209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": 1748249799.599667, "relative_start": 30.099775075912476, "end": 1748249799.599667, "relative_end": 1748249799.599667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "start": 1748249799.600457, "relative_start": 30.10056495666504, "end": 1748249799.600457, "relative_end": 1748249799.600457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249799.601958, "relative_start": 30.102066040039062, "end": 1748249799.601958, "relative_end": 1748249799.601958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.folder-git-2", "start": 1748249799.602999, "relative_start": 30.10310697555542, "end": 1748249799.602999, "relative_end": 1748249799.602999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249799.662622, "relative_start": 30.1627299785614, "end": 1748249799.662622, "relative_end": 1748249799.662622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index", "start": 1748249799.663919, "relative_start": 30.164026975631714, "end": 1748249799.663919, "relative_end": 1748249799.663919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::profile", "start": 1748249799.66475, "relative_start": 30.164858102798462, "end": 1748249799.66475, "relative_end": 1748249799.66475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index", "start": 1748249800.011082, "relative_start": 30.511189937591553, "end": 1748249800.011082, "relative_end": 1748249800.011082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": 1748249800.507399, "relative_start": 31.00750708580017, "end": 1748249800.507399, "relative_end": 1748249800.507399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": 1748249800.508219, "relative_start": 31.0083270072937, "end": 1748249800.508219, "relative_end": 1748249800.508219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249800.509088, "relative_start": 31.009196043014526, "end": 1748249800.509088, "relative_end": 1748249800.509088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down", "start": 1748249800.509972, "relative_start": 31.010080099105835, "end": 1748249800.509972, "relative_end": 1748249800.509972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "start": 1748249800.587321, "relative_start": 31.08742904663086, "end": 1748249800.587321, "relative_end": 1748249800.587321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": 1748249800.623696, "relative_start": 31.123804092407227, "end": 1748249800.623696, "relative_end": 1748249800.623696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": 1748249800.807461, "relative_start": 31.30756902694702, "end": 1748249800.807461, "relative_end": 1748249800.807461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": 1748249800.899854, "relative_start": 31.399961948394775, "end": 1748249800.899854, "relative_end": 1748249800.899854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": 1748249801.115976, "relative_start": 31.616084098815918, "end": 1748249801.115976, "relative_end": 1748249801.115976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249801.521129, "relative_start": 32.02123689651489, "end": 1748249801.521129, "relative_end": 1748249801.521129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun", "start": 1748249801.522367, "relative_start": 32.02247500419617, "end": 1748249801.522367, "relative_end": 1748249801.522367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": 1748249801.665731, "relative_start": 32.165838956832886, "end": 1748249801.665731, "relative_end": 1748249801.665731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": 1748249801.666945, "relative_start": 32.16705298423767, "end": 1748249801.666945, "relative_end": 1748249801.666945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249801.667885, "relative_start": 32.16799306869507, "end": 1748249801.667885, "relative_end": 1748249801.667885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon", "start": 1748249801.668934, "relative_start": 32.169042110443115, "end": 1748249801.668934, "relative_end": 1748249801.668934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": 1748249801.80389, "relative_start": 32.30399799346924, "end": 1748249801.80389, "relative_end": 1748249801.80389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": 1748249801.805096, "relative_start": 32.305203914642334, "end": 1748249801.805096, "relative_end": 1748249801.805096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": 1748249801.806906, "relative_start": 32.30701398849487, "end": 1748249801.806906, "relative_end": 1748249801.806906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop", "start": 1748249801.808639, "relative_start": 32.30874705314636, "end": 1748249801.808639, "relative_end": 1748249801.808639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index", "start": 1748249802.041618, "relative_start": 32.54172611236572, "end": 1748249802.041618, "relative_end": 1748249802.041618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented", "start": 1748249802.340078, "relative_start": 32.84018611907959, "end": 1748249802.340078, "relative_end": 1748249802.340078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": 1748249802.556147, "relative_start": 33.05625510215759, "end": 1748249802.556147, "relative_end": 1748249802.556147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": 1748249802.557546, "relative_start": 33.05765390396118, "end": 1748249802.557546, "relative_end": 1748249802.557546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": 1748249802.558, "relative_start": 33.05810809135437, "end": 1748249802.558, "relative_end": 1748249802.558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "start": 1748249802.558817, "relative_start": 33.05892491340637, "end": 1748249802.558817, "relative_end": 1748249802.558817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.016819, "relative_start": 33.516927003860474, "end": **********.016819, "relative_end": **********.016819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog", "start": **********.018238, "relative_start": 33.518346071243286, "end": **********.018238, "relative_end": **********.018238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.122353, "relative_start": 33.62246108055115, "end": **********.122353, "relative_end": **********.122353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "start": **********.123427, "relative_start": 33.62353491783142, "end": **********.123427, "relative_end": **********.123427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": **********.123849, "relative_start": 33.62395691871643, "end": **********.123849, "relative_end": **********.123849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": **********.124313, "relative_start": 33.62442111968994, "end": **********.124313, "relative_end": **********.124313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "start": **********.125143, "relative_start": 33.625251054763794, "end": **********.125143, "relative_end": **********.125143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.126145, "relative_start": 33.62625288963318, "end": **********.126145, "relative_end": **********.126145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle", "start": **********.127346, "relative_start": 33.62745404243469, "end": **********.127346, "relative_end": **********.127346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.234215, "relative_start": 33.734323024749756, "end": **********.234215, "relative_end": **********.234215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.index", "start": **********.234988, "relative_start": 33.7350959777832, "end": **********.234988, "relative_end": **********.234988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::dropdown", "start": **********.281349, "relative_start": 33.78145694732666, "end": **********.281349, "relative_end": **********.281349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.index", "start": **********.33452, "relative_start": 33.834628105163574, "end": **********.33452, "relative_end": **********.33452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.backdrop", "start": **********.54991, "relative_start": 34.050018072128296, "end": **********.54991, "relative_end": **********.54991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle", "start": **********.580305, "relative_start": 34.08041310310364, "end": **********.580305, "relative_end": **********.580305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "start": **********.580994, "relative_start": 34.08110189437866, "end": **********.580994, "relative_end": **********.580994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.582476, "relative_start": 34.08258390426636, "end": **********.582476, "relative_end": **********.582476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.panel-left", "start": **********.583308, "relative_start": 34.08341598510742, "end": **********.583308, "relative_end": **********.583308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.649415, "relative_start": 34.14952301979065, "end": **********.649415, "relative_end": **********.649415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.650059, "relative_start": 34.1501669883728, "end": **********.650059, "relative_end": **********.650059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "start": **********.650665, "relative_start": 34.15077304840088, "end": **********.650665, "relative_end": **********.650665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::profile", "start": **********.651135, "relative_start": 34.15124297142029, "end": **********.651135, "relative_end": **********.651135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index", "start": **********.651864, "relative_start": 34.15197205543518, "end": **********.651864, "relative_end": **********.651864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.653003, "relative_start": 34.15311098098755, "end": **********.653003, "relative_end": **********.653003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "start": **********.653563, "relative_start": 34.15367102622986, "end": **********.653563, "relative_end": **********.653563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.654123, "relative_start": 34.15423107147217, "end": **********.654123, "relative_end": **********.654123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down", "start": **********.654624, "relative_start": 34.15473198890686, "end": **********.654624, "relative_end": **********.654624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "start": **********.655762, "relative_start": 34.15586996078491, "end": **********.655762, "relative_end": **********.655762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": **********.656181, "relative_start": 34.15628910064697, "end": **********.656181, "relative_end": **********.656181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": **********.656622, "relative_start": 34.15672993659973, "end": **********.656622, "relative_end": **********.656622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": **********.657209, "relative_start": 34.15731692314148, "end": **********.657209, "relative_end": **********.657209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": **********.657753, "relative_start": 34.15786099433899, "end": **********.657753, "relative_end": **********.657753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.658461, "relative_start": 34.15856909751892, "end": **********.658461, "relative_end": **********.658461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun", "start": **********.658979, "relative_start": 34.15908694267273, "end": **********.658979, "relative_end": **********.658979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": **********.659495, "relative_start": 34.159603118896484, "end": **********.659495, "relative_end": **********.659495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": **********.66004, "relative_start": 34.16014790534973, "end": **********.66004, "relative_end": **********.66004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.661466, "relative_start": 34.16157388687134, "end": **********.661466, "relative_end": **********.661466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon", "start": **********.662322, "relative_start": 34.16243004798889, "end": **********.662322, "relative_end": **********.662322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "start": **********.662917, "relative_start": 34.16302490234375, "end": **********.662917, "relative_end": **********.662917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "start": **********.663533, "relative_start": 34.16364097595215, "end": **********.663533, "relative_end": **********.663533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.664313, "relative_start": 34.16442108154297, "end": **********.664313, "relative_end": **********.664313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop", "start": **********.664846, "relative_start": 34.16495394706726, "end": **********.664846, "relative_end": **********.664846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index", "start": **********.665389, "relative_start": 34.165497064590454, "end": **********.665389, "relative_end": **********.665389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented", "start": **********.665931, "relative_start": 34.16603899002075, "end": **********.665931, "relative_end": **********.665931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "start": **********.666535, "relative_start": 34.166642904281616, "end": **********.666535, "relative_end": **********.666535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": **********.667473, "relative_start": 34.16758108139038, "end": **********.667473, "relative_end": **********.667473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": **********.667909, "relative_start": 34.16801691055298, "end": **********.667909, "relative_end": **********.667909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "start": **********.668479, "relative_start": 34.16858696937561, "end": **********.668479, "relative_end": **********.668479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.669382, "relative_start": 34.16949009895325, "end": **********.669382, "relative_end": **********.669382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog", "start": **********.669923, "relative_start": 34.17003107070923, "end": **********.669923, "relative_end": **********.669923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.670444, "relative_start": 34.170552015304565, "end": **********.670444, "relative_end": **********.670444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "start": **********.671198, "relative_start": 34.171305894851685, "end": **********.671198, "relative_end": **********.671198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "start": **********.671585, "relative_start": 34.171693086624146, "end": **********.671585, "relative_end": **********.671585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "start": **********.672035, "relative_start": 34.17214298248291, "end": **********.672035, "relative_end": **********.672035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "start": **********.67271, "relative_start": 34.17281794548035, "end": **********.67271, "relative_end": **********.67271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "start": **********.673681, "relative_start": 34.17378902435303, "end": **********.673681, "relative_end": **********.673681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle", "start": **********.674273, "relative_start": 34.17438101768494, "end": **********.674273, "relative_end": **********.674273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "start": **********.674817, "relative_start": 34.174925088882446, "end": **********.674817, "relative_end": **********.674817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::menu.index", "start": **********.675436, "relative_start": 34.175544023513794, "end": **********.675436, "relative_end": **********.675436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::dropdown", "start": **********.675847, "relative_start": 34.175955057144165, "end": **********.675847, "relative_end": **********.675847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::header", "start": **********.676404, "relative_start": 34.176512002944946, "end": **********.676404, "relative_end": **********.676404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: e60dd9d2c3a62d619c9acb38f20d5aa5::toast.index", "start": **********.747343, "relative_start": 34.247451066970825, "end": **********.747343, "relative_end": **********.747343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.830696, "relative_start": 34.330804109573364, "end": **********.830987, "relative_end": **********.830987, "duration": 0.00029087066650390625, "duration_str": "291μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 34032184, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.3.3", "Environment": "local", "Debug Mode": "Enabled", "URL": "laravel-app.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 740, "nb_templates": 740, "templates": [{"name": "1x livewire.category-table", "param_count": null, "params": [], "start": **********.479281, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/category-table.blade.phplivewire.category-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fcategory-table.blade.php&line=1", "ajax": false, "filename": "category-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.category-table"}, {"name": "17x e60dd9d2c3a62d619c9acb38f20d5aa5::heading", "param_count": null, "params": [], "start": **********.107296, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/heading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 17, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::heading"}, {"name": "82x e60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "param_count": null, "params": [], "start": **********.260675, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 82, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button.index"}, {"name": "89x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "param_count": null, "params": [], "start": 1748249776.409658, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 89, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "param_count": null, "params": [], "start": 1748249776.761516, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/plus.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fplus.blade.php&line=1", "ajax": false, "filename": "plus.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.plus"}, {"name": "98x e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "param_count": null, "params": [], "start": 1748249776.881555, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/button-or-link.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fbutton-or-link.blade.php&line=1", "ajax": false, "filename": "button-or-link.blade.php", "line": "?"}, "render_count": 98, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::button-or-link"}, {"name": "84x e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "param_count": null, "params": [], "start": 1748249776.9746, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-tooltip.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-tooltip.blade.php&line=1", "ajax": false, "filename": "with-tooltip.blade.php", "line": "?"}, "render_count": 84, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-tooltip"}, {"name": "31x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "param_count": null, "params": [], "start": 1748249777.235633, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/trigger.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Ftrigger.blade.php&line=1", "ajax": false, "filename": "trigger.blade.php", "line": "?"}, "render_count": 31, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.trigger"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "param_count": null, "params": [], "start": 1748249777.284967, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "param_count": null, "params": [], "start": 1748249779.371123, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/magnifying-glass.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmagnifying-glass.blade.php&line=1", "ajax": false, "filename": "magnifying-glass.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.magnifying-glass"}, {"name": "18x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "param_count": null, "params": [], "start": 1748249779.501631, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/loading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 18, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.loading"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "param_count": null, "params": [], "start": 1748249779.614936, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/input/clearable.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Finput%2Fclearable.blade.php&line=1", "ajax": false, "filename": "clearable.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::input.clearable"}, {"name": "18x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "param_count": null, "params": [], "start": 1748249779.897199, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/x-mark.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fx-mark.blade.php&line=1", "ajax": false, "filename": "x-mark.blade.php", "line": "?"}, "render_count": 18, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.x-mark"}, {"name": "4x e60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "param_count": null, "params": [], "start": 1748249780.002677, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/with-field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::with-field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fwith-field.blade.php&line=1", "ajax": false, "filename": "with-field.blade.php", "line": "?"}, "render_count": 4, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::with-field"}, {"name": "3x components.table.column", "param_count": null, "params": [], "start": 1748249781.070551, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/column.blade.phpcomponents.table.column", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}, "render_count": 3, "name_original": "components.table.column"}, {"name": "1x components.table.columns", "param_count": null, "params": [], "start": 1748249781.239946, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/columns.blade.phpcomponents.table.columns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcolumns.blade.php&line=1", "ajax": false, "filename": "columns.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.columns"}, {"name": "45x components.table.cell", "param_count": null, "params": [], "start": 1748249781.326843, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/cell.blade.phpcomponents.table.cell", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}, "render_count": 45, "name_original": "components.table.cell"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "param_count": null, "params": [], "start": 1748249781.444394, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/pencil-square.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fpencil-square.blade.php&line=1", "ajax": false, "filename": "pencil-square.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.pencil-square"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "param_count": null, "params": [], "start": 1748249781.630121, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/trash.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Ftrash.blade.php&line=1", "ajax": false, "filename": "trash.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.trash"}, {"name": "15x e60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "param_count": null, "params": [], "start": 1748249781.806153, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/subheading.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::subheading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsubheading.blade.php&line=1", "ajax": false, "filename": "subheading.blade.php", "line": "?"}, "render_count": 15, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::subheading"}, {"name": "18x e60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "param_count": null, "params": [], "start": 1748249782.05991, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/spacer.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::spacer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fspacer.blade.php&line=1", "ajax": false, "filename": "spacer.blade.php", "line": "?"}, "render_count": 18, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::spacer"}, {"name": "32x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "param_count": null, "params": [], "start": 1748249782.477214, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/close.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.close", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Fclose.blade.php&line=1", "ajax": false, "filename": "close.blade.php", "line": "?"}, "render_count": 32, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.close"}, {"name": "16x e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "param_count": null, "params": [], "start": 1748249782.692087, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/modal/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 16, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::modal.index"}, {"name": "15x components.table.row", "param_count": null, "params": [], "start": 1748249783.417538, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/row.blade.phpcomponents.table.row", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}, "render_count": 15, "name_original": "components.table.row"}, {"name": "1x components.table.rows", "param_count": null, "params": [], "start": 1748249784.092177, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/rows.blade.phpcomponents.table.rows", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Frows.blade.php&line=1", "ajax": false, "filename": "rows.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.rows"}, {"name": "1x components.table.index", "param_count": null, "params": [], "start": 1748249784.15138, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/table/index.blade.phpcomponents.table.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Ftable%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.table.index"}, {"name": "1x livewire::tailwind", "param_count": null, "params": [], "start": 1748249784.220039, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire::tailwind"}, {"name": "1x components.card", "param_count": null, "params": [], "start": 1748249784.450985, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.card"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "param_count": null, "params": [], "start": 1748249784.770522, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/card/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::card.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::card.index"}, {"name": "1x livewire.category-form", "param_count": null, "params": [], "start": 1748249784.813211, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/livewire/category-form.blade.phplivewire.category-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Flivewire%2Fcategory-form.blade.php&line=1", "ajax": false, "filename": "category-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.category-form"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::label", "param_count": null, "params": [], "start": 1748249787.512579, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/label.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::label"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::error", "param_count": null, "params": [], "start": 1748249787.602243, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/error.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::error"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::field", "param_count": null, "params": [], "start": 1748249787.862639, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/field.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::field", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::field"}, {"name": "10x components.option", "param_count": null, "params": [], "start": 1748249787.940696, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/option.blade.phpcomponents.option", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Foption.blade.php&line=1", "ajax": false, "filename": "option.blade.php", "line": "?"}, "render_count": 10, "name_original": "components.option"}, {"name": "1x components.select", "param_count": null, "params": [], "start": 1748249788.016995, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/select.blade.phpcomponents.select", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.select"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": 1748249788.184159, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x components.layouts.app", "param_count": null, "params": [], "start": 1748249788.302875, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app.blade.phpcomponents.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::main", "param_count": null, "params": [], "start": 1748249788.626671, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/main.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::main", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmain.blade.php&line=1", "ajax": false, "filename": "main.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::main"}, {"name": "1x components.layouts.app.sidebar", "param_count": null, "params": [], "start": 1748249788.696426, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/layouts/app/sidebar.blade.phpcomponents.layouts.app.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app.sidebar"}, {"name": "1x partials.head", "param_count": null, "params": [], "start": 1748249796.985117, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/partials/head.blade.phppartials.head", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fpartials%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.head"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle", "param_count": null, "params": [], "start": 1748249797.039622, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/toggle.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Ftoggle.blade.php&line=1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.toggle"}, {"name": "1x components.app-logo", "param_count": null, "params": [], "start": 1748249797.291907, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/components/app-logo.blade.phpcomponents.app-logo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fcomponents%2Fapp-logo.blade.php&line=1", "ajax": false, "filename": "app-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.app-logo"}, {"name": "10x e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "param_count": null, "params": [], "start": 1748249797.339313, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/item.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 10, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.item"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.layout-dashboard", "param_count": null, "params": [], "start": 1748249797.945086, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/layout-dashboard.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.layout-dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Flayout-dashboard.blade.php&line=1", "ajax": false, "filename": "layout-dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.layout-dashboard"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.user", "param_count": null, "params": [], "start": 1748249798.006435, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/user.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.user", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.user"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.currency-dollar", "param_count": null, "params": [], "start": 1748249798.111756, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/currency-dollar.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.currency-dollar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcurrency-dollar.blade.php&line=1", "ajax": false, "filename": "currency-dollar.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.currency-dollar"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.scroll-text", "param_count": null, "params": [], "start": 1748249798.217894, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/scroll-text.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.scroll-text", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fscroll-text.blade.php&line=1", "ajax": false, "filename": "scroll-text.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.scroll-text"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.queue-list", "param_count": null, "params": [], "start": 1748249798.282287, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/queue-list.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.queue-list", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fqueue-list.blade.php&line=1", "ajax": false, "filename": "queue-list.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.queue-list"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.tags", "param_count": null, "params": [], "start": 1748249798.390199, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/tags.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.tags", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ftags.blade.php&line=1", "ajax": false, "filename": "tags.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.tags"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group", "param_count": null, "params": [], "start": 1748249798.454342, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/navlist/group.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Fnavlist%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.group"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.shield-check", "param_count": null, "params": [], "start": 1748249798.805656, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/shield-check.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.shield-check", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fshield-check.blade.php&line=1", "ajax": false, "filename": "shield-check.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.shield-check"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.key", "param_count": null, "params": [], "start": 1748249798.958086, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/key.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.key", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fkey.blade.php&line=1", "ajax": false, "filename": "key.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.key"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.users", "param_count": null, "params": [], "start": 1748249799.149827, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/users.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.users", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fusers.blade.php&line=1", "ajax": false, "filename": "users.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.users"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-down", "param_count": null, "params": [], "start": 1748249799.333543, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-down.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-down"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-right", "param_count": null, "params": [], "start": 1748249799.444251, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/chevron-right.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-right", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevron-right.blade.php&line=1", "ajax": false, "filename": "chevron-right.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevron-right"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index", "param_count": null, "params": [], "start": 1748249799.539124, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/navlist/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fnavlist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::navlist.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.folder-git-2", "param_count": null, "params": [], "start": 1748249799.60291, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/folder-git-2.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.folder-git-2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Ffolder-git-2.blade.php&line=1", "ajax": false, "filename": "folder-git-2.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.folder-git-2"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::profile", "param_count": null, "params": [], "start": 1748249799.664687, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/profile.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::profile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fprofile.blade.php&line=1", "ajax": false, "filename": "profile.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::profile"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index", "param_count": null, "params": [], "start": 1748249800.010982, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/avatar/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Favatar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::avatar.index"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down", "param_count": null, "params": [], "start": 1748249800.509908, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/chevrons-up-down.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fchevrons-up-down.blade.php&line=1", "ajax": false, "filename": "chevrons-up-down.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.chevrons-up-down"}, {"name": "4x e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "param_count": null, "params": [], "start": 1748249800.587233, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/radio/group.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fradio%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 4, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::menu.radio.group"}, {"name": "6x e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "param_count": null, "params": [], "start": 1748249800.623614, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/separator.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::menu.separator"}, {"name": "6x e60dd9d2c3a62d619c9acb38f20d5aa5::separator", "param_count": null, "params": [], "start": 1748249800.807367, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/separator.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::separator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fseparator.blade.php&line=1", "ajax": false, "filename": "separator.blade.php", "line": "?"}, "render_count": 6, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::separator"}, {"name": "6x e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "param_count": null, "params": [], "start": 1748249800.899759, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::radio.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::radio.index"}, {"name": "6x e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "param_count": null, "params": [], "start": 1748249801.11587, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/variants/segmented.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 6, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::radio.variants.segmented"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun", "param_count": null, "params": [], "start": 1748249801.522285, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/sun.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.sun"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon", "param_count": null, "params": [], "start": 1748249801.668872, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/moon.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.moon"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop", "param_count": null, "params": [], "start": 1748249801.808527, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/computer-desktop.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.computer-desktop"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index", "param_count": null, "params": [], "start": 1748249802.041511, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.index"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented", "param_count": null, "params": [], "start": 1748249802.340008, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/radio/group/variants/segmented.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fradio%2Fgroup%2Fvariants%2Fsegmented.blade.php&line=1", "ajax": false, "filename": "segmented.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::radio.group.variants.segmented"}, {"name": "4x e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "param_count": null, "params": [], "start": 1748249802.558758, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/item.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::menu.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::menu.item"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog", "param_count": null, "params": [], "start": **********.018172, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/cog.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Fcog.blade.php&line=1", "ajax": false, "filename": "cog.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.cog"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle", "param_count": null, "params": [], "start": **********.127226, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/icon/arrow-right-start-on-rectangle.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Ficon%2Farrow-right-start-on-rectangle.blade.php&line=1", "ajax": false, "filename": "arrow-right-start-on-rectangle.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.arrow-right-start-on-rectangle"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::menu.index", "param_count": null, "params": [], "start": **********.234928, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/menu/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::menu.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fmenu%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::menu.index"}, {"name": "2x e60dd9d2c3a62d619c9acb38f20d5aa5::dropdown", "param_count": null, "params": [], "start": **********.281276, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/dropdown.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::dropdown"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.index", "param_count": null, "params": [], "start": **********.334448, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.index"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.backdrop", "param_count": null, "params": [], "start": **********.549841, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/sidebar/backdrop.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.backdrop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fsidebar%2Fbackdrop.blade.php&line=1", "ajax": false, "filename": "backdrop.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::sidebar.backdrop"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::icon.panel-left", "param_count": null, "params": [], "start": **********.583231, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/icon/panel-left.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::icon.panel-left", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ficon%2Fpanel-left.blade.php&line=1", "ajax": false, "filename": "panel-left.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::icon.panel-left"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::header", "param_count": null, "params": [], "start": **********.676343, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\vendor\\livewire\\flux\\src/../stubs/resources/views/flux/header.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flivewire%2Fflux%2Fstubs%2Fresources%2Fviews%2Fflux%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::header"}, {"name": "1x e60dd9d2c3a62d619c9acb38f20d5aa5::toast.index", "param_count": null, "params": [], "start": **********.747244, "type": "blade", "hash": "bladeC:\\laragon\\www\\pure-finance\\resources\\views/flux/toast/index.blade.phpe60dd9d2c3a62d619c9acb38f20d5aa5::toast.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fresources%2Fviews%2Fflux%2Ftoast%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "e60dd9d2c3a62d619c9acb38f20d5aa5::toast.index"}]}, "queries": {"count": 7, "nb_statements": 6, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01034, "accumulated_duration_str": "10.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 131}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 111}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 60}, {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 88}], "start": **********.340186, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CacheManager.php:245", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheManager.php&line=245", "ajax": false, "filename": "CacheManager.php", "line": "245"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn' limit 1", "type": "query", "params": [], "bindings": ["dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.382695, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "daily", "explain": null, "start_percent": 0, "width_percent": 52.515}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.42697, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "daily", "explain": null, "start_percent": 52.515, "width_percent": 6.383}, {"sql": "select count(*) as aggregate from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.451354, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "CategoryTable.php:53", "source": {"index": 19, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryTable.php&line=53", "ajax": false, "filename": "CategoryTable.php", "line": "53"}, "connection": "daily", "explain": null, "start_percent": 58.897, "width_percent": 17.892}, {"sql": "select `id`, `name`, `parent_id` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null order by `name` asc limit 15 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.4591782, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "CategoryTable.php:53", "source": {"index": 19, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryTable.php&line=53", "ajax": false, "filename": "CategoryTable.php", "line": "53"}, "connection": "daily", "explain": null, "start_percent": 76.789, "width_percent": 7.737}, {"sql": "select * from `categories` where `categories`.`id` in (1, 2, 4, 5, 6, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.4654272, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "CategoryTable.php:53", "source": {"index": 24, "namespace": null, "name": "app/Livewire/CategoryTable.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryTable.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryTable.php&line=53", "ajax": false, "filename": "CategoryTable.php", "line": "53"}, "connection": "daily", "explain": null, "start_percent": 84.526, "width_percent": 6.093}, {"sql": "select `id`, `name` from `categories` where `categories`.`user_id` = 1 and `categories`.`user_id` is not null and `parent_id` is null order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/CategoryForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryForm.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/helpers.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\helpers.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportComputed/BaseComputed.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\BaseComputed.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportComputed/BaseComputed.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\BaseComputed.php", "line": 60}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/EventBus.php", "file": "C:\\laragon\\www\\pure-finance\\vendor\\livewire\\livewire\\src\\EventBus.php", "line": 60}], "start": 1748249787.9364252, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "CategoryForm.php:74", "source": {"index": 16, "namespace": null, "name": "app/Livewire/CategoryForm.php", "file": "C:\\laragon\\www\\pure-finance\\app\\Livewire\\CategoryForm.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryForm.php&line=74", "ajax": false, "filename": "CategoryForm.php", "line": "74"}, "connection": "daily", "explain": null, "start_percent": 90.619, "width_percent": 9.381}]}, "models": {"data": {"App\\Models\\Category": {"value": 33, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 34, "is_counter": true}, "livewire": {"data": {"category-table #sXoPLPDxCXA0cheU3pBA": "array:4 [\n  \"data\" => array:2 [\n    \"search\" => \"\"\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"category-table\"\n  \"component\" => \"App\\Livewire\\CategoryTable\"\n  \"id\" => \"sXoPLPDxCXA0cheU3pBA\"\n]", "category-form #yhDnLgyLn7hPtQfFTjQQ": "array:4 [\n  \"data\" => array:4 [\n    \"show_category_form\" => false\n    \"category\" => null\n    \"parent_id\" => null\n    \"name\" => \"\"\n  ]\n  \"name\" => \"category-form\"\n  \"component\" => \"App\\Livewire\\CategoryForm\"\n  \"id\" => \"yhDnLgyLn7hPtQfFTjQQ\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/categories", "action_name": "categories", "controller_action": "App\\Livewire\\CategoryTable", "uri": "GET categories", "controller": "App\\Livewire\\CategoryTable@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryTable.php&line=41\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fpure-finance%2Fapp%2FLivewire%2FCategoryTable.php&line=41\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/CategoryTable.php:41-55</a>", "middleware": "web, auth", "duration": "34.34s", "peak_memory": "34MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1770592786 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1770592786\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1321803625 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1321803625\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-886351343 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/categories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjFqVUQ5WlJDZEhUeXIyM0hLVFg3b3c9PSIsInZhbHVlIjoiUE9iWHpyVkM2TERKMHJFNFdRQ3FhOUoxSlRBQ0tGU2NGZTY1SzJERmxlTk9jU3RraGtwRXBTOTkxS09mODFXVUZpZi8yaFl1RE1IenBFTTFPZGMrUy9uYmM5R3p2Yk1xOGF4cElDRkJuY0pvUWQ5YTZvUHZBS2xXTjIvM3M4WloiLCJtYWMiOiIxYjEyNGU3ZWNkMjY2YjVhMWE2M2JhNzllYzg1NDQyNjAxMDZhODBiZWY3ZTk3ZjAwOTY1NmU4NzFiMDI5MmQyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZUQVA4dzk2TCs3NTE3cXVHb2tHaXc9PSIsInZhbHVlIjoiMTBMQWpCTUd0aWk3Wklya3pOSis1aEVDdGtXczVpTzMxWnRrSFFBSVJqWWlWakl0MERMNk5kQW9CdHNpS1RqbTVCV2wzUGpVQzE1dFVzSWNrSlF2ZFRSbUJ3WUc3LzkvYXFzSlc4RWZvL2VST2NxTXFHMmNiRCtpb3o0TUtjcUQiLCJtYWMiOiIxYjg2YzJkMmVlZGI2ZDI5ZDIzNTI3NThjNTViY2Y3MGY1ZWRmN2NkOTgzMGQ3ZTMxODA2ZjM0ODY2ODUyYjE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-886351343\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-563425031 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dM7vTLb0NYtb7uAl0ZBUK3VVXSi4HeWTNdoPOREn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-563425031\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-57533523 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 26 May 2025 08:56:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57533523\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1311814621 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P371uRddTUUNbmRkgeYWCHMZrHTahYwCpklMlZwH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/categories</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1311814621\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/categories", "action_name": "categories", "controller_action": "App\\Livewire\\CategoryTable"}, "badge": null}}