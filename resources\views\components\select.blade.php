@props([
    'name' => $attributes->whereStartsWith('wire:model')->first(),
    'placeholder' => null,
    'invalid' => null,
    'size' => null,
    'multiple' => false,
])

@php
$invalid ??= ($name && $errors->has($name));

// Use very basic styling to avoid conflicts
$baseClasses = 'block w-full px-3 py-2 text-base border rounded-lg shadow-sm focus:outline-none focus:ring-2';
$colorClasses = $invalid
    ? 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500'
    : 'border-gray-300 text-gray-900 placeholder-gray-400 focus:border-indigo-500 focus:ring-indigo-500';
$sizeClasses = match ($size) {
    'sm' => 'h-8 text-sm',
    'xs' => 'h-6 text-xs',
    default => 'h-10',
};
$multipleClasses = $multiple ? 'min-h-[80px]' : '';

$classes = "{$baseClasses} {$colorClasses} {$sizeClasses} {$multipleClasses}";
@endphp

<select
    {{ $attributes->class($classes) }}
    @if ($invalid) aria-invalid="true" @endif
    @if ($name) name="{{ $name }}" @endif
    @if ($multiple) multiple @endif
    style="background-image: none !important; -webkit-appearance: menulist !important; -moz-appearance: menulist !important; appearance: menulist !important;"
>
    @if ($placeholder && !$multiple)
        <option value="" disabled selected>{{ $placeholder }}</option>
    @endif

    {{ $slot }}
</select>
